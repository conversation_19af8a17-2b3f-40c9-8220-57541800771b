package br.com.pacto.enums;

import br.com.pacto.bean.usuario.UsuarioZW;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;

import static br.com.pacto.enums.Modulo.*;
import static org.junit.Assert.*;

/**
 * Teste unitário para a enum {@link Modulo}.
 *
 * <AUTHOR>
 * @since 08/11/2018
 */
public class ModuloTest {

    private UsuarioZW usuario;

    @Rule
    public ExpectedException expectedException = ExpectedException.none();

    @Before
    public void setUp() {
        usuario = new UsuarioZW();
        usuario.adicionarModulos(GAME_OF_RESULTS, STUDIO, TREINO);
    }

    @Test
    public void deve__Retornar__Sigla__Do__Sistema() {
        assertEquals("ZW", ZILLYON_WEB.getSiglaModulo());
    }

    @Test
    public void deve__Adicionar__Modulos__Corretamente__A__Partir__De__Uma__String__Com__As__Siglas__1() {
        adicionarModulosEValidar("ZW,CRM,FIN");
    }

    @Test
    public void deve__Adicionar__Modulos__Corretamente__A__Partir__De__Uma__String__Com__As__Siglas__2() {
        adicionarModulosEValidar("Zw, cRm ,  fin  ,  ");
    }

    private void adicionarModulosEValidar(String siglasModulos) {
        UsuarioZW usuario = new UsuarioZW();
        usuario.adicionarModulosPorStringSiglas(siglasModulos);

        assertTrue(Modulo.isModuloHabilitadoDadoUmUsuario(usuario, ZILLYON_WEB));
        assertTrue(Modulo.isModuloHabilitadoDadoUmUsuario(usuario, CUSTOMER_RELATIONSHIP_MANAGEMENT));
        assertTrue(Modulo.isModuloHabilitadoDadoUmUsuario(usuario, FINANCEIRO));
        assertTrue(Modulo.isModuloNaoHabilitadoDadoUmUsuario(usuario, GAME_OF_RESULTS));
    }

    @Test
    public void deve__Retornar__True__Quando__Verificar__Modulo__Que__Esta__Habilitado() {
        assertTrue(Modulo.isModuloHabilitadoDadoUmUsuario(usuario, STUDIO));
    }

    @Test
    public void deve__Retornar__Falso__Quando__Verificar__Modulo__Que__Nao__Esta__Habilitado() {
        assertFalse(Modulo.isModuloHabilitadoDadoUmUsuario(usuario, FINANCEIRO));
    }

    @Test
    public void deve__Retornar__True__Quando__Verificar__Modulo__Que__Nao__Esta__Habilitado__Usando__Sobrecarga() {
        assertTrue(Modulo.isModuloNaoHabilitadoDadoUmUsuario(usuario, ZILLYON_WEB));
    }

    @Test
    public void deve__Retornar__False__Quando__Remover__Modulos__E__Verificar__Que__Modulo__Nao__Esta__Mais__Ativo() {
        usuario.removerModulos(GAME_OF_RESULTS, STUDIO);
        assertTrue(Modulo.isModuloNaoHabilitadoDadoUmUsuario(usuario, GAME_OF_RESULTS));
        assertTrue(Modulo.isModuloNaoHabilitadoDadoUmUsuario(usuario, STUDIO));
        assertTrue(Modulo.isModuloHabilitadoDadoUmUsuario(usuario, TREINO));
    }

    @Test
    public void deve__Retornar__False__Quando__Tentar__Adicionar__Modulos__Presentes() {
        assertFalse(usuario.adicionarModulos(GAME_OF_RESULTS, STUDIO, TREINO));
    }

    @Test
    public void deve__Retornar__True__Quando__Tentar__Adicionar__Pelo__Menos__Um__Modulo__Nao__Presente() {
        assertTrue(usuario.adicionarModulos(GAME_OF_RESULTS, STUDIO, TREINO, ZILLYON_WEB));
    }

    @Test
    public void deve__Retornar__False__Quando__Tentar__Remover__Modulos__Nao__Presentes() {
        assertFalse(usuario.removerModulos(ZILLYON_WEB, FINANCEIRO));
    }

    @Test
    public void deve__Retornar__True__Quando__Tentar__Remover__Pelo__Menos__Um__Modulo__Presente() {
        assertTrue(usuario.removerModulos(ZILLYON_WEB, FINANCEIRO, GAME_OF_RESULTS));
    }

    @Test
    public void deve__Retornar__False__Quando__Tentar__Remover__Modulo__E__Colecao__Estiver__Vazia() {
        usuario.removerModulos(GAME_OF_RESULTS, STUDIO, TREINO);
        assertFalse(usuario.removerModulos(GAME_OF_RESULTS));
    }

    @Test
    public void deve__Retornar__Enum__Do__Modulo__Quando__Informada__Sigla__Valida__1() {
        assertNotNull(Modulo.fromSigla("SLC"));
    }

    @Test
    public void deve__Retornar__Enum__Do__Modulo__Quando__Informada__Sigla__Valida__2() {
        assertNotNull(Modulo.fromSigla(" slC "));
    }

    @Test
    public void deve__Retornar__False__Quando__Colecao__Estiver__Vazia__E__Tentar__Verificar__Modulo__Habilitado() {
        usuario.removerModulos(GAME_OF_RESULTS, STUDIO, TREINO);
        assertFalse(Modulo.isModuloHabilitadoDadoUmUsuario(usuario, GAME_OF_RESULTS));
    }

    @Test
    public void deve__Lancar__Exception__Personalizada__Quando__Informada__Sigla__Invalida() {
        final String SIGLA_MODULO_INVALIDA = " XXxXX ";
        final String EXPECTED_MESSAGE = "Não foi possível recuperar o módulo através da sigla informada: (" + SIGLA_MODULO_INVALIDA + ")";

        expectedException.expect(ModuloNaoEncontradoException.class);
        expectedException.expectMessage(EXPECTED_MESSAGE);

        assertNull(Modulo.fromSigla(SIGLA_MODULO_INVALIDA));
    }

}