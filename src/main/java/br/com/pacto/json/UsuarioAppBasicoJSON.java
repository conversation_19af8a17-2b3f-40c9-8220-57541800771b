/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.json;

import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR>
 */
public class UsuarioAppBasicoJSON extends SuperJSON {

    @ApiModelProperty(value = "Código identificador do cliente no sistema", example = "12345")
    private Integer codigocliente;

    @ApiModelProperty(value = "Código identificador do colaborador no sistema", example = "67890")
    private Integer codigocolaborador;

    @ApiModelProperty(value = "Nome completo do usuário", example = "<PERSON>")
    private String nome;

    @ApiModelProperty(value = "Endereço de email do usuário", example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "Número de celular do usuário", example = "11987654321")
    private String celular;

    @ApiModelProperty(value = "Código identificador do usuário móvel", example = "98765")
    private Integer codigousuariomovel;

    @ApiModelProperty(value = "Nome do usuário no aplicativo móvel", example = "joaosilva")
    private String nomeusuariomovel;

    @ApiModelProperty(value = "Status de ativação do usuário móvel", example = "true")
    private Boolean statususuariomovel;

    @ApiModelProperty(value = "URL da foto de perfil do usuário", example = "https://exemplo.com/fotos/usuario123.jpg")
    private String urlfoto;

    @ApiModelProperty(value = "Matrícula do usuário no sistema", example = "MAT2024001")
    private String matricula;

    @ApiModelProperty(value = "Nome do usuário no sistema", example = "João Silva")
    private String nomeusuario;

    @ApiModelProperty(value = "Username de login do usuário", example = "joao.silva")
    private String usernameusuario;

    @ApiModelProperty(value = "Código identificador do usuário", example = "54321")
    private Integer codusuario;

    @ApiModelProperty(value = "Código identificador do usuário no sistema de treino", example = "11223")
    private Integer codigousuariotreino;

    public UsuarioAppBasicoJSON() {
    }

    public Integer getCodigocliente() {
        return codigocliente;
    }

    public void setCodigocliente(Integer codigocliente) {
        this.codigocliente = codigocliente;
    }

    public Integer getCodigocolaborador() {
        return codigocolaborador;
    }

    public void setCodigocolaborador(Integer codigocolaborador) {
        this.codigocolaborador = codigocolaborador;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCelular() {
        return celular;
    }

    public void setCelular(String celular) {
        this.celular = celular;
    }

    public Integer getCodigousuariomovel() {
        return codigousuariomovel;
    }

    public void setCodigousuariomovel(Integer codigousuariomovel) {
        this.codigousuariomovel = codigousuariomovel;
    }

    public String getNomeusuario() {
        return nomeusuario;
    }

    public String getUsernameusuario() {
        return usernameusuario;
    }

    public void setUsernameusuario(String usernameusuario) {
        this.usernameusuario = usernameusuario;
    }

    public void setNomeusuario(String nomeusuario) {
        this.nomeusuario = nomeusuario;
    }

    public Integer getCodusuario() {
        return codusuario;
    }

    public void setCodusuario(Integer codusuario) {
        this.codusuario = codusuario;
    }

    public String getNomeusuariomovel() {
        return nomeusuariomovel;
    }

    public void setNomeusuariomovel(String nomeusuariomovel) {
        this.nomeusuariomovel = nomeusuariomovel;
    }

    public Boolean getStatususuariomovel() {
        return statususuariomovel;
    }

    public void setStatususuariomovel(Boolean statususuariomovel) {
        this.statususuariomovel = statususuariomovel;
    }

    public String getUrlfoto() {
        return urlfoto;
    }

    public void setUrlfoto(String urlfoto) {
        this.urlfoto = urlfoto;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public Integer getCodigousuariotreino() {
        return codigousuariotreino;
    }

    public void setCodigousuariotreino(Integer codigousuariotreino) {
        this.codigousuariotreino = codigousuariotreino;
    }
}
