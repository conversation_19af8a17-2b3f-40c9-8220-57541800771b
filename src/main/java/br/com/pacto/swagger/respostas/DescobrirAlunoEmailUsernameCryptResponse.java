package br.com.pacto.swagger.respostas;

import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo de resposta para o endpoint descobrirAlunoEmailUsernameCrypt.
 * Representa a estrutura de retorno com dados criptografados de usuários encontrados por email.
 */
public class DescobrirAlunoEmailUsernameCryptResponse {

    @ApiModelProperty(value = "Dados criptografados contendo a lista de usuários encontrados pelo email informado. " +
            "Os dados são criptografados usando algoritmo AES e contêm informações básicas dos usuários como " +
            "código do cliente, nome, email, celular, matrícula e outros identificadores do sistema.",
            example = "U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K1Y96Qsv2Lm+31cmzaAILwyt")
    private String returnValue;

    public DescobrirAlunoEmailUsernameCryptResponse() {
    }

    public String getReturnValue() {
        return returnValue;
    }

    public void setReturnValue(String returnValue) {
        this.returnValue = returnValue;
    }
}
