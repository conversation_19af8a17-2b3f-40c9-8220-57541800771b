package br.com.pacto.swagger.respostas;

import io.swagger.annotations.ApiModelProperty;

/**
 * Classe base para exemplos de respostas paginadas.
 * 
 * Esta classe deve ser estendida por outras classes de exemplo de resposta
 * que representem endpoints que retornam dados paginados. Contém os atributos
 * básicos de paginação utilizados no sistema.
 * 
 * <AUTHOR> Pacto
 * @version 1.0
 */
public abstract class ExemploPaginadorDTO {

    @ApiModelProperty(value = "Número total de elementos encontrados na consulta, independente da paginação", 
                     example = "150")
    private Integer total;

    @ApiModelProperty(value = "Número da página atual da consulta (baseado em 1)", 
                     example = "1")
    private Integer page;

    @ApiModelProperty(value = "Quantidade de elementos por página", 
                     example = "10")
    private Integer size;

    @ApiModelProperty(value = "Número total de páginas disponíveis", 
                     example = "15")
    private Integer totalPages;

    @ApiModelProperty(value = "Indica se é a primeira página", 
                     example = "true")
    private Boolean first;

    @ApiModelProperty(value = "Indica se é a última página", 
                     example = "false")
    private Boolean last;

    @ApiModelProperty(value = "Número de elementos na página atual", 
                     example = "10")
    private Integer numberOfElements;

    public ExemploPaginadorDTO() {
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public Integer getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(Integer totalPages) {
        this.totalPages = totalPages;
    }

    public Boolean getFirst() {
        return first;
    }

    public void setFirst(Boolean first) {
        this.first = first;
    }

    public Boolean getLast() {
        return last;
    }

    public void setLast(Boolean last) {
        this.last = last;
    }

    public Integer getNumberOfElements() {
        return numberOfElements;
    }

    public void setNumberOfElements(Integer numberOfElements) {
        this.numberOfElements = numberOfElements;
    }
}
