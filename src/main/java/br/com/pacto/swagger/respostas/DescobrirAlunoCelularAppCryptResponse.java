package br.com.pacto.swagger.respostas;

import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo de resposta para o endpoint descobrirAlunoCelularAppCrypt.
 * Representa a estrutura de retorno com dados criptografados de usuários encontrados por celular.
 */
public class DescobrirAlunoCelularAppCryptResponse {

    @ApiModelProperty(value = "Dados criptografados contendo a lista de usuários encontrados pelo número de celular informado. " +
            "Os dados são criptografados usando algoritmo AES e contêm informações básicas dos usuários como " +
            "código do cliente, código do colaborador, nome, email, celular, matrícula, dados do usuário móvel " +
            "e outros identificadores do sistema. A busca considera o DDI, DDD e número do celular para localizar " +
            "usuários tanto no sistema principal quanto no sistema de treino independente, dependendo da configuração da empresa.",
            example = "U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K1Y96Qsv2Lm+31cmzaAILwytKjH8vN2pQ5rT9xW1mF3kLpQ8sR7nE2vB4yC6zX9mA1sD")
    private String returnValue;

    public DescobrirAlunoCelularAppCryptResponse() {
    }

    public String getReturnValue() {
        return returnValue;
    }

    public void setReturnValue(String returnValue) {
        this.returnValue = returnValue;
    }
}
