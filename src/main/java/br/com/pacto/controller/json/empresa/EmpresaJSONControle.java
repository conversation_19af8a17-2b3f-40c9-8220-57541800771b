/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.empresa;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.empresa.EmpresaSite;
import br.com.pacto.bean.empresa.InfoInfraEnum;
import br.com.pacto.bean.empresafinanceiro.EmpresaFinanceiro;
import br.com.pacto.bean.feed.usuarioapp.UsuarioApp;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.dto.*;
import br.com.pacto.controller.json.dto.EmpresaDTO;
import br.com.pacto.swagger.respostas.DescobrirAlunoEmailUsernameCryptResponse;
import br.com.pacto.swagger.respostas.DescobrirAlunoCelularAppCryptResponse;
import br.com.pacto.email.UteisEmail;
import br.com.pacto.json.UsuarioAppBasicoJSON;
import br.com.pacto.objeto.HttpRequestUtil;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.empresafinanceiro.EmpresaFinanceiroServiceImpl;
import br.com.pacto.service.intf.clima.ClimaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.empresa.EmpresaSiteService;
import br.com.pacto.service.intf.empresafinanceiro.EmpresaFinanceiroService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.service.intf.usuarioapp.UsuarioAppService;
import br.com.pacto.servlet.Propagador;
import br.com.pacto.util.ExecuteRequestHttpService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.server.Constants;
import java.sql.ResultSet;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;




/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/empresa")
public class EmpresaJSONControle extends SuperControle {

    @Autowired
    private EmpresaService service;
    @Autowired
    private EmpresaFinanceiroService empFinanceiro;
    @Autowired
    private EmpresaSiteService serviceSite;
    @Autowired
    private UsuarioAppService userAppService;
    @Autowired
    private HttpServletRequest request;
    @Autowired
    private ServletContext sc;
    @Autowired
    private ClimaService climaService;
    @Autowired
    private UsuarioService usuarioService;

    @RequestMapping(value = "{ctx}/descobrir", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap empresa(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            Empresa emp = service.descobrir(ctx);
            mm.addAttribute(RETURN, emp.getUrlTreinoMobile());
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Uteis.logarDebug(String.format("Erro em {%s/descobrir} %s", ctx, ex.getMessage()));
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/descobrirZW", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap descobrirZW(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            Empresa emp = service.descobrir(ctx);
            mm.addAttribute(RETURN, emp.getRoboControleCorrigindoProtocolo());
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "/descobrirUsuario", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap usuario(@RequestParam String email) {
        ModelMap mm = new ModelMap();
        try {
            if (email == null || email.isEmpty()) {
                throw new ServiceException("E-mail não informado");
            }
            UsuarioApp usuario = userAppService.obterPorEmail(email.toLowerCase());
            if (usuario == null
                    || usuario.getEmail() == null
                    || usuario.getEmail().isEmpty()) {
                throw new ServiceException("E-mail não encontrado");
            }
            JSONObject json = new JSONObject();
            json.put("url", usuario.getEmpresa().getUrlTreinoMobile());
            json.put("chave", usuario.getEmpresa().getChave());
            mm.addAttribute(RETURN, json.toString());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/inserirUsuario", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap inserirUsuario(@PathVariable String ctx, @RequestParam String email) {
        ModelMap mm = new ModelMap();
        try {
            UsuarioApp usuario = userAppService.gerarUsuario(ctx, email, "", "", "", "");
            JSONObject json = new JSONObject();
            json.put("url", usuario.getEmpresa().getUrlTreinoMobile());
            json.put("chave", usuario.getEmpresa().getChave());
            mm.addAttribute(RETURN, json.toString());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/v3/inserirUsuario", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap inserirUsuarioV3(@PathVariable String ctx,
                              @RequestParam String email,
                              @RequestParam(required = false) String cpf,
                              @RequestParam(required = false) String telefone,
                              @RequestParam(required = false) String dataNascimento,
                              @RequestParam(required = false) String senha) {
        ModelMap mm = new ModelMap();
        try {
            UsuarioApp usuario = userAppService.gerarUsuario(ctx, email, cpf, telefone, dataNascimento, senha);
            JSONObject json = new JSONObject();
            json.put("url", usuario.getEmpresa().getUrlTreinoMobile());
            json.put("chave", usuario.getEmpresa().getChave());
            mm.addAttribute(RETURN, json.toString());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/syncPhotos", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap syncPhotos(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            Empresa emp = service.descobrir(ctx);
            emp.preencherLogo(true, sc.getRealPath("imagens"));
            Propagador.propagar(request);
            mm.addAttribute(STATUS_SUCESSO, "Fotos sincronizadas!");
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "consultarInfosClima", method = {RequestMethod.GET})
    public @ResponseBody
    ModelMap consultarInfosClima(@RequestParam String chave, @RequestParam Integer codigoEmpresa, @RequestParam Long dataTime) {
        ModelMap mm = new ModelMap();

        try {
            if (StringUtils.isBlank(chave)) {
                throw new ServiceException("A chave n\u00E3o pode ser vazia!");
            }

            if (codigoEmpresa == null || codigoEmpresa == 0) {
                throw new ServiceException("O codigo da empresa inv\u00E1lido!");
            }

            if(dataTime == null || dataTime == 0) {
                throw new ServiceException("Data inv\u00E1lida!");
            }
            ResultSet rs = climaService.buscarUltimoClimaAcesso(chave, codigoEmpresa, new Date(dataTime));

            JSONArray array = new JSONArray();
            if(rs != null) {
                while (rs.next()) {
                    JSONObject json = new JSONObject();
                    json.put("data_reg", rs.getTimestamp("data_reg"));
                    json.put("temperatura", rs.getInt("temperatura"));
                    json.put("condicao", rs.getInt("condicao"));
                    array.put(json);

                }
            }
            mm.addAttribute(RETURN, Uteis.toList(array));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/removerEmpresa", method = {RequestMethod.POST})
    public @ResponseBody
    ModelMap removerEmpresa(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            Empresa emp = service.descobrir(ctx);
            List<String> resultados = new ArrayList<>();
            try {
                resultados = service.executarBackups(emp);
            } catch (Exception e) {
                resultados.add("ERRO ao executar backups devido ao erro: " + e.getMessage());
            }            
            final Usuario u = UtilContext.getBean(UsuarioService.class).validarUsuario(Constants.LOGIN_FINAN_INTEG, Constants.SENHA_FINAN_INTEG, false);
            resultados.addAll(service.removerBancos(emp, u));
            mm.addAttribute(String.format("Resultado Remoção %s - %s", emp.getChave(), emp.getName()), resultados.toString());

            try {
                service.finalizarRemocaoBancos(emp, u, resultados);
            } catch (Exception e) {
                Uteis.logarDebug(String.format("### Não foi possível atualizar o log da remoção do banco %s => %s", ctx, e.getMessage()));
            }

        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "ativarTrialNovoTreino", method = {RequestMethod.POST})
    public @ResponseBody
    ResponseEntity<ModelMap> ativarTrialNovoTreino(@RequestBody CriarTrialNovoTreinoDTO dto) {
        ModelMap mm = new ModelMap();
        try {
            TrialNovoTreinoDTO resultDto = serviceSite.cadastrarTrialNovoTreino(dto);
            mm.addAttribute(RETURN, resultDto);
            return new ResponseEntity<>(mm, HttpStatus.OK);
        } catch (Exception ex) {
            if (StringUtils.isNotBlank(ex.getMessage()) && (ex.getMessage().startsWith("PAGAMENTO_NAO_APROVADO") || ex.getMessage().startsWith("CARTAO_INVALIDO"))) {
                Map<String, String> errocobranca = new HashMap<>();
                errocobranca.put("errocobranca", "true");
                errocobranca.put("token", ex.getMessage());
                mm.addAttribute(RETURN, errocobranca);
                return new ResponseEntity<>(mm,  HttpStatus.OK);
            }

            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
            HashMap<String, String> meta = new HashMap<>();
            HttpStatus status = HttpStatus.CONFLICT;
            if (StringUtils.isNotBlank(ex.getMessage()) && ex.getMessage().equals("erro_ativar_empresa")) {
                meta.put(STATUS_ERRO, ex.getMessage());
            } else if (StringUtils.isNotBlank(ex.getMessage()) && ex.getMessage().contains("Empresa já cadastrada!")) {
                meta.put(STATUS_ERRO, "erro_empresa_duplicada");
            } else {
                meta.put(STATUS_ERRO, ex.getMessage());
                status = HttpStatus.INTERNAL_SERVER_ERROR;
                System.out.println(ex.getMessage());
            }
            mm.addAttribute("meta", meta);
            return new ResponseEntity<>(mm, status);
        }
    }

    @RequestMapping(value = "tentarAtivacao", method = {RequestMethod.POST})
    public @ResponseBody
    ResponseEntity<ModelMap> tentarAtivacao(@RequestParam String token, @RequestBody CriarTrialNovoTreinoDTO dto) {
        ModelMap mm = new ModelMap();
        try {
            TrialNovoTreinoDTO resultDto = serviceSite.tentarCobrancaNovamente(token, dto);
            mm.addAttribute(RETURN, resultDto);
            return new ResponseEntity<>(mm, HttpStatus.OK);
        } catch (Exception ex) {

            if (StringUtils.isNotBlank(ex.getMessage()) && (ex.getMessage().startsWith("PAGAMENTO_NAO_APROVADO") || ex.getMessage().startsWith("CARTAO_INVALIDO"))) {
                Map<String, String> errocobranca = new HashMap<>();
                errocobranca.put("errocobranca", "true");
                errocobranca.put("token", ex.getMessage());
                mm.addAttribute(RETURN, errocobranca);
                return new ResponseEntity<>(mm,  HttpStatus.OK);
            }

            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
            HashMap<String, String> meta = new HashMap<>();
            HttpStatus status = HttpStatus.CONFLICT;
            if (StringUtils.isNotBlank(ex.getMessage()) && ex.getMessage().equals("erro_ativar_empresa")) {
                meta.put(STATUS_ERRO, ex.getMessage());
            } else if (StringUtils.isNotBlank(ex.getMessage()) && ex.getMessage().contains("Empresa já cadastrada!")) {
                meta.put(STATUS_ERRO, "erro_empresa_duplicada");
            } else {
                meta.put(STATUS_ERRO, "erro_inesperado");
                status = HttpStatus.INTERNAL_SERVER_ERROR;
                System.out.println(ex.getMessage());
            }
            mm.addAttribute("meta", meta);
            return new ResponseEntity<>(mm, status);
        }
    }

    @RequestMapping(value = "ativarEmpresa", method = {RequestMethod.POST})
    public @ResponseBody
    ResponseEntity ativarEmpresa(@RequestBody EmpresaSiteJSON empresa) {
        ModelMap mm = new ModelMap();
        try {
            EmpresaSite empresaSite = serviceSite.cadastrarEmpresaNovaZW(empresa);
            mm.addAttribute(serviceSite.htmlOK(empresaSite, empresaSite.getEmpresa()));
            return new ResponseEntity(mm, HttpStatus.OK);
        } catch (Exception ex) {            
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
            return new ResponseEntity(mm, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @RequestMapping(value = "{ctx}/updateServices", method = {RequestMethod.POST})
    public @ResponseBody
    ModelMap updateServices(@PathVariable final String ctx, @RequestParam final String infra) {
        ModelMap mm = new ModelMap();
        try {
            Empresa emp = null;
            if (infra != null && infra.equals("OUTRO"))
                emp = service.descobrir(ctx);
            service.updateScriptsServicos(emp, InfoInfraEnum.valueOf(infra));
            mm.addAttribute(STATUS_SUCESSO);
        } catch (Exception e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/updateBackup", method = {RequestMethod.POST})
    public @ResponseBody
    ModelMap updateBackup(@PathVariable final String ctx, @RequestParam final String infra,
            @RequestParam(required = false) final String flagSoftware) {
        ModelMap mm = new ModelMap();
        try {
            InfoInfraEnum inf = InfoInfraEnum.valueOf(infra);
            service.updateScriptsBackupPorInfra(inf);
            service.updateWorkersScriptsBackup(inf);
            mm.addAttribute(STATUS_SUCESSO);
        } catch (Exception e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
        }
        return mm;
    }

    @RequestMapping(value = "updateBackupAll", method = {RequestMethod.POST})
    public @ResponseBody
    ModelMap updateBackupAll() {
        ModelMap mm = new ModelMap();
        try {
            List<InfoInfraEnum> infras = InfoInfraEnum.getListaAtivas();
            for (InfoInfraEnum inf : infras) {
                service.updateScriptsBackupPorInfra(inf);
            }
            service.updateWorkersScriptsBackup(Constants.INFRA);
            mm.addAttribute(STATUS_SUCESSO);
        } catch (Exception e) {
            mm.addAttribute(STATUS_ERRO, e);
        }
        return mm;
    }

    @RequestMapping(value = "alterarSenhas", method = {RequestMethod.POST})
    public @ResponseBody
    ModelMap alterarSenhas(@RequestParam String novaSenhaAdmin, @RequestParam String novaSenhaPactoBR,
            @RequestParam String userName, @RequestParam String pwd,
            @RequestParam(required = false) String key) {
        ModelMap mm = new ModelMap();
        try {
            List<Empresa> lista;
            if (key != null && !key.isEmpty()) {
                lista = new ArrayList();
                lista.add(service.obterPorId(key));
            } else {
                lista = service.obterTodos(true);
            }
            lista = Ordenacao.ordenarLista(lista, "roboControle");
            final String adminCrypto = Uteis.encriptar(novaSenhaAdmin.toUpperCase());
            final String pactoCrypto = Uteis.encriptar(novaSenhaPactoBR.toUpperCase());
            List<String> retornos = new ArrayList<String>();
            final Usuario u = usuarioService.validarUsuario(userName, pwd, false);
            for (Empresa e : lista) {
                if (e.getAtiva() && StringUtils.isNotBlank(e.getRoboControleSemHTTPS()) && e.getModulos().contains("ZW")) {
                    //nao alterar senha das empresas abaixo:
                    if (e.getNomeBD().equals("bdzillyonpacto")
                            || e.getNomeBD().equals("bdzillyonpactosp")
                            || e.getNomeBD().equals("bdzillyonsollos")) {
                        continue;
                    }

                    final String url = String.format("%s/UpdateServlet", e.getRoboControleSemHTTPS());
                    final String sql = String.format("update usuario set senha = '%s',dataalteracaosenha=current_timestamp where upper(username) = 'ADMIN'; update usuario set senha = '%s',dataalteracaosenha=current_timestamp where upper(username) = 'PACTOBR';",
                            adminCrypto, pactoCrypto);
                    Map<String, String> p = new HashMap();
                    p.put("op", "updateONE");
                    p.put("hostPG", e.getHostBD());
                    p.put("portaPG", e.getPorta().toString());
                    p.put("userPG", e.getUserBD());
                    p.put("pwdPG", e.getPasswordBD());
                    p.put("bd", e.getNomeBD());
                    p.put("format", "json");
                    p.put("sql", sql);
                    if (u != null) {
                        p.put("lgn", Uteis.obterTokenUsuario(u.getUserName(), u.getPassTrans()));
                    }
                    try {
                        Uteis.logar(null, url + " " + p);
                        final String retorno = HttpRequestUtil.executeRequestInner(url, p, 15000, 4000, "UTF-8");
                        Uteis.logar(null, retorno);
                        retornos.add(String.format("Empresa: %s senha do ZillyonWeb alterada!", e.getName()));
                    } catch (Exception ex) {
                        retornos.add(String.format("Erro ao alterar senha da empresa %s no ZILLYONWEB, devido ao erro: %s",
                                e.getNomeBD(), ex.getMessage()));
                    }
                }
                if (e.getAtiva() && e.getUrlTreino() != null && !e.getUrlTreino().isEmpty()
                        && (e.getModulos().contains("TR") || e.getModulos().contains("PEF"))) {
                    final String url = String.format("%s/prest/EndpointControl/%s/resetPwd", e.getUrlTreinoCorrigindoProtocolo(), e.getChave());
                    Map<String, String> p = new HashMap();
                    p.put("userName", "pactobr");
                    p.put("pwd", novaSenhaPactoBR.toUpperCase());
                    try {
                        Uteis.logar(null, url + " " + p);
                        final String retorno = HttpRequestUtil.executeRequestInner(url, p, 15000, 4000, "UTF-8");
                        Uteis.logar(null, retorno);
                        retornos.add(String.format("Empresa: %s senha do Treino alterada!", e.getName()));
                    } catch (Exception ex) {
                        retornos.add(String.format("Erro ao alterar senha da empresa %s no TREINO, devido ao erro: %s",
                                e.getNomeBD(), ex.getMessage()));
                    }
                }

            }
            mm.addAttribute(RETURN, retornos);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "consultarEmpresaFinanceiro", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap consultarEmpresaFinanceiro(@RequestParam String key) {
        ModelMap mm = new ModelMap();
        try {
            EmpresaFinanceiroService empresaFinanceiroService = (EmpresaFinanceiroService) UtilContext.getBean(EmpresaFinanceiroService.class);
            EmpresaFinanceiro empresaFinanceiro = empresaFinanceiroService.obterPorChave(key);
            if (empresaFinanceiro == null) {
                mm.addAttribute(STATUS_ERRO_REQUISICAO_WS, "Não foi encontrada nenhuma EmpresaFinanceiro com a chave " + key);
                return mm;
            }
            mm.addAttribute("empresaFinanceiro", empresaFinanceiro.toJSONCompleto().toString());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO_REQUISICAO_WS, ex.getMessage());
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "/descobrirUsuarioNew", method = RequestMethod.POST)
    @Deprecated
    public @ResponseBody
    ModelMap usuarioNew(@RequestParam String email) {        
        ModelMap mm = new ModelMap();
        try {
            if (email == null || email.isEmpty()) {
                throw new ServiceException("E-mail não informado");
            }
            UsuarioApp usuario = userAppService.obterPorEmail(email.toLowerCase());
            if (usuario == null
                    || usuario.getEmail() == null
                    || usuario.getEmail().isEmpty()) {
                throw new ServiceException("E-mail não encontrado");
            }
            mm.addAttribute("url", usuario.getEmpresa().getUrlTreinoMobile());
            mm.addAttribute("chave", usuario.getEmpresa().getChave());
            mm.addAttribute("urlZw", usuario.getEmpresa().getRoboControle());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "/v3/descobrirUsuario", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap descobrirUsuarioV3(@RequestParam String parametro) {
        ModelMap mm = new ModelMap();
        try {
            if (parametro == null || parametro.isEmpty()) {
                throw new ServiceException("Nenhum parâmetro informado");
            }

            UsuarioApp usuario = null;
            List<UsuarioApp> listaUsu = userAppService.descobrirUsuarioV3(parametro);
            if (UteisValidacao.emptyList(listaUsu)) {
                throw new ServiceException("Usuário não encontrado");
            } else if (listaUsu.size() > 1) {
                throw new ServiceException("Mais de um usuário encontrado");
            } else {
                usuario = listaUsu.get(0);
            }

            if (usuario == null
                    || usuario.getEmail() == null
                    || usuario.getEmail().isEmpty()) {
                throw new ServiceException("Usuário não encontrado");
            }
            mm.addAttribute("url", usuario.getEmpresa().getUrlTreinoMobile());
            mm.addAttribute("chave", usuario.getEmpresa().getChave());
            mm.addAttribute("urlZw", usuario.getEmpresa().getRoboControle());
            mm.addAttribute("email", usuario.getEmail());
            mm.addAttribute("cpf", usuario.getCpf());
            mm.addAttribute("dataNascimento", Uteis.getDataAplicandoFormatacao(usuario.getDataNascimento(), "dd/MM/yyyy"));
            mm.addAttribute("telefone", usuario.getTelefone());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
    
    @RequestMapping(value = "{key}/descobrirAlunoZW", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap descobrirAlunoZW(@PathVariable final String key, @RequestParam
            final String email, @RequestParam(required = false) final String celular) {
        ModelMap mm = new ModelMap();
        try {
            if (key == null || key.isEmpty()) {
                throw new ServiceException("Nenhuma empresa informada!");
            }
            Empresa e = service.obterPorId(key);
            if (e == null || !e.getAtiva()) {
                throw new ServiceException("Empresa inválida!");
            }
            if (UteisValidacao.emptyString(email) || !UteisEmail.getValidEmail(email)){
                throw new ServiceException("E-mail inválido!");
            }
            if (!UteisValidacao.emptyString(celular) && celular.length() != 11){//quando a validação do usuário é por Rede Social o celular não é informado
                throw new ServiceException("Celular inválido!");
            }
            //
            mm.addAttribute(RETURN, userAppService.verificarIdentidadeDadosBasicosZW(e, celular, email));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Descobrir usuário por celular com dados criptografados",
                  notes = "Busca usuários pelo número de celular informado retornando dados básicos criptografados. " +
                          "Os dados de entrada e saída são criptografados usando algoritmo AES para segurança. " +
                          "Retorna informações básicas dos usuários encontrados como código do cliente, nome, " +
                          "email, celular, matrícula e outros identificadores do sistema. " +
                          "O endpoint verifica se a empresa possui módulo ZW para determinar o tipo de busca a ser realizada.",
                  tags = "Descoberta de Usuários")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Usuários encontrados com sucesso",
                     response = DescobrirAlunoCelularAppCryptResponse.class)
    })
    @RequestMapping(value = "{chave}/iY7pJ3sO6sT0rA0nR3wS8jQ2yL8iO2qX", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap descobrirAlunoCelularAppCrypt(
            @ApiParam(value = "Chave identificadora da empresa no sistema",
                      required = true,
                      defaultValue = "empresa123")
            @PathVariable final String chave,

            @ApiParam(value = "Dados criptografados contendo as informações do celular do usuário a ser pesquisado. " +
                              "Os dados devem ser criptografados usando algoritmo AES e devem conter " +
                              "um objeto JSON com as propriedades 'ddi', 'ddd' e 'celular'. " +
                              "<br/><br/>" +
                              "<strong>Estrutura esperada do JSON descriptografado:</strong><br/>" +
                              "- <strong>ddi:</strong> Código de discagem direta internacional (ex: '55' para Brasil)<br/>" +
                              "- <strong>ddd:</strong> Código de discagem direta à distância (ex: '11' para São Paulo)<br/>" +
                              "- <strong>celular:</strong> Número do celular sem formatação (ex: '987654321')",
                      required = true,
                      defaultValue = "eyJkZGkiOiI1NSIsImRkZCI6IjExIiwiY2VsdWxhciI6Ijk4NzY1NDMyMSJ9")
            @RequestBody String content) {
        ModelMap mm = new ModelMap();
        try {
            JSONObject o = new JSONObject(Uteis.decryptUserData(content));
            String ddi = o.optString("ddi");
            String ddd = o.optString("ddd");
            String celular = o.optString("celular");

            List<UsuarioAppBasicoJSON> arr = acaoDescobrirAlunoCelularApp(chave, ddi, ddd, celular);
            JSONArray arrayUsuarios = new JSONArray();
            for (UsuarioAppBasicoJSON usuarioAppBasicoJSON : arr) {
                arrayUsuarios.put(new JSONObject(usuarioAppBasicoJSON));
            }
            mm.addAttribute(RETURN, Uteis.encryptUserData(arrayUsuarios.toString()));
        } catch (Exception ex) {
            String erro = ex.getMessage();
            try {
                erro = Uteis.encryptUserData(ex.getMessage());
            } catch (Exception ignored) {}
            mm.addAttribute(STATUS_ERRO, erro);
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    private List<UsuarioAppBasicoJSON> acaoDescobrirAlunoCelularApp(String key, String ddi, String ddd, String celular) throws ServiceException {
        if (key == null || key.isEmpty()) {
            throw new ServiceException("Nenhuma empresa informada!");
        }
        Empresa e = service.obterPorId(key);
        if (e == null || !e.getAtiva()) {
            throw new ServiceException("Empresa inválida!");
        }
        if (UteisValidacao.emptyString(celular) || !celular.matches("[0-9]+")){ // valor aceito para o funcionamento ex.: 5562981813435
            throw new ServiceException("Celular inválido!");
        }
        boolean treinoIndependente = ! e.getModulos().contains("ZW");
        List<UsuarioAppBasicoJSON> arr = null;
        if(treinoIndependente){
            arr = userAppService.verificarIdentidadeDadosBasicosByCelularTreinoIndepApp(e, ddi, ddd, celular);
        }else{
            arr = userAppService.verificarIdentidadeDadosBasicosByCelularApp(e, ddi, ddd, celular);
            if(arr == null || arr.isEmpty()){
                arr = userAppService.verificarDadosBasicosByCelularColaborador(e, ddi, ddd, celular);
            }
        }
        if(arr == null || arr.isEmpty())
        {
            throw new ServiceException(String.format("Celular não encontrado! %s - +%s (%s) %s",
                    key, ddi, ddd, celular));
        }
        return arr;
    }

    @RequestMapping(value = "{key}/descobrirAlunoEmailUsername", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap descobrirAlunoEmailUsername(@PathVariable final String key,
                                         @RequestParam final String email) {
        ModelMap mm = new ModelMap();
        try {
            List<UsuarioAppBasicoJSON> arr = acaoDescobrirAlunoEmailUsername(key, email);
            mm.addAttribute(RETURN, arr);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Descobrir usuário por email com dados criptografados",
                  notes = "Busca usuários pelo email informado retornando dados básicos criptografados. " +
                          "Os dados de entrada e saída são criptografados usando algoritmo AES para segurança. " +
                          "Retorna informações básicas dos usuários encontrados como código do cliente, nome, " +
                          "email, celular, matrícula e outros identificadores do sistema.",
                  tags = "Descoberta de Usuários")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Usuários encontrados com sucesso",
                     response = DescobrirAlunoEmailUsernameCryptResponse.class)
    })
    @RequestMapping(value = "{key}/wV9zD7rW1yJ6tI4zG4nX7fT9oL2fG4fJ", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap descobrirAlunoEmailUsernameCrypt(
            @ApiParam(value = "Chave identificadora da empresa no sistema",
                      required = true,
                      defaultValue = "empresa123")
            @PathVariable final String key,

            @ApiParam(value = "Dados criptografados contendo o email do usuário a ser pesquisado. " +
                              "Os dados devem ser criptografados usando algoritmo AES e devem conter " +
                              "um objeto JSON com a propriedade 'email'.",
                      required = true,
                      defaultValue = "********************************************")
            @RequestBody String content) {
        ModelMap mm = new ModelMap();
        try {
            JSONObject o = new JSONObject(Uteis.decryptUserData(content));
            String email = o.optString("email");

            List<UsuarioAppBasicoJSON> arr = acaoDescobrirAlunoEmailUsername(key, email);

            JSONArray arrayUsuarios = new JSONArray();
            for (UsuarioAppBasicoJSON usuarioAppBasicoJSON : arr) {
                arrayUsuarios.put(new JSONObject(usuarioAppBasicoJSON));
            }
            mm.addAttribute(RETURN, Uteis.encryptUserData(arrayUsuarios.toString()));
        } catch (Exception ex) {
            String erro = ex.getMessage();
            try {
                erro = Uteis.encryptUserData(ex.getMessage());
            } catch (Exception ignored) {}
            mm.addAttribute(STATUS_ERRO, erro);
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    private List<UsuarioAppBasicoJSON> acaoDescobrirAlunoEmailUsername(String key, String email) throws ServiceException {
        if (key == null || key.isEmpty()) {
            throw new ServiceException("Nenhuma empresa informada!");
        }
        Empresa e = service.obterPorId(key);
        if (e == null || !e.getAtiva()) {
            throw new ServiceException("Empresa inválida!");
        }
        if (UteisValidacao.emptyString(email)) {
            throw new ServiceException("Email inválido!");
        }
        boolean treinoIndependente = ! e.getModulos().contains("ZW");
        List<UsuarioAppBasicoJSON> arr = null;
        if(treinoIndependente) {
            arr = userAppService.verificarIdentidadeDadosBasicosByEmailTreinoIndepApp(e, email);
        } else {
            arr = userAppService.verificarUsuarioEmail(e, email);
        }
        if (arr == null || arr.isEmpty()) {
            throw new ServiceException("Email não encontrado!");
        }
        return arr;
    }

    @RequestMapping(value = "/dadosRede", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap dadosRede(@RequestParam String chave) {
        ModelMap mm = new ModelMap();
        try {
            Uteis.logarDebug(String.format("############# DADOS REDE Consultar => %s", chave));
            Uteis.logarDebug(String.format("############# DADOS REDE CACHE ALL REDES => %s ITENS",
                    EmpresaFinanceiroServiceImpl.CACHE_EMPRESA_REDE.keySet().size()));
            Uteis.logarDebug(String.format("############# DADOS REDE %s CACHE_EMPRESA_REDE => %s ITENS", chave,
                    EmpresaFinanceiroServiceImpl.CACHE_EMPRESA_REDE.get(chave) != null ? EmpresaFinanceiroServiceImpl.CACHE_EMPRESA_REDE.get(chave).size() : 0));

            /*Enumeration<String> parameterNames = request.getParameterNames();

            while (parameterNames.hasMoreElements()) {

                String paramName = parameterNames.nextElement();

                String[] paramValues = request.getParameterValues(paramName);
                for (int i = 0; i < paramValues.length; i++) {
                    String paramValue = paramValues[i];
                    Uteis.logarDebug(String.format("############# DADOS REDE => param: %s value %s ###########", paramName, paramValue));
                }
            }*/

            if (EmpresaFinanceiroServiceImpl.CACHE_EMPRESA_REDE.containsKey(chave) && !EmpresaFinanceiroServiceImpl.CACHE_EMPRESA_REDE.get(chave).isEmpty()) {
                mm.addAttribute("empresas", EmpresaFinanceiroServiceImpl.CACHE_EMPRESA_REDE.get(chave));
                Uteis.logarDebug(String.format("############# DADOS REDE => %s com %s itens CONSULTADOS do CACHE", chave,
                        EmpresaFinanceiroServiceImpl.CACHE_EMPRESA_REDE.get(chave).size()));
                return mm;
            }

            List<EmpresaJSON> empresaJSONS = empFinanceiro.consultarEmpresasRede(chave);
            if (empresaJSONS != null && empresaJSONS.size() > 0)
                EmpresaFinanceiroServiceImpl.CACHE_EMPRESA_REDE.put(chave, empresaJSONS);

            Uteis.logarDebug(String.format("############# DADOS REDE => %s com %s itens CONSULTADOS do WS", chave, empresaJSONS != null ? empresaJSONS.size() : 0));
            mm.addAttribute("empresas", empresaJSONS);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
    
    @RequestMapping(value = "/obterDadosParaTeste", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap obterDadosParaTeste(@RequestParam String chave) {
        ModelMap mm = new ModelMap();
        try {
            Empresa emp = service.obterPorId(chave);
            if (emp != null) {
                mm.addAttribute("empresa", new EmpresaOAMDJSON(emp).toJSON());
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "/consultarEmpresas", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarEmpresas(@RequestParam Integer pagina,
                               @RequestParam Integer limitePorPagina,
                               @RequestParam String orderBy,
                               @RequestParam(required = false) boolean incluirEmpresasPacto,
                               @RequestParam(required = false) boolean somenteAtivas,
                               @RequestParam(required = false) String chave) {
        ModelMap mm = new ModelMap();
        try {

            Integer offset = (pagina == 0 ? 0 : (pagina - 1) * limitePorPagina + 1);

            List<Empresa> listaEmpresas = service.obterEmpresas(incluirEmpresasPacto, somenteAtivas, chave, limitePorPagina, offset, orderBy, null);
            Integer total = service.obterEmpresasTotal(incluirEmpresasPacto, somenteAtivas, chave, null);

            List<EmpresaDTO> lista = new ArrayList<>();
            for (Empresa empresa : listaEmpresas) {
                lista.add(new EmpresaDTO(empresa));
            }

            RespostaEmpresaDTO resp = new RespostaEmpresaDTO();
            resp.setTotal(total);
            resp.setEmpresas(lista);
            mm.addAttribute(RETURN, resp);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "/pesquisarEmpresas", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap pesquisarEmpresas(@RequestParam String chaveOuNomeEmpresa, @RequestParam(required = false) Integer limite) {
        ModelMap mm = new ModelMap();
        try {
            if(limite == null){
                limite = 10;
            }
            List<Empresa> empresas = service.pesquisarEmpresas(chaveOuNomeEmpresa, limite);
            List<EmpresaResponseDTO> empresasRetorno = new ArrayList<>();

            for (Empresa empresa : empresas) {
                empresasRetorno.add(new EmpresaResponseDTO(empresa));
            }
            mm.addAttribute(RETURN, empresasRetorno);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "/betatesters", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap betatesters() {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute(RETURN, service.betaTesters());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "/sO4tA9vQ9wA8dU5iD5pV5wK8pP0zF4kM", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap consultarEmpresasTesteAuto(@RequestParam(required = false) boolean incluirEmpresasPacto,
                                        @RequestParam(required = false) boolean somenteAtivas,
                                        @RequestParam(required = false) Integer limit,
                                        @RequestParam(required = false) String orderBy,
                                        @RequestParam(required = false) String infra) {
        ModelMap mm = new ModelMap();
        try {
            List<EmpresaDTO> lista = new ArrayList<>();
            for (InfoInfraEnum infoEnum : InfoInfraEnum.values()) {
                if (UteisValidacao.emptyString(infra) || (!UteisValidacao.emptyString(infra) && infoEnum.getDescricao().equalsIgnoreCase(infra))) {
                    List<Empresa> listaEmpresas = service.obterEmpresas(incluirEmpresasPacto, somenteAtivas, null, limit, 0, orderBy, infoEnum);
                    for (Empresa empresa : listaEmpresas) {
                        lista.add(new EmpresaDTO(empresa));
                    }
                }
            }

            mm.addAttribute(RETURN, lista);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
}
