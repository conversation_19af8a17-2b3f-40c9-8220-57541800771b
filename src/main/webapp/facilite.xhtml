<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
    <h:head>
        <title>Dashboard - Facilite</title>

        <style>
            body{
                font-size: 14px;
                background-color: #f6f6f6;
                font-family: 'Lato',sans-serif;
                margin: 0;
                padding: 0;
            }
            #main-menu .item{
                display: block;
                width: 100%;
                padding: 5px 10px;
                height: 40px;
                line-height: 40px;
                word-wrap: break-word;
                overflow: hidden;
                
            }
            #main-menu{
                margin: 0;
                padding-bottom: 0;
                background-color: #222;
                color: #808b9c;
                width: 20vw;
                height: 100vh;
                float: left;
                overflow-x: hidden;
                overflow-y: auto;
                display: inline-block;
            }
            #conteudo{
                display: block;
                width: 100%;
                text-align: center;
            }

            .caixa{
                background: #fff;
                border-radius: 2px;
                overflow: hidden;
                display: inline-block;
                box-shadow: 1px 1px 4px #888888;
                margin: 15px;
                width: calc(33% - 30px);
                height: 180px;
            }

            .caixa .titulo{
                text-transform: uppercase;
                color: #becad4;
                font-weight: bold;
                font-size: 10px;
                text-align: center;
                display: block;
                margin-top: 15px;
            }
            .valor{
                font-size: 40px;
                color: #557a94;
                display: block;
                text-align: center;
                margin-top: 30px;
            }
            .topo{
                display: block;
                background-color: #00b3ee;
                height: 80px;
                text-align: center;
            }
            .topo img{
                margin-top: 15px;
                height: 50px;
            }
            .container  table tr {
                line-height: 30px;
            }
            .container  table tr th{
                border-bottom: 2px solid #e4e4e4;
            }
            .container  table tr td{
                border-bottom: 1px solid #e4e4e4;
            }
            .container {
                background: #fff;
                margin: 0;
                padding: 20px;
                display: block;
                width: calc(100% - 70px);
                box-shadow: 1px 1px 4px #888888;
                margin-left: 15px;
            }
            .container  a,.container  a:visited, .atualizar {
                text-transform: none;
                text-decoration: none;
                color: #557a94;
            }
            .container a span{
                display: block;
                color: #557a94;
                font-weight: bold;

            }

            .ui-dialog.ui-widget-content .ui-dialog-title{
                font-size: 16px;
            }
            .valor.money{
                margin-top: 15px;
                font-size: 20px;
                color: #f29f29;
            }

        </style>
        <h:outputStylesheet library="css" name="font-awesome.min.css"/>


    </h:head>
    <h:body>
    <h:form id="fm">
        <div class="topo">
            <img src="#{resource['imagens/logo-facilite-branco.svg']}"/>

            <p:commandLink action="#{FaciliteControle.atualizar}"
                           update=":fm:empresas" styleClass="atualizar"
                           style="color: #ffffff; float: right; font-size: 24px; margin: 10px;">
                <i class="fa-icon-refresh"></i>
            </p:commandLink>
        </div>
        <div id="conteudo">
            <div class="caixa">
                <span class="titulo">Número de remessas</span>
                <span class="valor">#{FaciliteControle.estatisticas.remessas}</span>
                <span class="valor money">
                    R$
                    <h:outputText value="#{FaciliteControle.estatisticas.valorTotal}"
                                  style="font-size: 30px;">
                                        <f:convertNumber pattern="#0.00" />
                                    </h:outputText>
                    </span>
            </div>
            <div class="caixa">
                <span class="titulo">Aguardando retorno</span>
                <span class="valor">#{FaciliteControle.estatisticas.totais['REMESSA_ENVIADA']}</span>
                <span class="valor money">
                    R$
                    <h:outputText value="#{FaciliteControle.estatisticas.valores['REMESSA_ENVIADA']}"
                                  style="font-size: 30px;">
                                        <f:convertNumber pattern="#0.00" />
                                    </h:outputText>
                    </span>
            </div>
            <div class="caixa">
                <span class="titulo">Retorno processado</span>
                <span class="valor">#{FaciliteControle.estatisticas.totais['RETORNO_PROCESSADO']}</span>
                <span class="valor money">
                    R$
                    <h:outputText value="#{FaciliteControle.estatisticas.valores['RETORNO_PROCESSADO']}"
                                  style="font-size: 30px;">
                                        <f:convertNumber pattern="#0.00" />
                                    </h:outputText>
                    </span>
            </div>

            <h:panelGroup id="empresas" styleClass="container" layout="block">

                <table style="width:100%;">
                    <tr>
                        <th  style="text-align: left;">Empresa</th>
                        <th>Remessas</th>
                        <th>Aguardando retorno</th>
                        <th>Retorno processado</th>
                        <th style="color: #777777;">Aguardando</th>
                        <th style="color: #94000f;">Rejeitados</th>
                        <th style="color: #008800;">Aceitos</th>
                    </tr>
                    <ui:repeat value="#{FaciliteControle.empresas}" var="emp">
                        <tr>
                            <td style="text-align: left;">
                                <a href="#{emp.login}" target="_blank">
                                   <span>#{emp.empresa}</span>
                                </a>
                            </td>
                            <td>#{emp.remessas}</td>
                            <td>#{emp.totais['REMESSA_ENVIADA']}</td>
                            <td>#{emp.totais['RETORNO_PROCESSADO']}</td>
                            <td style="text-align: right;">
                                <p:commandLink action="#{FaciliteControle.detalhar(emp, 'REMESSA_ENVIADA')}"
                                               oncomplete="modalDialog.show()"  update=":fm:modal">
                                    <h:outputText value="#{emp.itensAguardando}"
                                                  rendered="#{emp.itensAguardando > 0.0}">
                                        <f:convertNumber pattern="#0.00" />
                                    </h:outputText>
                                </p:commandLink>


                            </td>
                            <td style="text-align: right;">
                                <p:commandLink action="#{FaciliteControle.detalhar(emp, 'ERRO_RETORNO')}"
                                               oncomplete="modalDialog.show()"  update=":fm:modal">
                                    <h:outputText value="#{emp.itensRejeitados}"
                                                  rendered="#{emp.itensRejeitados > 0.0}">
                                        <f:convertNumber pattern="#0.00" />
                                    </h:outputText>
                                </p:commandLink>

                            </td>
                            <td style="text-align: right;">
                                <p:commandLink action="#{FaciliteControle.detalhar(emp, 'RETORNO_PROCESSADO')}"
                                oncomplete="modalDialog.show()"  update=":fm:modal">
                                    <h:outputText value="#{emp.itensAceitos}"
                                                  rendered="#{emp.itensAceitos > 0.0}">
                                        <f:convertNumber pattern="#0.00" />
                                    </h:outputText>
                                </p:commandLink>

                            </td>
                        </tr>
                    </ui:repeat>
                </table>

            </h:panelGroup>

        </div>

        <p:dialog widgetVar="modalDialog" modal="true" id="modal" showEffect="fade" hideEffect="fade" resizable="true"
                  width="800">
        <f:facet name="header">
            <h:outputText value="#{FaciliteControle.label}"/>
        </f:facet>
            <div class="container" style="max-height: 65vh;">
                <table style="width:100%;">
                <tr>
                    <th style="text-align: left;">Cliente</th>
                    <th style="text-align: left;">Descrição</th>
                    <th>Vencimento</th>
                    <th>Envio</th>
                    <th style="text-align: right;">Valor</th>
                </tr>
                <ui:repeat value="#{FaciliteControle.infos}" var="info">
                    <tr>
                    <td>#{info.nome}</td>
                    <td>#{info.descricao}</td>
                    <td><h:outputText value="#{info.vencimento}">
                        <f:convertDateTime pattern="dd/MM/yyyy" />
                    </h:outputText></td>
                    <td><h:outputText value="#{info.envio}">
                        <f:convertDateTime pattern="dd/MM/yyyy" />
                    </h:outputText></td>
                    <td style="text-align: right;"><h:outputText value="#{info.valor}">
                        <f:convertNumber pattern="#0.00" />
                    </h:outputText></td>
                    </tr>
                </ui:repeat>
                </table>

            </div>

        </p:dialog>

        <p:dialog modal="true" id="panelCarregando"
                  styleClass="panelCarregando"
                  showHeader="false"
                  closeOnEscape="false"
                  widgetVar="carregando"
                  draggable="false"
                  closable="false"
                  maximizable="false"
                  minimizable="false"
                  resizable="false"
                  width="210"
                  minWidth="220">
            <i class="fa-icon-spin fa-icon-refresh fa-icon-2x" style="vertical-align: middle;" />
            <h:outputText style="font-weight: bold; vertical-align: middle; margin-left: 10px; font-size: 14px;" value="Por favor, aguarde..."/>
        </p:dialog>

        <p:ajaxStatus style="width:64px;height:64px;position:fixed;right:5px;bottom:5px"
                      onstart="carregando.show();"
                      oncomplete="carregando.hide();"
                      onerror="carregando.hide();"/>



    </h:form>
    </h:body>
</html>
