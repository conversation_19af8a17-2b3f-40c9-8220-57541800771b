<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="conteudo">
        <style>
            @media (max-width: 1400px) {
                .container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
                    width: 100% !important;
                }
            }

            @media (min-width: 1200px) {
                .container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
                    width: 95%;
                }
            }
        </style>

        <h:panelGroup layout="block" style="padding: 0;" id="divGeral">
            <div class="bread span12"></div>

            <h:panelGroup styleClass="span10 caixaPrincipal" id="dados3"
                          layout="block"
                          style="width: 100%; padding: 0px; margin: 0px; margin-top: 20px">
                <h4>#{MigracaoRecursoFeedbackControle.totalLista} - Feedbacks</h4>

                <h:panelGroup layout="block"
                              style="display:grid; grid-template-columns: 1fr 1fr; padding: 10px 20px">

                    <h:panelGroup layout="block" style="display: flex;">
                        <p:selectBooleanCheckbox id="somenteComFeedback" itemLabel="Somente com feedback informado"
                                                 value="#{MigracaoRecursoFeedbackControle.somenteComFeedback}">
                            <p:ajax event="change" update=":fmLay:dados3"
                                    listener="${MigracaoRecursoFeedbackControle.carregarLista}"/>
                        </p:selectBooleanCheckbox>
                    </h:panelGroup>

                    <h:panelGroup layout="block" style="text-align: end;">
                        <p:commandButton ajax="false" partialSubmit="false" value="Exportar">
                            <p:dataExporter type="xls" target="listaLog"
                                            fileName="#{MigracaoRecursoFeedbackControle.fileName}"
                                            postProcessor="#{MigracaoRecursoFeedbackControle.postProcessXLS}"/>
                        </p:commandButton>
                    </h:panelGroup>
                </h:panelGroup>

                <p:dataTable var="item" value="#{MigracaoRecursoFeedbackControle.lista}" id="listaLog"
                             widgetVar="tabelaAtual3"
                             emptyMessage="#{msg['tabela.semregistros']}"
                             style="margin: 0"
                             paginator="true" rowsPerPageTemplate="5,10,20,50,100,200" rows="10"
                             paginatorPosition="bottom"
                             styleClass="tabelaPrincipal">
                    <p:column headerText="ID" style="text-align: center">
                        <h:outputText value="#{item.id}"/>
                    </p:column>

                    <p:column headerText="DT. REGISTRO" style="text-align: center">
                        <h:outputText value="#{item.dataRegistroApresentar}"/>
                    </p:column>

                    <p:column headerText="RECURSO" style="text-align: center">
                        <h:outputText value="#{item.tipoInfoMigracaoApresentar}"/>
                    </p:column>

                    <p:column headerText="CHAVE" style="text-align: center">
                        <h:outputText value="#{item.chave}"/>
                    </p:column>

                    <p:column headerText="USUARIO" style="text-align: center">
                        <h:outputText value="#{item.username}"/>
                    </p:column>

                    <p:column headerText="TELEFONE" style="text-align: center; display: none">
                        <h:outputText value="#{item.telefone}"/>
                    </p:column>

                    <p:column headerText="EMAIL" style="text-align: center; display: none">
                        <h:outputText value="#{item.email}"/>
                    </p:column>

                    <p:column headerText="FEEDBACK" style="text-align: center">
                        <h:outputText value="#{item.feedback}"/>
                    </p:column>
                </p:dataTable>
            </h:panelGroup>
        </h:panelGroup>
    </ui:define>
    <ui:define name="JS">
        <h:outputScript library="js" name="cadastrosV2.js"/>
    </ui:define>
</ui:decorate>
</html>
