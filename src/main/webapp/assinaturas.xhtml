<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="conteudo">
        <style>
            @media (max-width: 1400px) {
                .container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
                    width: 100% !important;
                }
            }

            .marginTop {
                margin-top: 10px;
            }

            .coluna1 {
                margin: 20px;
            }

            .coluna2 {
                margin: 20px;
            }

            .grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 30px;
            }

            .ui-fileupload-content {
                display: none;
            }

            .ui-fileupload-buttonbar {
                background: #f0eded;
            }

            .noHeader.ui-datatable table thead tr {
                display: none;
            }

            .ui-autocomplete-token-icon.ui-icon.ui-icon-close {
                background-image: url("imagens/icons.png");
                margin-right: 10px;
            }

            .ui-autocomplete-token-label {
                background-color: #363636;
                background-image: none;
                color: white;
                text-transform: lowercase;
                box-shadow: none;
                border: none;
                padding: 13.5px 30px 13.5px 10px;
                border-radius: 3px;
            }


            .ui-autocomplete-input-token > input[type="text"] {
                border: none !important;
            }

            .ui-autocomplete-multiple-container.ui-inputfield {
                width: 90% !important;
            }

            .chosen-container-multi .chosen-choices li.search-choice .search-choice-close {
                top: 0;
                right: 0;
                height: 100%;
                width: 100%;
                background: none;
                color: #cbcbcb;
            }

            .chosen-container-multi .chosen-choices li.search-choice .search-choice-close:hover {
                color: white;
            }

            .chosen-container-multi .chosen-choices li.search-choice .search-choice-close::before {
                font-family: FontAwesome;
                font-size: 20px;
                content: "\f00D";
                position: absolute;
                top: 13px;
                right: 6px;
            }

            td[role="gridcell"] {
                word-break: break-all;
                word-wrap: break-word;
            }

            .ui-selectmanycheckbox label, .ui-selectoneradio label {
                font-size: 10px;
            }

            .ui-datatable-data > tr > td > a {
                font-size: 12px !important;
                font-weight: bold;
            }

            .styleBlue .ui-state-default {
                background-color: #3258CB;
                background: #3258CB;
                border: 1px solid #5688D5;
                color: #FFFFFF;
                font-weight: normal;
            }

            .w30 {
                width: 30%;
            }

            .w70 {
                width: 70%;
            }
        </style>

        <div class="noSlider">
            <h:panelGroup layout="block" styleClass="span3 menuLateral" id="addEdit"
                          pt:data-spy="affix" pt:data-offset-top="0"
                          style="width: 100%; background-color: #F0EDED; position: unset;">
                <h4>Assinaturas - Personal Fit</h4>


                <h:panelGroup layout="block" id="groupAddEdit" style="padding: 10px">


                    <ui:repeat value="#{ConfigAssinaturaControle.configs}" var="config">

                        <div style="width: calc(100% - 20px); border-top: 1px solid #ccc; margin-top: 5px; padding: 10px">
                            <h:panelGrid style="width: 100%;" columns="2" columnClasses="w70,w30">

                                <h:panelGrid styleClass="grid" columns="1">


                                    <h:panelGroup layout="block" rendered="#{config.id != null and config.id > 0}">
                                        <p:outputLabel value="ID: #{config.id}"
                                                       style="font-weight: bold"/>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block">
                                        <p:outputLabel value="Tipo"
                                                       style="font-weight: bold"/>
                                        <h:selectOneMenu disabled="#{config.id != null and config.id > 0}"
                                                         style="min-width: 250px" value="#{config.tipo}">
                                            <f:selectItems value="#{ConfigAssinaturaControle.cobrancas}"/>
                                            <p:ajax event="change"
                                                    update=":fmLay:groupAddEdit"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>


                                    <h:panelGroup layout="block" id="descricao">
                                        <p:outputLabel value="Descrição"
                                                       style="font-weight: bold"/>
                                        <h:inputText style="margin-top: 0; min-width: 500px;" maxlength="100" size="100"
                                                     value="#{config.descricao}"
                                                     pt:placeholder="Descrição">
                                        </h:inputText>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" id="panelNome" rendered="#{config.tipo eq 'pacto'}">
                                        <p:outputLabel value="Chave do banco de cobrança"
                                                       style="font-weight: bold"/>
                                        <h:inputText style="margin-top: 0; min-width: 500px;" maxlength="100" size="100"
                                                     value="#{config.chave}"
                                                     pt:placeholder="Chave do banco de cobrança">
                                            <p:ajax event="change"
                                                    listener="#{ConfigAssinaturaControle.alterarChave(config)}"
                                                    update=":fmLay:groupAddEdit"/>
                                        </h:inputText>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" id="panelApiKey" rendered="#{config.tipo eq 'stripe'}">
                                        <p:outputLabel value="Api Key"
                                                       style="font-weight: bold"/>
                                        <h:inputText style="margin-top: 0; min-width: 500px;"
                                                     value="#{config.apiKeyStripe}"
                                                     pt:placeholder="Api Key">
                                            <p:ajax event="change"
                                                    listener="#{ConfigAssinaturaControle.alterarPrice(config)}"
                                                    update=":fmLay:groupAddEdit"/>
                                        </h:inputText>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" id="panelpriceKeyStripe" rendered="#{config.tipo eq 'stripe'}">
                                        <p:outputLabel value="Price Key"
                                                       style="font-weight: bold"/>
                                        <h:inputText style="margin-top: 0; min-width: 500px;" maxlength="100" size="100"
                                                     value="#{config.priceKeyStripe}"
                                                     pt:placeholder="Price Key (chave do produto)">
                                            <p:ajax event="change"
                                                    listener="#{ConfigAssinaturaControle.alterarPrice(config)}"
                                                    update=":fmLay:groupAddEdit"/>
                                        </h:inputText>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" id="panelApiKeyTeste" rendered="#{config.tipo eq 'stripe'}">
                                        <p:outputLabel value="Dias de teste"
                                                       style="font-weight: bold"/>
                                        <h:inputText style="margin-top: 0; min-width: 50px;" maxlength="2" size="2"
                                                     value="#{config.diasTeste}">
                                        </h:inputText>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" id="panelEmpresas" rendered="#{config.tipo eq 'pacto'}">
                                        <p:outputLabel value="Empresa do banco"
                                                       style="font-weight: bold"/>
                                        <h:selectOneMenu style="min-width: 500px" value="#{config.empresa}">
                                            <f:selectItems value="#{config.empresas}"/>
                                            <p:ajax event="change"
                                                    listener="#{ConfigAssinaturaControle.selecionarEmpresa(config)}"
                                                    update=":fmLay:groupAddEdit"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" id="panelPlanos" rendered="#{config.tipo eq 'pacto'}">
                                        <p:outputLabel value="Plano das cobranças"
                                                       style="font-weight: bold"/>
                                        <h:selectOneMenu style="min-width: 500px" value="#{config.plano}">
                                            <f:selectItems value="#{config.planos}"/>
                                            <p:ajax event="change"
                                                    listener="#{ConfigAssinaturaControle.selecionarPlano(config)}"
                                                    update=":fmLay:groupAddEdit"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" id="urldash">
                                        <p:outputLabel value="URL Dash"
                                                       style="font-weight: bold"/>
                                        <h:inputText style="margin-top: 0; min-width: 500px;" maxlength="100" size="100"
                                                     value="#{config.urlDash}"
                                                     pt:placeholder="URL Dash">
                                            <p:ajax event="change"
                                                    update=":fmLay:groupAddEdit"/>
                                        </h:inputText>

                                        <h:outputLink target="_blank" style="margin-top: 6px;"
                                                      value="#{config.urlDash}" rendered="#{not empty config.urlDash}">
                                            <span title="Ir" style="font-size: 8px;"
                                                  class="label label-info">Ver dash</span>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGrid>

                                <h:panelGrid columns="1" width="100%">

                                    <h:panelGroup layout="block">
                                        <div  style="text-align: right">
                                            <p:commandLink styleClass="btn btn-secundary"
                                                           rendered="#{config.id eq null or config.id == 0}"
                                                           action="#{ConfigAssinaturaControle.remover(config)}"
                                                           update=":fmLay:groupAddEdit"
                                                           ajax="true">
                                                Remover
                                            </p:commandLink>
                                            <p:commandLink styleClass="btn btn-secundary"
                                                           rendered="#{config.id != null and config.id > 0}"
                                                           action="#{ConfigAssinaturaControle.desativar(config)}"
                                                           update=":fmLay:groupAddEdit"
                                                           ajax="true">
                                                Desativar
                                            </p:commandLink>
                                        </div>

                                    </h:panelGroup>


                                    <h:panelGroup layout="block" id="panelPrimeira"
                                                  rendered="#{config.planoSelecionado ne null}">
                                        <h:outputText value="Primeira cobrança"
                                                      style="font-weight: bold"/>
                                        <h:outputText
                                                style="display: block; font-size: 24px; margin-top: 10px; margin-bottom: 10px;"
                                                value="R$ #{config.planoSelecionado.primeiraParcelaStr}"/>
                                    </h:panelGroup>


                                    <h:panelGroup layout="block" id="moeda"
                                                  rendered="#{config.moeda ne null}">
                                        <h:outputText value="Moeda"
                                                      style="font-weight: bold"/>
                                        <h:outputText
                                                style="display: block; font-size: 24px; margin-top: 10px; margin-bottom: 10px;"
                                                value="#{config.moeda}"/>
                                    </h:panelGroup>


                                    <h:panelGroup layout="block" id="mensalidade"
                                                  rendered="#{config.mensalidade ne null}">
                                        <h:outputText value="Mensalidade"
                                                      style="font-weight: bold"/>
                                        <h:outputText
                                                style="display: block; font-size: 24px; margin-top: 10px; margin-bottom: 10px;"
                                                value="#{config.mensalidadeStr}"/>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" id="panelMensalidade"
                                                  rendered="#{config.planoSelecionado ne null}">
                                        <h:outputText value="Mensalidade"
                                                      style="font-weight: bold; margin-top: 10px; display: block;"/>
                                        <h:outputText
                                                style="display: block; font-size: 24px; margin-top: 10px; margin-bottom: 20px;"
                                                value="R$ #{config.planoSelecionado.mensalidadeStr}"/>
                                    </h:panelGroup>
                                </h:panelGrid>


                            </h:panelGrid>
                        </div>
                    </ui:repeat>

                    <div style="text-align: center">
                        <p:commandLink styleClass="btn btn-primary"
                                       action="#{ConfigAssinaturaControle.gravar}"
                                       update=":fmLay:groupAddEdit"
                                       ajax="true">
                            <i class="fa-icon-save"/> Salvar alterações
                        </p:commandLink>

                        <p:commandLink styleClass="btn btn-secundary"
                                       action="#{ConfigAssinaturaControle.add}"
                                       update=":fmLay:groupAddEdit"
                                       ajax="true">
                            <i class="fa-icon-plus"/> Adicionar nova configuração
                        </p:commandLink>


                    </div>

                </h:panelGroup>

            </h:panelGroup>
        </div>
    </ui:define>
    <ui:define name="JS">
        <h:outputScript library="js" name="cadastrosV2.js"/>
    </ui:define>
</ui:decorate>
</html>
