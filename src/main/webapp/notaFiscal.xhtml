<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="conteudo">
        <style>
            @media (max-width: 1400px) {
                .container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
                    width: 100% !important;
                }
            }

            @media (min-width: 1200px) {
                .container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
                    width: 80%;
                }
            }

            .conteudoGeralClass {
                width: 95% !important;
            }
        </style>

        <h:panelGroup layout="block" id="testePanelNotaFiscal"
                      style="padding: 0;">
            <h:panelGroup styleClass="span10 caixaPrincipal" id="filtros"
                          layout="block" style="width: 100%; margin: 0 0 10px 0;">
                <h4 style="margin-left: 15px">Filtros</h4>

                <h:panelGroup layout="block" id="panelFiltros1"
                              style="display: flex">
                    <h:panelGroup layout="block" id="filtroEmpresa"
                                  style="padding-left: 15px">
                        <p:outputLabel value="Empresa" style="font-weight: bold"/>
                        <h:selectOneMenu value="#{NotaFiscalControle.empresaIdEnotas}">
                            <f:selectItems value="#{NotaFiscalControle.selectItemEmpresas}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="filtroDataInicio"
                                  style="padding-left: 15px">
                        <p:outputLabel value="Período" style="font-weight: bold"/>
                        <h:panelGroup layout="block" id="panelDatasFiltro" style="display: flex;">
                            <p:calendar id="inicio" value="#{NotaFiscalControle.inicio}"
                                        size="80"
                                        styleClass="calendarioNotaFiscal"
                                        pattern="dd/MM/yyyy" mask="true"
                                        showButtonPanel="true" placeholder="Data Inicial" navigator="true"/>
                            <p:outputLabel value="até"
                                           style="padding-left: 5px; padding-right: 5px; margin-top: 10px;"/>
                            <p:calendar id="fim" value="#{NotaFiscalControle.fim}"
                                        size="80"
                                        styleClass="calendarioNotaFiscal"
                                        pattern="dd/MM/yyyy" mask="true"
                                        showButtonPanel="true" placeholder="Data Final" navigator="true"/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="filtroSituacao"
                                  style="padding-left: 15px">
                        <p:outputLabel value="Situação" style="font-weight: bold"/>
                        <h:selectOneMenu value="#{NotaFiscalControle.situacao}">
                            <f:selectItems value="#{NotaFiscalControle.selectItemStatus}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="filtroSincronizar"
                                  style="padding-left: 15px; display: flex; align-items: center; padding-top: 17px;">
                        <h:panelGroup layout="block" id="divSituacao" style="display: flex">
                            <p:selectBooleanCheckbox value="#{NotaFiscalControle.sincronizar}"/>
                            <p:outputLabel value="Sincronizar com Enotas" style="font-weight: bold; padding-left: 5px"/>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block" id="panelFiltros2"
                              style="display: flex">
                    <h:panelGroup layout="block" id="filtroNome"
                                  style="padding-left: 15px">
                        <p:outputLabel value="Nome Cliente" style="font-weight: bold"/>
                        <p:inputText value="#{NotaFiscalControle.nome}"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="filtroDocumento"
                                  style="padding-left: 15px">
                        <p:outputLabel value="CPF/CNPJ Cliente" style="font-weight: bold"/>
                        <p:inputText value="#{NotaFiscalControle.documento}"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="filtroNumero"
                                  style="padding-left: 15px">
                        <p:outputLabel value="Nº Nota" style="font-weight: bold"/>
                        <p:inputText value="#{NotaFiscalControle.numeroNota}"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="filtroConsultar"
                                  style="padding-left: 15px">
                        <p:commandLink styleClass="btn btn-primary"
                                       style="margin-top: 22px;"
                                       update=":fmLay:lista"
                                       action="#{NotaFiscalControle.consultar}">
                            Consultar
                        </p:commandLink>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup styleClass="span10 caixaPrincipal" id="dados"
                          layout="block"
                          style="width: 100%; padding: 0px; margin: 0px;">
                <h4>Notas Fiscais</h4>

                <p:dataTable var="nota"
                             value="#{NotaFiscalControle.lista}" id="lista"
                             widgetVar="tabelaAtual"
                             emptyMessage="#{msg['tabela.semregistros']}"
                             style="margin: 0"
                             styleClass="tabelaPrincipal">

                    <p:column headerText="Nome" style="padding-left: 25px;">
                        <h:outputText value="#{nota.cliente}"/>
                    </p:column>

                    <p:column headerText="CPF/CNPJ" style="text-align: center">
                        <h:outputText value="#{nota.clienteDocumentoApresentar}"/>
                    </p:column>

                    <p:column headerText="Dt. Registro" style="text-align: center">
                        <h:outputText value="#{nota.dataCriacaoApresentar}"/>
                    </p:column>

                    <p:column headerText="Situação" style="text-align: center">
                        <h:outputText value="#{nota.situacao}"/>
                    </p:column>

                    <p:column headerText="Série" style="text-align: center">
                        <h:outputText value="#{nota.serieRps}"/>
                    </p:column>

                    <p:column headerText="Nº Nota" style="text-align: center">
                        <h:outputText value="#{nota.numero}"/>
                    </p:column>

                    <p:column headerText="Autorização" style="text-align: center">
                        <h:outputText value="#{nota.codigoVerificacao}"/>
                    </p:column>

                    <p:column headerText="Valor" style="text-align: center">
                        <h:outputText value="#{nota.valorTotalApresentar}"/>
                    </p:column>

                    <p:column headerText="Arquivos" style="text-align: center">
                        <h:panelGroup layout="block" style="display: inline-flex">
                            <h:outputLink target="_blank"
                                          rendered="#{not empty nota.linkDownloadPDF}"
                                          value="#{nota.linkDownloadPDF}">
                                <p:graphicImage cache="true" library="svg" name="file-pdf.svg" style="width: 20px;"/>
                            </h:outputLink>
                            <h:outputLink target="_blank"
                                          style="padding-left: 10px"
                                          rendered="#{not empty nota.linkDownloadXML}"
                                          value="#{nota.linkDownloadXML}">
                                <p:graphicImage cache="true" library="svg" name="file-xml.svg" style="width: 20px;"/>
                            </h:outputLink>
                        </h:panelGroup>
                    </p:column>

                </p:dataTable>
            </h:panelGroup>
        </h:panelGroup>
    </ui:define>
    <ui:define name="JS">
        <h:outputScript library="js" name="cadastrosV2.js"/>
    </ui:define>
</ui:decorate>
</html>
