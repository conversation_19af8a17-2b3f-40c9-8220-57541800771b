<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
<ui:decorate template="/template/layout.xhtml">

    <ui:define name="conteudo">

        <h:outputScript library="js" name="redeEmpresa.js" target="head"/>

        <style>
            @media (max-width: 1400px) {
                .container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
                    width: 100% !important;
                }
            }

        </style>

        <ui:include src="include_modais_dcc.xhtml"/>

        <div id="divRedeEmpresa" class="container-fluid noSlider">

            <h:panelGroup layout="block" styleClass="span6 menuLateral" id="addEdit"
                          pt:data-spy="affix" pt:data-offset-top="0"
                          style="top: 0; position: absolute; display: table;padding: 0 16px 16px 16px;">

                <h4 style="margin-left: 0; margin-bottom: 20px;">Rede de Empresa</h4>

                <h:panelGroup id="groupAddEdit" style="width: 100%">
                    <h:panelGrid columns="2" width="100%">
                        <h:outputText value="Nome da Rede:"/>
                        <h:inputText style="margin-bottom: 0; width: 400px;"
                                     value="#{RedeEmpresaControle.redeEmpresa.nome}"/>
                        <h:outputText value="Chave da Rede: "/>
                        <h:inputText style="margin-bottom: 0; width: 400px;" readonly="true"
                                     value="#{RedeEmpresaControle.redeEmpresa.chaverede}"/>

                        <h:outputLabel value="Empresas da Rede"/>
                        <h:panelGroup layout="block">
                            <div class="internoMenuLateral" style="width: 100%">
                                <p:autoComplete id="idEmpresasDaRede" multiple="true"
                                                style="width: 100%;" disabled="true"
                                                value="#{RedeEmpresaControle.empresasSelecionadas}"
                                                completeMethod="#{RedeEmpresaControle.buscarNomeEmpresas}"
                                                maxResults="10"/>
                            </div>
                            <h:outputText id="totalEmp"
                                          value="#{fn:length(RedeEmpresaControle.empresasSelecionadas)} Empresas"/>
                        </h:panelGroup>

                        <h:outputLabel value="Crédito de DCC por rede"
                                       rendered="#{RedeEmpresaControle.redeEmpresaSelecionada and !RedeEmpresaControle.redeEmpresaSelecionadaCobrancaMensal}"/>
                        <h:panelGroup layout="block"
                                      rendered="#{RedeEmpresaControle.redeEmpresaSelecionada and !RedeEmpresaControle.redeEmpresaSelecionadaCobrancaMensal}">
                            <p:selectBooleanCheckbox value="#{RedeEmpresaControle.redeEmpresa.creditoPorRede}"/>
                        </h:panelGroup>

                        <h:outputText value="Chave Franqueadora: " title="Essa chave é para identificar em qual banco principal da franqueadora para ter os WODs e Modelos de Mensagem da rede"/>
                        <h:inputText style="margin-bottom: 0; width: 400px;"
                                     value="#{RedeEmpresaControle.redeEmpresa.chaveFranqueadora}"/>

                        <h:outputText value="Cód. Unidade Franqueadora: "
                                      title="Esse código é o identificdor da empresa no banco principal, que indica qual é a unidade principal."/>
                        <p:inputText style="margin-bottom: 0; width: 400px;"
                                     value="#{RedeEmpresaControle.redeEmpresa.codigoUnidadeFranqueadora}"
                                     maxlength="3" onkeyup="$(this).val($(this).val().replace(/[^0-9]/g, ''));"/>

                        <h:outputLabel value="Módulo Gestão de Redes"/>
                        <h:panelGroup layout="block">
                            <p:selectBooleanCheckbox value="#{RedeEmpresaControle.redeEmpresa.gestaoRedes}">
                                <p:ajax event="change" update=":fmLay:groupAddEdit" />
                            </p:selectBooleanCheckbox>
                        </h:panelGroup>

                        <h:outputLabel value="Chave de Testes" rendered="#{RedeEmpresaControle.redeEmpresaSelecionada and RedeEmpresaControle.redeEmpresa.gestaoRedes}"/>
                        <h:panelGroup layout="block" rendered="#{RedeEmpresaControle.redeEmpresaSelecionada and RedeEmpresaControle.redeEmpresa.gestaoRedes}">
                            <h:inputText style="margin-bottom: 0; width: 400px;" value="#{RedeEmpresaControle.redeEmpresa.chaveTeste}"/>
                        </h:panelGroup>

                        <h:outputLabel value="Chave de Estudo" rendered="#{RedeEmpresaControle.redeEmpresaSelecionada and RedeEmpresaControle.redeEmpresa.gestaoRedes}"/>
                        <h:panelGroup layout="block" rendered="#{RedeEmpresaControle.redeEmpresaSelecionada and RedeEmpresaControle.redeEmpresa.gestaoRedes}">
                            <h:inputText style="margin-bottom: 0; width: 400px;" value="#{RedeEmpresaControle.redeEmpresa.chaveEstudo}"/>
                        </h:panelGroup>

                        <h:outputText value="Chave banco Integração Evo: " title="É a chave do banco da Integração Evo, onde serão sincronizados os alunos do Evo, para gerar autorização de acesso e, consequentemente, possibilitar que eles acessem as unidades que estão na Pacto."/>
                        <h:inputText style="margin-bottom: 0; width: 400px;"
                                     value="#{RedeEmpresaControle.redeEmpresa.chaveBDIntegracaoEvo}"/>

                        <h:outputLabel value="URL Servidor Biometrico Unificado" rendered="#{RedeEmpresaControle.redeEmpresaSelecionada and RedeEmpresaControle.redeEmpresa.gestaoRedes}"/>
                        <h:panelGroup layout="block" rendered="#{RedeEmpresaControle.redeEmpresaSelecionada and RedeEmpresaControle.redeEmpresa.gestaoRedes}">
                            <h:inputText style="margin-bottom: 0; width: 400px;" value="#{RedeEmpresaControle.redeEmpresa.urlServidorBiometricoUnificado}"/>
                        </h:panelGroup>

                        <h:outputLabel value="Configurações do Treino na Franqueadora"
                                       rendered="#{RedeEmpresaControle.redeEmpresaSelecionada and RedeEmpresaControle.redeEmpresa.gestaoRedes}"/>
                        <h:panelGroup layout="block"
                                      rendered="#{RedeEmpresaControle.redeEmpresaSelecionada and RedeEmpresaControle.redeEmpresa.gestaoRedes}">
                            <p:selectBooleanCheckbox value="#{RedeEmpresaControle.redeEmpresa.configTreinoFranqueadora}">
                            </p:selectBooleanCheckbox>
                        </h:panelGroup>

                        <h:outputLabel value="Programas predefinidos na Franqueadora"
                                       rendered="#{RedeEmpresaControle.redeEmpresaSelecionada and RedeEmpresaControle.redeEmpresa.gestaoRedes}"/>
                        <h:panelGroup layout="block"
                                      rendered="#{RedeEmpresaControle.redeEmpresaSelecionada and RedeEmpresaControle.redeEmpresa.gestaoRedes}">
                            <p:selectBooleanCheckbox value="#{RedeEmpresaControle.redeEmpresa.treinoPredefinidoFranqueadora}">
                            </p:selectBooleanCheckbox>
                        </h:panelGroup>

                        <h:outputLabel value="Sincronizar clientes rede empresa na franqueadora (API consulta clientes por cpf)"
                                       rendered="#{RedeEmpresaControle.redeEmpresaSelecionada and RedeEmpresaControle.redeEmpresa.gestaoRedes}"/>
                        <h:panelGroup layout="block"
                                      rendered="#{RedeEmpresaControle.redeEmpresaSelecionada and RedeEmpresaControle.redeEmpresa.gestaoRedes}">
                            <p:selectBooleanCheckbox value="#{RedeEmpresaControle.redeEmpresa.sincronizarClientesNaFranqueadora}">
                            </p:selectBooleanCheckbox>
                        </h:panelGroup>

                        <h:outputLabel value="Empresa p/ gerar cobrança ao config. cobrança automática"
                                       rendered="#{RedeEmpresaControle.redeEmpresaSelecionada and !RedeEmpresaControle.redeEmpresaSelecionadaCobrancaMensal}"/>
                        <h:panelGroup layout="block"
                                      rendered="#{RedeEmpresaControle.redeEmpresaSelecionada and !RedeEmpresaControle.redeEmpresaSelecionadaCobrancaMensal}">
                            <h:selectOneMenu style="width: 100%;"
                                             value="#{RedeEmpresaControle.redeEmpresa.chaveCobrancaDCC}">
                                <f:selectItems value="#{RedeEmpresaControle.empresasRede}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputLabel for="listaEmpresasRede"
                                       value="Empresas p/ gerar cobrança ao adicionar créditos manual"
                                       rendered="#{RedeEmpresaControle.redeEmpresaSelecionada and !RedeEmpresaControle.redeEmpresaSelecionadaCobrancaMensal}"/>
                        <h:panelGroup layout="block"
                                      rendered="#{RedeEmpresaControle.redeEmpresaSelecionada and !RedeEmpresaControle.redeEmpresaSelecionadaCobrancaMensal}">
                            <p:selectCheckboxMenu style="width: 100%;" id="listaEmpresasRede"
                                                  value="#{RedeEmpresaControle.redeEmpresa.listaChavesEmpresaRede}"
                                                  label="Selecione as empresas" multiple="true">
                                <f:selectItems value="#{RedeEmpresaControle.empresasRede}"/>
                            </p:selectCheckboxMenu>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGroup>

                <p:tabView id="tabFuncoes"
                           rendered="#{RedeEmpresaControle.redeEmpresaSelecionada}">
                    <p:tab title="DCC">
                        <h:panelGroup id="dcc">
                            <h:panelGroup layout="block" style="display: flex;"
                                          rendered="#{!RedeEmpresaControle.redeEmpresaSelecionadaCobrancaMensal}">
                                <h:outputLabel style="font-weight: bold" value="Créditos: #{RedeEmpresaControle.redeEmpresa.creditos}"/>
                            </h:panelGroup>
                            <h:panelGroup layout="block"
                                          rendered="#{!RedeEmpresaControle.redeEmpresaSelecionadaCobrancaMensal}"
                                          style="display: inline-block; margin-top: 15px">
                                <p:commandLink style="display: inline-block;"
                                               styleClass=" btn btn-secundary"
                                               action="#{OamdControle.addCreditoRede(RedeEmpresaControle.redeEmpresa)}"
                                               update=":fmLay:modalCobrancaPacto"
                                               oncomplete="modalDialogCobrancaPacto.show();">
                                    <i class="fa-icon-plus"></i> Adicionar
                                </p:commandLink>

                                <p:commandLink style="margin-left: 10px; display: inline-block;"
                                               styleClass=" btn btn-secundary"
                                               action="#{OamdControle.configurarCreditoRede(RedeEmpresaControle.redeEmpresa)}"
                                               update=":fmLay:modalCobrancaConfig"
                                               oncomplete="modalDialogCobrancaConfig.show();">
                                    <i class="fa-icon-cog"></i> Configurar
                                </p:commandLink>
                            </h:panelGroup>
                            <h:panelGroup layout="block"
                                          style="margin-top: 15px">
                                <p:commandLink style="display: inline-block;"
                                               styleClass=" btn btn-secundary"
                                               action="#{CreditoPactoConfigControle.configurarRedeEmpresaMensal(RedeEmpresaControle.redeEmpresa)}"
                                               update=":fmLay:modalCobrancaMensalRede"
                                               oncomplete="modalDialogCobrancaMensalRede.show();">
                                    <i class="fa-icon-cog"></i> Configurar Pós-Pago Mensal - Rede
                                </p:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                    </p:tab>
                </p:tabView>

                <h:panelGroup id="pnlBotoes" layout="block" style="margin-bottom: 6px; margin-top: 20px"
                              styleClass="pull-right">
                    <p:commandLink styleClass="btn btn-primary"
                                   ajax="true"
                                   update=":fmLay:addEdit :fmLay:dados"
                                   action="#{RedeEmpresaControle.gravar}">
                        <i class="fa-icon-save"/> #{title['cadastros.salvar']}
                    </p:commandLink>

                </h:panelGroup>

            </h:panelGroup>

            <h:panelGroup style="top: 5px;" styleClass="span6 offset6 caixaPrincipal" id="dados">
                <h4>Redes cadastradas</h4>
                <hr/>

                <p:outputPanel layout="block" styleClass="pesq pesq-tabelas buscaAuxiliar" id="filtrar">
                    <p:inputText id="globalFilter" value="#{RedeEmpresaControle.filtro}">
                        <f:ajax event="keyup"
                                listener="#{RedeEmpresaControle.filtrar(RedeEmpresaControle.listaRedeEmpresa)}"
                                delay="#{RedeEmpresaControle.tempoDelay}"
                                render=":fmLay:dados"/>
                    </p:inputText>
                </p:outputPanel>
                <p:dataTable var="rede" value="#{RedeEmpresaControle.listaTela}" id="listaRedesCadastradas"
                             widgetVar="tabelaAtual"
                             emptyMessage="#{msg['tabela.semregistros']}"
                             styleClass="tabelaPrincipal">
                    <f:facet name="header">
                        <h:panelGroup layout="block" id="totalTabela" styleClass="totalizaTabela">
                            <h:outputText value="#{fn:length(RedeEmpresaControle.listaTela)} redes"/>
                        </h:panelGroup>
                    </f:facet>
                    <p:column headerText="Nome" style="text-align: left">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{RedeEmpresaControle.editar(rede)}"
                                       update=":fmLay:addEdit">#{rede.nome}</p:commandLink>
                    </p:column>
                    <p:column headerText="Operação" style="text-align: center">
                        <p:commandLink ajax="true"
                                       action="#{RedeEmpresaControle.excluir(rede)}"
                                       onclick="if (! confirm('Tem certeza que deseja excluirPlano a rede de empresa? ')) return false;"
                                       update=":fmLay:addEdit :fmLay:dados">
                            Excluir</p:commandLink>
                    </p:column>
                </p:dataTable>
            </h:panelGroup>

        </div>


    </ui:define>
    <ui:define name="JS">
        <h:outputScript library="js" name="cadastrosV2.js"/>
    </ui:define>
</ui:decorate>
</html>
