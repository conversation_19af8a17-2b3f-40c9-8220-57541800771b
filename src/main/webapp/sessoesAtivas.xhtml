<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
<ui:decorate template="/template/layout.xhtml">

    <ui:define name="conteudo">
        <style type="text/css">
            .ui-column-filter {
                display: block !important;
            }

            input.ui-inputfield {
                color: #000;
                width: 90% !important;
                height: 10px !important;
                font-size: 0.7em !important;
            }

            .tabelaPrincipal tbody {
                font-size: 0.7em !important;
            }

        </style>


        <h:panelGroup styleClass="span9 offset3 caixaPrincipal" id="dados" style="padding: 20px; width: 90%; margin-left: 3%;">
                <h4>Sessões Ativas</h4>
                <hr/>
                <h:panelGroup layout="block" styleClass="pull-right" style="margin: 0 20px">
                    <h:commandButton styleClass="btn btn-primary" action="#{SessaoControle.consultarSessoes}" value="Atualizar"/>
                </h:panelGroup>
                <p:dataTable id="lista" value="#{SessaoControle.listaTela}" var="sessao" widgetVar="tabelaAtual"
                             style="width: 100%" rowIndexVar="row"
                             emptyMessage="#{msg['tabela.semregistros']}" styleClass="tabelaPrincipal">
                    <f:facet name="header">
                        <h:panelGroup layout="block" id="totalSessoes" styleClass="totalizaTabela">
                            <h:outputText value="Sessões: #{fn:length(SessaoControle.listaTela)} "/>
                        </h:panelGroup>
                        <h:panelGroup layout="block" id="totalConexoes" styleClass="totalizaTabela" style="margin-top: 18px">
                            <h:outputText value="Conexões: #{SessaoControle.conexoes} "/>
                        </h:panelGroup>
                    </f:facet>
                    <p:column headerText="#" style="width:3%; text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true">#{row+1}</p:commandLink>
                    </p:column>
                    <p:column headerText="Instância" filterBy="#{sessao.instancia}" sortBy="#{sessao.instancia}">
                        <p:commandLink ajax="true" partialSubmit="true">#{sessao.instancia}</p:commandLink>
                    </p:column>
                    <p:column headerText="Empresa" filterBy="#{sessao.empresa}" sortBy="#{sessao.empresa}">
                        <p:commandLink ajax="true" partialSubmit="true">#{sessao.empresa}</p:commandLink>
                    </p:column>
                    <p:column headerText="Usuário" filterBy="#{sessao.usuario}" sortBy="#{sessao.usuario}">
                        <p:commandLink ajax="true" partialSubmit="true">#{sessao.usuario}</p:commandLink>
                    </p:column>
                    <p:column headerText="IP" filterBy="#{sessao.ip}" sortBy="#{sessao.ip}">
                        <p:commandLink ajax="true" partialSubmit="true">#{sessao.ip}</p:commandLink>
                    </p:column>
                    <p:column headerText="Processo" filterBy="#{sessao.procId}" sortBy="#{sessao.procId}" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true">#{sessao.procId}</p:commandLink>
                    </p:column>
                    <p:column headerText="Dt. Criação" filterBy="#{sessao.dtCriacaoApresentar}"
                              sortBy="#{sessao.dtCriacaoApresentar}">
                        <p:commandLink ajax="true" partialSubmit="true">#{sessao.dtCriacaoApresentar}</p:commandLink>
                    </p:column>
                    <p:column headerText="Últ. Acesso" filterBy="#{sessao.ultAcessoApresentar}"
                              sortBy="#{sessao.ultAcessoApresentar}">
                        <p:commandLink ajax="true" partialSubmit="true">#{sessao.ultAcessoApresentar}</p:commandLink>
                    </p:column>
                    <p:column headerText="Last URI" filterBy="#{sessao.lastURI}" sortBy="#{sessao.lastURI}">
                        <p:commandLink ajax="true" partialSubmit="true">#{sessao.lastURI}</p:commandLink>
                    </p:column>
                    <p:column headerText="ID" filterBy="#{sessao.id}" sortBy="#{sessao.id}" style="width:16%; text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true">#{sessao.id}</p:commandLink>
                    </p:column>
                    <p:column headerText="Browser" filterBy="#{sessao.browser}" sortBy="#{sessao.browser}" style="width:15%">
                        <p:commandLink ajax="true" partialSubmit="true">#{sessao.browser}</p:commandLink>
                    </p:column>
                </p:dataTable>
            </h:panelGroup>

    </ui:define>
    <ui:define name="JS">

    </ui:define>
</ui:decorate>
</html>
