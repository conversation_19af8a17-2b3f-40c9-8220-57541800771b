<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:p="http://primefaces.org/ui"
>
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="CSS">
        <style>
            .ui-autocomplete-token-icon.ui-icon.ui-icon-close {
                background-image: url("imagens/icons.png");
                margin-right: 10px;
            }

            .ui-autocomplete-token-label {
                background-color: #363636;
                background-image: none;
                color: white;
                text-transform: lowercase;
                box-shadow: none;
                border: none;
                padding: 13.5px 30px 13.5px 10px;
                border-radius: 3px;
            }


            .ui-autocomplete-input-token > input[type="text"] {
                border: none !important;
            }

            .ui-autocomplete-multiple-container.ui-inputfield {
                width: 90% !important;
            }

            .chosen-container-multi .chosen-choices li.search-choice .search-choice-close {
                top: 0;
                right: 0;
                height: 100%;
                width: 100%;
                background: none;
                color: #cbcbcb;
            }

            .chosen-container-multi .chosen-choices li.search-choice .search-choice-close:hover {
                color: white;
            }

            .chosen-container-multi .chosen-choices li.search-choice .search-choice-close::before {
                font-family: FontAwesome;
                font-size: 20px;
                content: "\f00D";
                position: absolute;
                top: 13px;
                right: 6px;
            }

            td[role="gridcell"] {
                word-break: break-all;
                word-wrap: break-word;
            }

            .ui-selectmanycheckbox label, .ui-selectoneradio label {
                font-size: 10px;
            }

            .ui-datatable-data > tr > td > a {
                font-size: 12px !important;
                font-weight: bold;
            }

            .styleBlue .ui-state-default {
                background-color: #3258CB;
                background: #3258CB;
                border: 1px solid #5688D5;
                color: #FFFFFF;
                font-weight: normal;
            }
        </style>
    </ui:define>


    <ui:define name="conteudo">
        <h:panelGroup layout="block" styleClass="container-fluid noSlider" style="width: 100%;">
            <h:panelGroup layout="block" id="dados" style="width: 100%; margin-left: auto; margin-right: auto;"
                          styleClass="span12 caixaPrincipal">
                <h4>Troca de Senha</h4>
                <hr/>

                <h:panelGroup id="pnlResultado" layout="block" style="text-align: center">
                    <h4 class="text-center">
                        <h:outputText value="#{UsuarioControle.usuarioSelecionado.userName}"/>
                    </h4>
                    <h:panelGroup layout="block" styleClass="input-group">
                            <span class="input-group-addon">
                                <i class="ui-icon-locked"/>
                            </span>
                        <p:password styleClass="form-value" id="pwrAtual" value="#{UsuarioControle.senhaAtual}"
                                    label="Senha Atual"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" styleClass="input-group">
                            <span class="input-group-addon">
                                <i class="fa fa-asterisk"/>
                            </span>
                        <p:password styleClass="form-value" id="pwrNovo" value="#{UsuarioControle.senhaNova}"
                                    label="Senha Atual"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" styleClass="input-group">
                            <span class="input-group-addon">
                                <i class="fa fa-asterisk"/>
                            </span>
                        <p:password styleClass="form-value" id="pwdConfirmar"
                                    value="#{UsuarioControle.senhaConfirmacao}"
                                    label="Senha Atual"/>
                    </h:panelGroup>
                    <p:watermark for="pwrAtual" value="Senha Atual"/>
                    <p:watermark for="pwrNovo" value="Nova Senha"/>
                    <p:watermark for="pwdConfirmar" value="Confirmar Senha"/>
                    <h:panelGroup layout="block" styleClass="modal-footer">
                        <p:commandLink action="#{UsuarioControle.alterarSenha}" styleClass="btn btn-primary"
                                       id="btnTrSenha" ajax="true"
                                       value="Salvar"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>

    </ui:define>
    <ui:define name="JS">
        <h:outputScript library="js" name="cadastrosV2.js"/>
    </ui:define>
</ui:decorate>
</html>
