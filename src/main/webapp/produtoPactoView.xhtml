<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="conteudo">
        <style>
            @media (max-width: 1400px) {
                .container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
                    width: 100% !important;
                }
            }

            .marginTop {
                margin-top: 10px;
            }

            .coluna1 {
                margin: 20px;
            }

            .coluna2 {
                margin: 20px;
            }

            .grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 30px;
            }

            .ui-fileupload-content {
                display: none;
            }

            .ui-fileupload-buttonbar {
                background: #f0eded;
            }

            .noHeader.ui-datatable table thead tr {
                display: none;
            }

            .ui-autocomplete-token-icon.ui-icon.ui-icon-close {
                background-image: url("imagens/icons.png");
                margin-right: 10px;
            }

            .ui-autocomplete-token-label {
                background-color: #363636;
                background-image: none;
                color: white;
                text-transform: lowercase;
                box-shadow: none;
                border: none;
                padding: 13.5px 30px 13.5px 10px;
                border-radius: 3px;
            }


            .ui-autocomplete-input-token > input[type="text"] {
                border: none !important;
            }

            .ui-autocomplete-multiple-container.ui-inputfield {
                width: 90% !important;
            }

            .chosen-container-multi .chosen-choices li.search-choice .search-choice-close {
                top: 0;
                right: 0;
                height: 100%;
                width: 100%;
                background: none;
                color: #cbcbcb;
            }

            .chosen-container-multi .chosen-choices li.search-choice .search-choice-close:hover {
                color: white;
            }

            .chosen-container-multi .chosen-choices li.search-choice .search-choice-close::before {
                font-family: FontAwesome;
                font-size: 20px;
                content: "\f00D";
                position: absolute;
                top: 13px;
                right: 6px;
            }

            td[role="gridcell"] {
                word-break: break-all;
                word-wrap: break-word;
            }

            .ui-selectmanycheckbox label, .ui-selectoneradio label {
                font-size: 10px;
            }

            .ui-datatable-data > tr > td > a {
                font-size: 12px !important;
                font-weight: bold;
            }

            .styleBlue .ui-state-default {
                background-color: #3258CB;
                background: #3258CB;
                border: 1px solid #5688D5;
                color: #FFFFFF;
                font-weight: normal;
            }
        </style>

        <div class="noSlider">
            <h:panelGroup layout="block" styleClass="span3 menuLateral" id="addEdit"
                          pt:data-spy="affix" pt:data-offset-top="0"
                          style="width: 100%; background-color: #F0EDED; position: unset;">
                <h4>Produto Pacto</h4>

                <h:panelGroup layout="block" id="groupAddEdit">
                    <h:panelGroup layout="block" styleClass="grid">
                        <h:panelGroup layout="block" id="coluna1" styleClass="coluna1">

                            <h:panelGroup layout="block" id="panelExplicacaoProdutoPacto">
                                <p:outputLabel value="Informações"
                                               style="font-weight: bold"/>
                                <h:outputLabel style="margin-top: 0;"
                                               value="O sistema de Produto Pacto utiliza um banco de dados do ZillyonWeb
                                                para registrar as vendas realizadas e realizar as cobranças.
                                                As cobranças são realizadas utilizando o serviço do Vendas Online"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelBancoPactoChave">
                                <p:outputLabel value="Chave do banco de Produtos Pacto"
                                               style="font-weight: bold"/>
                                <h:outputLabel style="margin-top: 0;"
                                               value="#{ProdutoPactoControle.configProdutoPacto.chave}"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelBancoPactoEmpresa">
                                <p:outputLabel value="Empresa"
                                               style="font-weight: bold"/>
                                <h:outputLabel style="margin-top: 0;"
                                               value="#{ProdutoPactoControle.empresaSelecionado.nome}"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelTipoProduto">
                                <p:outputLabel value="Tipo produto"
                                               style="font-weight: bold"/>
                                <h:selectOneMenu value="#{ProdutoPactoControle.produtoPacto.tipo}"
                                                 style="margin-top: 0;width: 100%">
                                    <f:selectItems value="#{ProdutoPactoControle.listaTipoProduto}"/>
                                    <p:ajax event="change" listener="#{ProdutoPactoControle.alterarTipoProduto}"
                                            update=":fmLay:coluna1"/>
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelPlanoZW">
                                <p:outputLabel value="Plano"
                                               style="font-weight: bold"/>
                                <h:selectOneMenu value="#{ProdutoPactoControle.produtoPacto.planoZW}"
                                                 style="margin-top: 0;width: 100%">
                                    <f:selectItems value="#{ProdutoPactoControle.listaSelectItemPlano}"/>
                                    <p:ajax event="change" listener="#{ProdutoPactoControle.selecionarPlano}"
                                            update=":fmLay:coluna1"/>
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelProdutoZW">
                                <p:outputLabel value="Produto"
                                               style="font-weight: bold"/>
                                <h:selectOneMenu value="#{ProdutoPactoControle.produtoPacto.produtoZW}"
                                                 style="margin-top: 0;width: 100%">
                                    <f:selectItems value="#{ProdutoPactoControle.listaSelectItemProduto}"/>
                                    <p:ajax event="change" listener="#{ProdutoPactoControle.selecionarProduto}"
                                            update=":fmLay:coluna1"/>
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelNome"
                                          styleClass="marginTop">
                                <p:outputLabel value="Nome do Produto Pacto"
                                               style="font-weight: bold"/>
                                <p:inputText pt:placeholder="Nome que será apresentado no Pacto Store"
                                             maxlength="150"
                                             style="width: 100%"
                                             value="#{ProdutoPactoControle.produtoPacto.nome}"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelProprietario"
                                          styleClass="marginTop">
                                <p:outputLabel value="Proprietário"
                                               style="font-weight: bold"/>
                                <p:inputText pt:placeholder="Proprietário Recurso"
                                             maxlength="150"
                                             value="#{ProdutoPactoControle.produtoPacto.proprietario}"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelAdesao"
                                          rendered="#{ProdutoPactoControle.produtoPacto.planoZW > 0 || ProdutoPactoControle.produtoPacto.produtoZW > 0}"
                                          styleClass="marginTop">
                                <p:outputLabel value="Adesão"
                                               rendered="#{ProdutoPactoControle.produtoPacto.planoZW > 0}"
                                               style="font-weight: bold"/>
                                <p:outputLabel value="Valor"
                                               rendered="#{ProdutoPactoControle.produtoPacto.produtoZW > 0}"
                                               style="font-weight: bold"/>
                                <p:outputLabel style="margin-top: 0;"
                                               value="#{ProdutoPactoControle.produtoPacto.adesaoApresentar}"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelRecorrenteProduto"
                                          styleClass="marginTop"
                                          rendered="#{ProdutoPactoControle.produtoPacto.planoZW > 0}">

                                <h:panelGroup layout="block"
                                              style="margin-top: 10px;">
                                    <p:outputLabel value="Mensalidade"
                                                   style="font-weight: bold"/>
                                    <p:outputLabel style="margin-top: 0;"
                                                   value="#{ProdutoPactoControle.produtoPacto.mensalidadeApresentar}"/>
                                </h:panelGroup>

                                <h:panelGroup layout="block"
                                              style="margin-top: 10px;">
                                    <p:outputLabel value="Fidelidade"
                                                   style="font-weight: bold"/>
                                    <p:outputLabel style="margin-top: 0;"
                                                   rendered="#{ProdutoPactoControle.produtoPacto.fidelidade == 1}"
                                                   value="#{ProdutoPactoControle.produtoPacto.fidelidade} Mês"/>
                                    <p:outputLabel style="margin-top: 0;"
                                                   rendered="#{ProdutoPactoControle.produtoPacto.fidelidade > 1}"
                                                   value="#{ProdutoPactoControle.produtoPacto.fidelidade} Meses"/>
                                </h:panelGroup>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelQtdParcelas"
                                          rendered="#{ProdutoPactoControle.produtoPacto.planoZW > 0 || ProdutoPactoControle.produtoPacto.produtoZW > 0}"
                                          styleClass="marginTop">
                                <h:panelGroup layout="block"
                                              style="margin-top: 10px;">
                                    <p:outputLabel value="Máximo de parcelamento"
                                                   style="font-weight: bold"/>
                                    <p:outputLabel style="margin-top: 0;"
                                                   value="#{ProdutoPactoControle.produtoPacto.maximoParcelas}"/>
                                </h:panelGroup>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelRecorrente"
                                          styleClass="marginTop">
                                <h:panelGroup layout="block"
                                              style="margin-top: 10px;">
                                    <p:outputLabel value="Recorrência"
                                                   style="font-weight: bold"/>
                                    <p:outputLabel style="margin-top: 0;"
                                                   value="#{ProdutoPactoControle.produtoPacto.recorrenteApresentar}"/>
                                </h:panelGroup>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelQtdGeral"
                                          rendered="#{(ProdutoPactoControle.produtoPacto.planoZW > 0 || ProdutoPactoControle.produtoPacto.produtoZW > 0) and
                                          !ProdutoPactoControle.produtoPacto.tipo.modulo and !ProdutoPactoControle.produtoPacto.tipo.reconhecimentoFacial}">

                                <h:panelGroup layout="block"
                                              styleClass="marginTop"
                                              style="display: inline-flex">
                                    <p:selectBooleanCheckbox value="#{ProdutoPactoControle.produtoPacto.qtdFixa}"
                                                             styleClass="comBorda">
                                        <p:ajax event="change" update=":fmLay:panelQtdGeral"/>
                                    </p:selectBooleanCheckbox>
                                    <p:outputLabel value="Quantidade fixa"
                                                   style="font-weight: bold"/>
                                </h:panelGroup>

                                <h:panelGroup layout="block" id="panelQtdFixa"
                                              styleClass="marginTop"
                                              rendered="#{ProdutoPactoControle.produtoPacto.qtdFixa}">
                                    <p:outputLabel value="Quantidade"
                                                   style="font-weight: bold"/>
                                    <p:inputText pt:placeholder="Quantidade"
                                                 maxlength="5"
                                                 onkeypress="mascara(this, somenteNumeros)"
                                                 value="#{ProdutoPactoControle.produtoPacto.qtd}"/>
                                </h:panelGroup>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelValorCredito"
                                          rendered="#{ProdutoPactoControle.produtoPacto.tipo.moduloCobrancaCartao}"
                                          styleClass="marginTop">
                                <p:outputLabel value="Valor Unitário (Crédito - Pós-Pago Efetivado)"
                                               style="font-weight: bold"/>
                                <p:inputText pt:placeholder="Valor unitário"
                                             maxlength="10"
                                             onkeypress="mascara(this, moeda)"
                                             value="#{ProdutoPactoControle.produtoPacto.valorCreditoPacto}"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block"
                                          style="display: grid"
                                          styleClass="marginTop">
                                <h:panelGroup layout="block"
                                              style="display: inline-flex;">
                                    <p:selectBooleanCheckbox value="#{ProdutoPactoControle.produtoPacto.ativo}"
                                                             styleClass="comBorda"/>
                                    <p:outputLabel value="Ativo"
                                                   style="font-weight: bold"/>
                                </h:panelGroup>

                                <h:panelGroup layout="block"
                                              rendered="#{!ProdutoPactoControle.produtoPacto.tipo.modulo and !ProdutoPactoControle.produtoPacto.tipo.email and !ProdutoPactoControle.produtoPacto.tipo.reconhecimentoFacial}"
                                              style="display: inline-flex;">
                                    <p:selectBooleanCheckbox value="#{ProdutoPactoControle.produtoPacto.permiteRepetir}"
                                                             styleClass="comBorda"/>
                                    <p:outputLabel value="Permite que o cliente compre esse produto mais de uma vez"
                                                   style="font-weight: bold"/>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup layout="block" id="coluna2" styleClass="coluna2">
                            <h:panelGroup layout="block"
                                        id="panelCategorias">
                                <h:outputLabel value="Categoria" style="font-weight: bold"/>
                                <h:panelGroup layout="block" style="display: flex; margin-top: 0;">
                                    <h:selectOneMenu value="#{ProdutoPactoControle.categoriaAdicionar}">
                                        <f:selectItems value="#{ProdutoPactoControle.categoriasSelectItem}"/>
                                    </h:selectOneMenu>
                                    <p:commandLink action="#{ProdutoPactoControle.adicionarCategoria}"
                                                   style="padding-left: 10px"
                                                   update=":fmLay:panelCategorias"
                                                   ajax="true">
                                        <i class="fa-icon-plus-sign-alt"/>
                                    </p:commandLink>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="display: flex; width: 99%;"
                                              rendered="#{not empty ProdutoPactoControle.categoriasSelecionadas}">
                                    <p:dataTable value="#{ProdutoPactoControle.categoriasSelecionadas}" var="cate"
                                                 styleClass="noHeader" style="width: 100%">
                                        <p:column style="text-align: center">
                                            <h:outputText value="#{cate.descricao}"/>
                                        </p:column>
                                        <p:column style="text-align: center">
                                            <p:commandLink action="#{ProdutoPactoControle.removerCategoria(cate)}"
                                                           update=":fmLay:panelCategorias">
                                                <i class="fa-icon-remove"/>
                                            </p:commandLink>
                                        </p:column>
                                    </p:dataTable>
                                </h:panelGroup>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelObservacao"
                                          styleClass="marginTop">
                                <p:outputLabel value="Texto de apresentação"
                                               style="font-weight: bold"/>
                                <p:editor id="descricao"
                                          style="margin-top: 0; width: 99%;"
                                          value="#{ProdutoPactoControle.produtoPacto.descricao}"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelExplicacao"
                                          styleClass="marginTop">
                                <p:outputLabel value="Benefícios"
                                               style="font-weight: bold"/>
                                <p:editor id="explicacao"
                                          style="margin-top: 0; width: 99%;"
                                          value="#{ProdutoPactoControle.produtoPacto.explicacao}"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelRecursosFuncionalidades"
                                          styleClass="marginTop">
                                <p:outputLabel value="Funcionalidades"
                                               style="font-weight: bold"/>
                                <h:panelGroup layout="block" style="display: flex; margin-top: 0;">
                                    <p:inputText id="recursosFuncionalidades"
                                                 style="margin-bottom: 0; width: 67%;"
                                                 value="#{ProdutoPactoControle.funcionalidadeAdicionar}"/>

                                    <p:commandLink action="#{ProdutoPactoControle.adicionarFuncionalidade}"
                                                   style="padding-left: 10px"
                                                   update=":fmLay:panelRecursosFuncionalidades"
                                                   ajax="true">
                                        <i class="fa-icon-plus-sign-alt"/>
                                    </p:commandLink>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="display: flex; width: 99%;"
                                              rendered="#{not empty ProdutoPactoControle.funcionalidades}">
                                    <p:dataTable value="#{ProdutoPactoControle.funcionalidades}" var="func"
                                                 styleClass="noHeader" style="width: 100%">
                                        <p:column style="text-align: center">
                                            <h:outputText value="#{func}"/>
                                        </p:column>
                                        <p:column style="text-align: center">
                                            <p:commandLink action="#{ProdutoPactoControle.removerFuncionalidade(func)}"
                                                           update=":fmLay:panelRecursosFuncionalidades">
                                                <i class="fa-icon-remove"/>
                                            </p:commandLink>
                                        </p:column>
                                    </p:dataTable>
                                </h:panelGroup>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelRequisitos"
                                          styleClass="marginTop">
                                <p:outputLabel value="Requisitos"
                                               style="font-weight: bold"/>
                                <p:editor id="requisitos"
                                          style="margin-top: 0; width: 99%;"
                                          value="#{ProdutoPactoControle.produtoPacto.requisitos}"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelComoUtilizar"
                                          styleClass="marginTop">
                                <p:outputLabel value="Como Utilizar - Texto"
                                               style="font-weight: bold"/>
                                <p:editor id="comoUtilizar"
                                          style="margin-top: 0; width: 99%;"
                                          value="#{ProdutoPactoControle.produtoPacto.comoUtilizar}"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelVideo"
                                          styleClass="marginTop">
                                <p:outputLabel value="Como Utilizar - URL Vídeo"
                                               style="font-weight: bold"/>
                                <p:inputText id="urlVideo"
                                             style="margin-top: 0; width: 99%;"
                                             value="#{ProdutoPactoControle.produtoPacto.urlVideo}"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelImagemProduto"
                                          styleClass="marginTop">

                                <p:outputLabel value="Imagem"
                                               style="font-weight: bold"/>

                                <p:fileUpload
                                        invalidFileMessage="Arquivo de tipo inválido. Somente arquivos de imagem (jpg, png, jpeg) são permitidos."
                                        style="float: inherit;vertical-align: bottom;margin-bottom: 10px;"
                                        auto="true"
                                        rendered="#{empty ProdutoPactoControle.produtoPacto.urlImagem}"
                                        update=":fmLay:panelImagemProduto"
                                        cancelLabel="Cancelar"
                                        allowTypes="/(\.|\/)(jpe?g|png)$/"
                                        sizeLimit="5000000"
                                        uploadLabel="Enviar"
                                        multiple="false"
                                        dragDropSupport="true"
                                        invalidSizeMessage="Não serão permitidas imagens maiores que 5MB"
                                        label="Imagem"
                                        fileUploadListener="#{ProdutoPactoControle.handleFileUpload}"/>

                                <h:panelGroup layout="block"
                                              rendered="#{not empty ProdutoPactoControle.produtoPacto.urlImagem}">
                                    <img src="#{ProdutoPactoControle.produtoPacto.urlImagem}"
                                         alt="img"
                                         style="width: 300px; height: 140px;"/>
                                    <br/>
                                    <br/>
                                    <p:commandLink styleClass="btn btn-secundary"
                                                   action="#{ProdutoPactoControle.removerImagem}"
                                                   update=":fmLay:panelImagemProduto"
                                                   ajax="true">
                                        <i class="fa-icon-remove"/> Remover Imagem
                                    </p:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelChavesHabilitadas"
                                          styleClass="marginTop">
                                <p:outputLabel value="Chaves Habilitadas"
                                               style="font-weight: bold"/>
                                <h:panelGroup layout="block" style="display: flex; margin-top: 0;">
                                    <p:inputText id="chavesHabilitadas"
                                                 style="margin-bottom: 0; width: 67%;"
                                                 value="#{ProdutoPactoControle.chaveAdicionar}"/>

                                    <p:commandLink action="#{ProdutoPactoControle.adicionarChave}"
                                                   style="padding-left: 10px"
                                                   update=":fmLay:panelChavesHabilitadas"
                                                   ajax="true">
                                        <i class="fa-icon-plus-sign-alt"/>
                                    </p:commandLink>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="display: flex; width: 99%;"
                                              rendered="#{not empty ProdutoPactoControle.chavesHabilitadas}">
                                    <p:dataTable value="#{ProdutoPactoControle.chavesHabilitadas}" var="chave"
                                                 styleClass="noHeader" style="width: 100%">
                                        <p:column style="text-align: center">
                                            <h:outputText value="#{chave}"/>
                                        </p:column>
                                        <p:column style="text-align: center">
                                            <p:commandLink action="#{ProdutoPactoControle.removerChave(chave)}"
                                                           update=":fmLay:panelChavesHabilitadas">
                                                <i class="fa-icon-remove"/>
                                            </p:commandLink>
                                        </p:column>
                                    </p:dataTable>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="panelTermos"
                                  style="padding: 20px;">
                        <p:outputLabel value="Termo e Condições"
                                       style="font-weight: bold"/>
                        <p:editor id="termos"
                                  style="margin-top: 0; width: 99%;"
                                  value="#{ProdutoPactoControle.produtoPacto.termosCondicoes}"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="panelEmailCliente"
                                  style="padding: 20px;">
                        <p:outputLabel value="E-mail Cliente" style="font-weight: bold"/>
                        <p:outputLabel value="Tags disponíveis:" style="font-weight: bold"/>
                        <p:outputLabel value="#NOME# - Nome do Cliente que fez a compra"/>
                        <p:outputLabel value="#NUMERO_PEDIDO# - Número do pedido"/>
                        <p:outputLabel value="#PRODUTO_NOME# - Nome do produto"/>
                        <p:outputLabel value="#PRODUTO_VIDEO# - Url do vídeo (Como Utilizar)"/>

                        <p:editor id="htmlEmailCliente"
                                  style="margin-top: 0; width: 99%;"
                                  value="#{ProdutoPactoControle.produtoPacto.htmlEmailCliente}"/>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup id="pnlBotoes" layout="block" style="padding: 30px;"
                              styleClass="col-md-12 text-center">
                    <p:commandLink styleClass="btn btn-primary"
                                   action="#{ProdutoPactoControle.novoProduto}"
                                   update=":fmLay:groupAddEdit"
                                   ajax="true">
                        <i class="fa-icon-plus-sign-alt"/> Novo
                    </p:commandLink>
                    <p:commandLink styleClass="btn btn-secundary"
                                   rendered="#{ProdutoPactoControle.produtoPacto.id > 0}"
                                   action="#{ProdutoPactoControle.excluir}"
                                   update=":fmLay:groupAddEdit"
                                   ajax="true">
                        <i class="fa-icon-remove"/> Excluir
                    </p:commandLink>
                    <p:commandLink styleClass="btn btn-primary"
                                   action="#{ProdutoPactoControle.gravar}"
                                   update=":fmLay:groupAddEdit"
                                   ajax="true">
                        <i class="fa-icon-save"/> #{title['cadastros.salvar']}
                    </p:commandLink>
                    <p:commandLink styleClass="btn btn-secundary"
                                   action="pretty:produtoPacto">
                        <i class="fa fa-home"/> Voltar
                    </p:commandLink>
                </h:panelGroup>
            </h:panelGroup>
        </div>
    </ui:define>
    <ui:define name="JS">
        <h:outputScript library="js" name="cadastrosV2.js"/>
    </ui:define>
</ui:decorate>
</html>
