<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions"
      xmlns:c="http://java.sun.com/jsp/jstl/core">
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="CSS">
        <style>
            .gridRecente .ui-datagrid-data {
                width: auto !important;
            }

            .gridRecente .ui-datagrid-content {
                border: none !important;
            }

            .tabelaUsuarios table tbody tr td a {
                padding: 8px;
            }

            .ui-menu .ui-menu-dynamic .ui-widget .ui-widget-content .ui-corner-all ui-helper-clearfix .ui-shadow {
                z-index: 3000 !important;
            }

            .tabelaUsuarios table tbody tr:nth-child(odd) {
                background-color: #FFFFFF !important;
            }

            .tabelaUsuarios table tbody tr:nth-child(odd):hover {
                background-color: #CBCBCB !important;
            }

            .ui-autocomplete-panel li {
                float: none !important;
            }

            .color-box {
                width: 20px;
                height: 20px;
                display: inline-block;
                background-color: #ffffff;
                position: center;
            }
            .semBorda tr, .semBorda td {
                border: none !important;
            }

            .ui-state-highlight{
                color: black!important;
                background: #ffc465 !important;
            }

            .btn-style{
                color: black!important;
                background: white!important;
            }
            .btn-style:hover{
                color: black;
                background: #ffc465!important;
            }
            .ui-widget-header{
                background: white!important;
            }

        </style>

    </ui:define>
    <ui:define name="conteudo">

        <p:panel id="panelHome" style="border: none!important;" >
            <p:toolbar style="background: white!important; border: none!important;">
                <p:toolbarGroup align="left">
                    <p:outputLabel  styleClass="label label-warning" style="margin-top: 4px"
                                    value="#{fn:length(AplicativoControle.listaAplicativos)} aplicativos adicionados"/>

                </p:toolbarGroup>

                <p:toolbarGroup align="right">
                    <p:commandButton value="Adicionar novo app" actionListener="#{ AplicativoControle.addAplicativo() }"
                                   styleClass="btn-style" style="padding: 5px; background: #0000CD!important;; color: white!important;"
                                     title="Adicionar novo app na lista"
                                     rendered="#{ !AplicativoControle.editando }"
                                     process="@this" update="panelHome, :fmLay:panelCadastro" />

                    <p:commandButton value="Cancelar" actionListener="#{ AplicativoControle.cancelar() }"
                                     styleClass="btn-style" style="padding: 5px; background: #FF0000!important; color: white!important;" title="Cancelar cadastro"
                                     rendered="#{ AplicativoControle.editando }"
                                     process="@this" update="panelHome, :fmLay:panelCadastro"
                    />

                    <p:commandButton value="Salvar" actionListener="#{ AplicativoControle.salvar() }"
                                     styleClass="btn-style" style="padding: 5px 20px 5px 20px;margin-left: 20px; background: #32CD32!important; color: white!important;"
                                     title="Cancelar cadastro"
                                     rendered="#{ AplicativoControle.editando }"
                                     process="@this,panelHome" update="panelHome, :fmLay:panelCadastro"/>

                </p:toolbarGroup>
            </p:toolbar>

            <p:panel id="penalList" style="margin-top: 20px" rendered="#{ !AplicativoControle.editando }">

                <p:outputPanel>
                    <h:outputText value="Buscar: " />
                    <p:inputText id="filterEmpresa" style="width:700px" value="#{AplicativoControle.nomeEmpresaSalva}">
                        <p:ajax event="keyup" process="@this" listener="#{AplicativoControle.filter}" update="tableEmpresas" />
                    </p:inputText>
                </p:outputPanel>

                <p:dataTable value="#{AplicativoControle.listaAplicativos}" var="aplicativo" id="tableEmpresas" paginator="true" rows="10" paginatorPosition="top">

                    <p:column style="width:16px">
                        <p:rowToggler />
                    </p:column>

                    <p:column sortBy="#{aplicativo.nome}" headerText="Empresa" width="400">
                        <h:outputText style="padding: 10px; display: block;"
                                      value="#{aplicativo.nome}"/>
                    </p:column>

                    <p:column  width="60" style="text-align: center">
                            <p:commandButton actionListener="#{AplicativoControle.editar(aplicativo)}"
                                             styleClass="btn-style" icon="ui-icon-pencil" style="background: #999999!important;"
                                             update=":fmLay:panelHome,:fmLay:panelCadastro " title="Editar Empresa"/>
                    </p:column>
                    <p:column  width="60" style="text-align: center">
                            <p:commandButton actionListener="#{AplicativoControle.remover(aplicativo)}"
                                             styleClass="btn-style" style="color: #FF0000!important;" value="X"
                                             update=":fmLay:panelHome" title="Remover Empresa"></p:commandButton>
                    </p:column>

                    <p:rowExpansion>
                        <p:dataTable value="#{aplicativo.chavesEmpresas}" var="empresa">
                            <p:column headerText="Empresa nome" style="text-align: center!important;" >
                                <h:outputText value="#{empresa.nome}" style="font-weight: normal;"/>
                            </p:column>
                            <p:column headerText="Chave" style="text-align: center!important;" >
                                <h:outputText value="#{empresa.chave}" style="font-weight: normal;"/>
                            </p:column>
                        </p:dataTable>
                    </p:rowExpansion>

                </p:dataTable>
             </p:panel>

            <p:panel id="panelCadastro" rendered="#{ AplicativoControle.editando }" style="margin-top: 20px">
                <p:panelGrid columns="2" styleClass="semBorda">
                    <f:facet name="header">
                        <h3>Aplicativo</h3>
                    </f:facet>
                    <p:row>
                        <p:outputLabel for="txtNome" id="lblNome" value="Nome do Aplicativo"/>
                        <p:inputText id="txtNome" style="width: 600px" disabled="#{AplicativoControle.noChangeNome}"
                                     value="#{AplicativoControle.aplicativoSendoEditado.nome}"/>
                    </p:row>
                    <p:row>
                        <p:outputLabel for="cbxTipoAplicativo" id="lblTipoAplicativo" value="Aplicativo"/>
                        <p:selectOneMenu id="cbxTipoAplicativo" value="#{AplicativoControle.aplicativoSendoEditado.aplicativo}"
                                         style="width:200px" >
                            <f:selectItem itemLabel="Selecione" itemValue="" noSelectionOption="true"/>
                            <f:selectItems value="#{AplicativoControle.listAplicativoEnum()}"/>
                        </p:selectOneMenu>
                    </p:row>
                    <p:row>
                        <p:outputLabel for="txtCorPrimaria" id="lblCor" value="Cor Primária (Hexadecimal)" />
                        <p:inputText id="txtCorPrimaria" maxlength="7" placeholder="#FF0000"
                                     value="#{AplicativoControle.aplicativoSendoEditado.cor}"
                                     onkeyup="calcularCor(this.value);">
                            <span class="color-box" id="corSelecionada"
                                  style="background-color: #{AplicativoControle.aplicativoSendoEditado.cor};"/>
                        </p:inputText>
                    </p:row>

                    <p:row >
                        <p:message for="dataExp"/>
                        <p:outputLabel value="Data de expiração"  for="dataExp"/>
                        <p:inputMask id="dataExp" mask="99/99/9999" value="#{ AplicativoControle.aplicativoSendoEditado.dataExpiracao  }"
                                     converterMessage="Formato de data inválido. Exemplo: dia(xx)/mês(xx)/ano(xxxx)">
                            <f:convertDateTime pattern="dd/MM/yyyy" />
                        </p:inputMask>
                    </p:row>
                </p:panelGrid>
            </p:panel>
            <p:separator rendered="#{ AplicativoControle.editando }" />

            <p:panel id="panelCadastroChaves" rendered="#{ AplicativoControle.editando }" style="margin-top: 20px">

                <f:facet name="header">
                    <h3>Chaves da Empresas</h3>

                    <p:commandButton value="Adicionar Chave"
                                     styleClass="btn-style"
                                     style="float: right!important; padding: 5px; background: #0000CD!important; color: white!important;"
                                     actionListener="#{AplicativoControle.addChave()}"/>
                </f:facet>

                <p:dataTable id="tblChavesAplicativos" resizableColumns="true" var="chaveMapeada"
                             value="#{AplicativoControle.chavesMapeadas}" emptyMessage="Nenhuma Chave encontrada">

                    <p:column headerText="Empresa">
                        <p:outputLabel value="#{chaveMapeada.nome}" />
                    </p:column>
                    <p:column headerText="Chaves">
                        <p:outputLabel value="#{chaveMapeada.chave}" />
                    </p:column>

                    <p:column style="text-align: center" width="80">
                        <p:commandButton  style="margin-left: 5px; color: #FF0000!important;"
                                         styleClass="btn-style" value="X"
                                         update="tblChavesAplicativos"
                                         actionListener="#{AplicativoControle.removerChave(chaveMapeada)}"/>
                    </p:column>
                </p:dataTable>

            </p:panel>

        </p:panel>

    </ui:define>
    <ui:define name="JS">
        <h:outputScript target="head" library="js" name="srollinfinito.js"/>
        <script>
            function calcularCor(corHex) {
                $('#corSelecionada').css("background-color", corHex);
            }

            PrimeFaces.widget.Dialog.prototype.applyFocus = function () {
            };
        </script>
    </ui:define>
</ui:decorate>
</html>
