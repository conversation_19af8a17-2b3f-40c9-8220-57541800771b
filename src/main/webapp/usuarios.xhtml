<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions" xmlns:c="http://java.sun.com/jsp/jstl/core">
<ui:decorate template="/template/layout.xhtml">

    <ui:define name="CSS">
        <style>
            @media (max-width: 1600px) {
                .container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
                    width: 100% !important;
                }
            }

        </style>
    </ui:define>
    <ui:define name="conteudo">

        <div class="container-fluid noSlider" style="min-height: 670px">
            <h:panelGroup id="addEdit"
                          layout="block"
                          styleClass="span4 menuLateral"
                          pt:data-spy="affix"
                          pt:data-offset-top="0"
                          style="top: 0; position: absolute; display: table">
                <h4>Cadastro usuários</h4>
                <h:panelGroup id="groupAddEdit">
                    <h:panelGroup layout="block" class="internoMenuLateral">
                        <h:outputText value="Username: " style="margin-left: 19px; margin-top: 10px;"/>
                        <h:inputText autocomplete="off" disabled="#{UsuarioControle.usuarioSelecionado.codigo > 0}" value="#{UsuarioControle.usuarioSelecionado.userName}" pt:placeholder="Username"/>

                        <h:outputText value="Senha: " style="margin-left: 19px;"/>
                        <p:password autocomplete="off" value="#{UsuarioControle.senha}" pt:placeholder="Senha"/>

                        <h:outputText style="margin-left: 19px;" value="CPF: " rendered="#{!UsuarioControle.oamdPacto}"/>
                        <p:inputMask rendered="#{!UsuarioControle.oamdPacto}" mask="999.999.999-99" value="#{UsuarioControle.usuarioSelecionado.cpf}"/>

                        <h:outputText style="margin-left: 19px;" value="Setor: " rendered="#{!UsuarioControle.oamdPacto}"/>
                        <p:selectOneMenu value="#{UsuarioControle.usuarioSelecionado.setor}" editable="true"
                                         style="margin-left: 19px; height: 29px;"
                                         rendered="#{!UsuarioControle.oamdPacto}">
                            <f:selectItems value="#{UsuarioControle.setores}"/>
                        </p:selectOneMenu>

                        <h:outputText value="Perfil: "
                                      style="margin-left: 19px;" rendered="#{UsuarioControle.oamdPacto}"/>
                        <h:selectOneMenu value="#{UsuarioControle.usuarioSelecionado.perfil.codigo}"
                                         styleClass="tagUser"
                                         rendered="#{UsuarioControle.oamdPacto}">
                            <f:selectItems value="#{UsuarioControle.listaSelectItemPerfis}"
                                           rendered="#{UsuarioControle.oamdPacto}"/>
                        </h:selectOneMenu>

                        <c:if test="${UsuarioControle.oamdPacto and UsuarioControle.usuario.adm}">
                            <p:selectBooleanCheckbox style="margin-left: 19px;"
                                                     value="#{UsuarioControle.usuarioSelecionado.adm}"
                                                     styleClass="comBorda"/>
                            <h:outputText value="Administrador OAMD"/>
                            <br/>
                        </c:if>

                        <p:selectBooleanCheckbox style="margin-left: 19px;"
                                                 value="#{UsuarioControle.usuarioSelecionado.ativo}"
                                                 styleClass="comBorda"/>
                        <h:outputText value="Ativo"/>
                        <br/>
                        <p:selectBooleanCheckbox style="margin-left: 19px;"
                                                 value="#{UsuarioControle.usuarioSelecionado.pmg}" styleClass="comBorda"
                                                 rendered="#{UsuarioControle.oamdPacto}"/>
                        <h:outputText value="PMG"
                                      rendered="#{UsuarioControle.oamdPacto}"/>
                        <br/>
                        <p:selectBooleanCheckbox style="margin-left: 19px;"
                                                 value="#{UsuarioControle.usuarioSelecionado.permiteDataBase}"
                                                 styleClass="comBorda"
                                                 rendered="#{UsuarioControle.oamdPacto}"/>
                        <h:outputText value="Usar Data Base"
                                      rendered="#{UsuarioControle.oamdPacto}"/>
                        <br/>
                        <p:selectBooleanCheckbox style="margin-left: 19px;"
                                                 value="#{UsuarioControle.usuarioSelecionado.usaSomenteGame}"
                                                 rendered="#{UsuarioControle.oamdPacto}"
                                                 styleClass="comBorda"/>
                        <h:outputText value="Usar apenas Game of Results"
                                      rendered="#{UsuarioControle.oamdPacto}"/>
                        <br/>

                        <p:selectBooleanCheckbox style="margin-left: 19px;"
                                                 value="#{UsuarioControle.usuarioSelecionado.podeVisualizarGame}"
                                                 rendered="#{UsuarioControle.oamdPacto}"
                                                 styleClass="comBorda"/>
                        <h:outputText value="Permite visualizar o Game of Results"
                                      rendered="#{UsuarioControle.oamdPacto}"/>
                        <br/>


                        <c:if test="${UsuarioControle.oamdPacto and UsuarioControle.usuario.adm}">
                            <p:selectBooleanCheckbox style="margin-left: 19px;"
                                                     value="#{UsuarioControle.usuarioSelecionado.permiteCriarEmpresa}"
                                                     styleClass="comBorda"/>
                            <h:outputText value="Permite criar empresa"/>
                            <br/>
                            <p:selectBooleanCheckbox style="margin-left: 19px;"
                                                     value="#{UsuarioControle.usuarioSelecionado.permiteInformacoesFinanceiras}"
                                                     styleClass="comBorda"/>
                            <h:outputText value="Permite visualizar informações Financeiras"/>
                            <br/>

                            <p:selectBooleanCheckbox style="margin-left: 19px;"
                                                     value="#{UsuarioControle.usuarioSelecionado.permiteAcessarConfiguracoesChave}"
                                                     styleClass="comBorda"/>
                            <h:outputText value="Permite acessar as configurações da chave"/>
                            <br/>

                            <p:selectBooleanCheckbox style="margin-left: 19px;"
                                                     value="#{UsuarioControle.usuarioSelecionado.permiteAlterarModulosChave}"
                                                     styleClass="comBorda"/>
                            <h:outputText value="Permite alterar os módulos da chave"/>
                            <br/>


                            <p:selectBooleanCheckbox style="margin-left: 19px;"
                                                     value="#{UsuarioControle.usuarioSelecionado.permiteControlarGitlab}"
                                                     styleClass="comBorda"/>
                            <h:outputText value="Permite acessar os recursos do Gitlab"/>
                            <br/>
                        </c:if>


                    </h:panelGroup>
                    <h:panelGroup id="pnlBotoes" layout="block" style="margin-bottom: 6px;" styleClass="pull-right">
                        <p:commandLink styleClass="btn btn-primary" action="#{UsuarioControle.limpar}"
                                       update=":fmLay:groupAddEdit :fmLay:dados"
                                       ajax="true">
                            <i class="fa-icon-plus-sign-alt"/> Novo
                        </p:commandLink>
                        <p:commandLink styleClass="btn btn-primary" action="#{UsuarioControle.gravar}"
                                       update=":fmLay:groupAddEdit :fmLay:dados"
                                       ajax="true">
                            <i class="fa-icon-save"/> #{title['cadastros.salvar']}
                        </p:commandLink>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup styleClass="span10 offset4 caixaPrincipal" id="dados">
                <h4>Usuários cadastrados</h4>
                <hr/>
                <p:outputPanel layout="block" styleClass="pesq pesq-tabelas buscaAuxiliar" id="filtrar">
                    <p:inputText id="globalFilter" value="#{UsuarioControle.filtro}">
                        <f:ajax event="keyup" listener="#{UsuarioControle.filtrar(UsuarioControle.usuarios)}"
                                delay="500" render=":fmLay:lista"/>
                    </p:inputText>

                    <p:watermark for="globalFilter" value="#{title['cadastros.filtrar']}"/>
                </p:outputPanel>
                <p:dataTable var="user" value="#{UsuarioControle.listaTela}" id="lista" widgetVar="tabelaAtual"
                             emptyMessage="#{msg['tabela.semregistros']}"
                             styleClass="tabelaPrincipal">
                    <f:facet name="header">
                        <h:panelGroup layout="block" id="totalTabela" styleClass="totalizaTabela">
                            <h:outputText value="#{fn:length(UsuarioControle.listaTela)} usuários"/>
                        </h:panelGroup>
                    </f:facet>
                    <p:column headerText="Username">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{UsuarioControle.editar(user)}"
                                       update=":fmLay:groupAddEdit :fmLay:pnlBotoes">#{user.userName}</p:commandLink>
                    </p:column>
                    <p:column headerText="CPF" rendered="#{!UsuarioControle.oamdPacto}">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{UsuarioControle.editar(user)}"
                                       update=":fmLay:groupAddEdit :fmLay:pnlBotoes">
                            #{user.cpf}
                        </p:commandLink>
                    </p:column>
                    <p:column headerText="Setor" rendered="#{!UsuarioControle.oamdPacto}">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{UsuarioControle.editar(user)}"
                                       update=":fmLay:groupAddEdit :fmLay:pnlBotoes">
                            #{user.setor}
                        </p:commandLink>
                    </p:column>

                    <p:column headerText="Perfil" rendered="#{UsuarioControle.oamdPacto}">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{UsuarioControle.editar(user)}"
                                       update=":fmLay:groupAddEdit :fmLay:pnlBotoes">
                            #{user.perfil.nome}
                        </p:commandLink>
                    </p:column>

                    <p:column headerText="Ativo" rendered="#{UsuarioControle.oamdPacto}" sortBy="#{user.ativo}">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{UsuarioControle.editar(user)}"
                                       update=":fmLay:groupAddEdit :fmLay:pnlBotoes">
                            #{user.ativo ? 'Ativo':' - '}
                        </p:commandLink>
                    </p:column>

                    <p:column headerText="Admin" sortBy="#{user.adm}">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{UsuarioControle.editar(user)}"
                                       update=":fmLay:groupAddEdit :fmLay:pnlBotoes">
                            #{user.adm ? 'Admin':' - '}
                        </p:commandLink>
                    </p:column>
                </p:dataTable>
            </h:panelGroup>

        </div>

    </ui:define>
    <ui:define name="JS">
        <h:outputScript library="js" name="cadastrosV2.js"/>
    </ui:define>
</ui:decorate>
</html>
