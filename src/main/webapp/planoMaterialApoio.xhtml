<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:p="http://primefaces.org/ui">

<ui:decorate template="/template/layout.xhtml">
    <ui:define name="conteudo">
        <h:form id="formCadastrarMaterialApoio">
            <h:panelGroup styleClass="span9 offset3 caixaPrincipal" id="planoMaterial"
                          style="padding: 20px; width: 90%; margin-left: 3%;">
                <h4>Cadastro material de apoio</h4>
                <br/>
                <h:panelGroup layout="block" styleClass="form-group col-md-10">
                    <h:panelGrid columns="4">
                        <h:outputText value="Nome :"/>
                        <h:inputText value="#{ModeloPlanoSucessoControle.modeloPlanoSucesso.nome}" style="width: 500px;"/>

                        <h:outputText value="Duração em minutos:"/>
                        <h:inputText value="#{ModeloPlanoSucessoControle.modeloPlanoSucesso.duracao}" style="width: 100px;"/>
                    </h:panelGrid>

                    <h:outputText value="Descrição:" style="margin-left: 2%"/>
                    <h:inputTextarea value="#{ModeloPlanoSucessoControle.modeloPlanoSucesso.descricao}"
                                     style="width:800px; height: 100px"/>
                </h:panelGroup>

                <ui:include src="planosSucesso/modalMaterialApoio.xhtml"/>
                <h:panelGroup layout="block" styleClass="form-group col-md-10">
                    <p:dataTable id="tblCadastroPlanoSucesso" value="#{ModeloPlanoSucessoControle.modeloPlanoSucesso.modeloPlanoSucessoAcoes}" var="acao">
                        <p:column>
                            <f:facet name="header">
                                <h:outputText value="Ação"/>
                            </f:facet>
                            <h:commandLink title="#{acao.nome}" action="#"/>
                        </p:column>
                        <p:column>
                            <f:facet name="header">
                                <h:outputText value="Notas"/>
                            </f:facet>
                            <h:outputText value="#{acao.notas}"/>
                        </p:column>
                        <p:column>
                            <f:facet name="header">
                                <h:outputText value="Duração"/>
                            </f:facet>
                            <h:outputText value="#{acao.duracao}"/>
                        </p:column>
                        <p:column>
                            <f:facet name="header">
                                <h:outputText value="Matérias de apoio"/>
                            </f:facet>
                            <h:outputText value="#{acao.materiaisApoio.size}"/>
                        </p:column>
                    </p:dataTable>
                </h:panelGroup>
            </h:panelGroup>
            <h:panelGrid columns="4" style="margin-left: 350px">
                <p:commandButton value="Voltar" styleClass="btn btn-primary" style="float: right;"
                                 action="#{ModeloPlanoSucessoControle.voltarParaConsulta}"/>
                <p:commandButton value="Adicionar Material" styleClass="btn btn-primary"
                                 onclick="PF('dlg2').show();"/>
                <p:commandButton value="Salvar" styleClass="btn btn-primary"
                                 action="#{ModeloPlanoSucessoControle.salvarPlano}"/>
                <p:commandButton value="Excluir" styleClass="btn btn-primary"/>
            </h:panelGrid>
        </h:form>
    </ui:define>
</ui:decorate>
</html>
