<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions" xmlns:c="http://java.sun.com/jsp/jstl/core">
    <ui:decorate template="/template/layout.xhtml">
        <ui:define name="CSS">
            <style>
                .ui-autocomplete-token-icon.ui-icon.ui-icon-close {
                    background-image: url("imagens/icons.png");
                    margin-right: 10px;
                }
                .ui-autocomplete-token-label{
                    background-color: #363636;
                    background-image: none;
                    color: white;
                    text-transform: lowercase;
                    box-shadow: none;
                    border: none;
                    padding: 13.5px 30px 13.5px 10px;
                    border-radius: 3px;
                }


                .ui-autocomplete-input-token > input[type="text"]{
                    border: none !important;
                }
                .ui-autocomplete-multiple-container.ui-inputfield{
                    width: 90% !important;
                }
                .chosen-container-multi .chosen-choices li.search-choice .search-choice-close {
                    top: 0;
                    right: 0;
                    height: 100%;
                    width: 100%;
                    background: none;
                    color: #cbcbcb;
                }
                .chosen-container-multi .chosen-choices li.search-choice .search-choice-close:hover {
                    color: white;
                }
                .chosen-container-multi .chosen-choices li.search-choice .search-choice-close::before {
                    font-family: FontAwesome;
                    font-size: 20px;
                    content: "\f00D";
                    position: absolute;
                    top: 13px;
                    right: 6px;
                }
                td[role="gridcell"] {
                    word-break: break-all;
                    word-wrap: break-word;
                }
                .ui-selectmanycheckbox label, .ui-selectoneradio label {
                    font-size: 10px;
                }
                .ui-datatable-data > tr > td > a{
                    font-size: 12px !important;
                    font-weight: bold;
                }
                .styleBlue .ui-state-default {
                    background-color: #3258CB;
                    background: #3258CB;
                    border: 1px solid #5688D5;
                    color: #FFFFFF;
                    font-weight: normal;
                }
            </style>
        </ui:define>


        <ui:define name="conteudo">
            <h:panelGroup layout="block" styleClass="container-fluid noSlider" style="width: 100%;">
                 <h:panelGroup layout="block" id="dados" style="width: 100%; margin-left: auto; margin-right: auto;"
                              styleClass="span12 caixaPrincipal">
                    <h4>Empresas Cadastradas</h4>
                    <hr />
                    <p:outputPanel layout="block" styleClass="pesq pesq-tabelas buscaAuxiliar" id="filtrar">
                        <p:inputText  id="globalFilter" value="#{EmpresaControle.filtro}">
                            <f:ajax event="keyup" listener="#{EmpresaControle.filtrar}"
                                    delay="#{EmpresaControle.tempoDelay}"
                                    render=":fmLay:pnlResultado"/>
                        </p:inputText>

                        <p:watermark for="globalFilter" value="#{title['cadastros.filtrar']}" />
                    </p:outputPanel>
                    <h:panelGroup layout="block" id="totalTabela" style="margin-left: 20px;">
                        <p:commandLink styleClass="btn btn-primary" action="#{EmpresaControle.nova}"
                                       oncomplete="empresaDialog.show();"
                                       update=":fmLay:empresaDetails"
                                       ajax="true" >
                            <i class="fa-icon-plus-sign-alt"/> #{title['cadastros.nova']}
                        </p:commandLink>
                        <p:commandLink styleClass="btn btn-primary" action="#{EmpresaControle.updateAllServices}"
                                       rendered="#{EmpresaControle.oamdPacto and EmpresaControle.usuario.adm}"
                                       style="margin-top: 2px; margin-bottom: 2px;"
                                       ajax="true">
                            <i class="fa-icon-refresh"/> #{title['cadastros.updateServices']}
                        </p:commandLink>
                        <p:commandLink styleClass="btn btn-primary" action="#{EmpresaControle.updateVerificadorInconsistencias}"
                                       rendered="#{EmpresaControle.oamdPacto and EmpresaControle.usuario.adm}"
                                       style="margin-top: 2px; margin-bottom: 2px;"
                                       ajax="true">
                            <i class="fa-icon-refresh"/> #{title['cadastros.updateVerificador']}
                        </p:commandLink>
                        <p:commandLink styleClass="btn btn-primary" action="#{EmpresaControle.listarServidores}"
                                       rendered="#{EmpresaControle.oamdPacto and EmpresaControle.usuario.adm}"
                                       update=":fmLay:pnlResultado"
                                       style="margin-top: 2px; margin-bottom: 2px;"
                                       ajax="true">
                            <i class="fa-icon-cloud"/> #{title['cadastros.nuvens']}
                        </p:commandLink>
                        <p:commandLink styleClass="btn btn-primary" action="#{EmpresaControle.editarCronTab}"
                                       rendered="#{EmpresaControle.oamdPacto and EmpresaControle.usuario.adm}"
                                       update=":fmLay:pnlResultado"
                                       style="margin-top: 2px; margin-bottom: 2px;"
                                       ajax="true">
                            <i class="fa-icon-edit"/> #{title['cadastros.editarCrontab']}
                        </p:commandLink>
                        <p:commandLink styleClass="btn btn-primary" action="#{EmpresaControle.gravarCronTab}"
                                       rendered="#{EmpresaControle.oamdPacto and EmpresaControle.usuario.adm}"
                                       update=":fmLay:pnlResultado"
                                       style="margin-top: 2px; margin-bottom: 2px;"
                                       ajax="true">
                            <i class="fa-icon-save"/> #{title['cadastros.gravarCrontab']}
                        </p:commandLink>
                        <p:commandLink styleClass="btn btn-primary" action="#{EmpresaControle.lerVersaoPgsql}"
                                       rendered="#{EmpresaControle.oamdPacto and EmpresaControle.usuario.adm}"
                                       update=":fmLay:pnlResultado"
                                       style="margin-top: 2px; margin-bottom: 2px;"
                                       ajax="true">
                            <i class="fa-icon-edit"/> #{title['cadastros.lerVersaoPgsql']}
                        </p:commandLink>
                        <p:menuButton value="MoviDesk" style="margin-top: 2px; margin-bottom: 2px;"
                                      rendered="#{EmpresaControle.oamdPacto and EmpresaControle.usuario.adm}"
                                      styleClass="styleBlue">
                            <p:menuitem value="Migrar Plataforma" action="#{EmpresaControle.migrarEmpresasParaMoviDesk}"
                                        title="Muda a plataforma de chamados do BEE para o MoviDesk"
                                        style="margin-top: 2px; margin-bottom: 2px;"/>

                            <p:menuitem value="Habilitar Chat" action="#{EmpresaControle.habilitarChatMoviDesk}"
                                        title="Habilita a integração com o chat do MoviDesk"
                                        style="margin-top: 2px; margin-bottom: 2px;"/>

                            <p:menuitem value="Migrar Plataforma/Chat"
                                        action="#{EmpresaControle.migrarEmpresasParaMoviDeskHabilitandoChat}"
                                        title="Migra a plataforma e habilita integração com o chat do MoviDesk"
                                        style="margin-top: 2px; margin-bottom: 2px;"/>
                        </p:menuButton>
                    </h:panelGroup>
                    <h:panelGroup id="pnlResultado" layout="block">
                        <p:dataTable var="emp" value="#{EmpresaControle.listaTela}" id="lista"
                                     paginator="true" rows="20" rowsPerPageTemplate="20,40,60,80,100"
                                     rendered="#{not EmpresaControle.exibirServidores}"
                                     emptyMessage="#{msg['tabela.semregistros']}"
                                     styleClass="tabelaPrincipal">
                            <f:facet name="header">
                                <center>
                                    <h:outputText value="#{fn:length(EmpresaControle.listaTela)} grupos"  />
                                </center>
                            </f:facet>
                            <p:column headerText="Nome" width="280">
                                <p:commandLink ajax="true" partialSubmit="true"
                                               action="#{EmpresaControle.editar(emp)}"
                                               oncomplete="empresaDialog.show();"
                                               update=":fmLay:groupAddEdit :fmLay:pnlBotoes" >#{emp.visualName}</p:commandLink>
                            </p:column>
                            <p:column headerText="Chave" width="300">
                                <p:commandLink ajax="true" partialSubmit="true"
                                               action="#{EmpresaControle.editar(emp)}"
                                               oncomplete="empresaDialog.show();"
                                               update=":fmLay:groupAddEdit :fmLay:pnlBotoes" >#{emp.chave}</p:commandLink>
                            </p:column>
                            <p:column headerText="Tipo" width="300">
                                <p:commandLink ajax="true" partialSubmit="true"
                                               action="#{EmpresaControle.editar(emp)}"
                                               oncomplete="empresaDialog.show();"
                                               update=":fmLay:groupAddEdit :fmLay:pnlBotoes" >#{emp.tipoEmpresaDescricao}</p:commandLink>
                            </p:column>
                            <p:column headerText="Módulos" width="200">
                                <p:commandLink ajax="true" partialSubmit="true"
                                               action="#{EmpresaControle.editar(emp)}"
                                               oncomplete="empresaDialog.show();"
                                               process="@this"
                                               update=":fmLay:groupAddEdit :fmLay:pnlBotoes">#{emp.modulos}</p:commandLink>
                            </p:column>
                            <p:column headerText="Infra" width="90">
                                <p:commandLink ajax="true" partialSubmit="true"
                                               action="#{EmpresaControle.editar(emp)}"
                                               oncomplete="empresaDialog.show();"
                                               update=":fmLay:groupAddEdit :fmLay:pnlBotoes">#{emp.infoInfra.descricao}</p:commandLink>
                            </p:column>
                            <p:column headerText="Backup" width="90">
                                <p:commandLink ajax="true" partialSubmit="true"
                                               action="#{EmpresaControle.editar(emp)}"
                                               oncomplete="empresaDialog.show();"
                                               update=":fmLay:groupAddEdit :fmLay:pnlBotoes">#{emp.dirSlaveBackup}</p:commandLink>
                            </p:column>
                            <p:column headerText="Carteiras">
                                <p:commandLink ajax="true" partialSubmit="true"
                                               action="#{EmpresaControle.editar(emp)}"
                                               oncomplete="empresaDialog.show();"
                                               update=":fmLay:groupAddEdit :fmLay:pnlBotoes">#{emp.codigoCarteirasApresentar}</p:commandLink>
                            </p:column>
                            <p:column headerText="ZW Infra Pacto?">
                                <p:commandLink ajax="true" partialSubmit="true"
                                               action="#{EmpresaControle.editar(emp)}"
                                               oncomplete="empresaDialog.show();"
                                               update=":fmLay:groupAddEdit :fmLay:pnlBotoes">
                                    #{emp.usarBDLocal}
                                </p:commandLink>
                            </p:column>
                            <p:column headerText="TR Infra Pacto?">
                                <p:commandLink ajax="true" partialSubmit="true"
                                               action="#{EmpresaControle.editar(emp)}"
                                               oncomplete="empresaDialog.show();"
                                               update=":fmLay:groupAddEdit :fmLay:pnlBotoes">
                                    #{emp.usarBDTreinoLocal}
                                </p:commandLink>
                            </p:column>
                            <p:column headerText="PGSQL">
                                <p:commandLink ajax="true" partialSubmit="true"
                                               action="#{EmpresaControle.editar(emp)}"
                                               oncomplete="empresaDialog.show();"
                                               update=":fmLay:groupAddEdit :fmLay:pnlBotoes">
                                    #{emp.versaoSGBD}
                                </p:commandLink>
                            </p:column>
                            <p:column headerText="Opções">
                                <p:commandLink oncomplete="modalSubir.show();"
                                               partialSubmit="true"
                                               update=":fmLay:groupAddEdit :fmLay:pnlBotoes :fmLay:dlgModalSubir "
                                               action="#{EmpresaControle.editar(emp)}"
                                               title="Subir Empresa para Nuvem"
                                               ajax="true" >
                                    <i class="fa-icon-cloud-upload"/>
                                </p:commandLink>
                                <p:commandLink oncomplete="modalEmpresaMigracao.show();" partialSubmit="true"
                                               rendered="false"
                                                update=":fmLay:groupAddEdit :fmLay:pnlBotoes :fmLay:dlgModalEmpresaMigracao "
                                               action="#{EmpresaControle.validarEmpresasParaMigracaoDados(emp)}"
                                               title="Migração de dados"
                                               ajax="true" >
                                    <i class="fa-icon-random"/>
                                </p:commandLink>
                            </p:column>
                        </p:dataTable>

                        <p:dataTable var="emp" value="#{EmpresaControle.listaTela}" id="listaServidores"
                                     rendered="#{EmpresaControle.exibirServidores}"
                                     emptyMessage="#{msg['tabela.semregistros']}"
                                     styleClass="tabelaPrincipal">
                            <f:facet name="header">
                                <center>
                                    <h:outputText value="#{fn:length(EmpresaControle.listaTela)} grupos"  />
                                </center>
                            </f:facet>
                            <p:column headerText="Nome" width="280">
                                <p:commandLink ajax="true" partialSubmit="true"
                                               action="#{EmpresaControle.editar(emp)}"
                                               oncomplete="empresaDialog.show();"
                                               update=":fmLay:groupAddEdit :fmLay:pnlBotoes" >#{emp.identificadorEmpresa == null ? emp.nomeBD : emp.identificadorEmpresa}</p:commandLink>
                            </p:column>
                            <p:column headerText="Chave" width="350">
                                <p:commandLink ajax="true" partialSubmit="true"
                                               action="#{EmpresaControle.editar(emp)}"
                                               oncomplete="empresaDialog.show();"
                                               update=":fmLay:groupAddEdit :fmLay:pnlBotoes" >#{emp.chave}</p:commandLink>
                            </p:column>
                            <p:column headerText="Infra" width="90">
                                <p:commandLink ajax="true" partialSubmit="true"
                                               action="#{EmpresaControle.editar(emp)}"
                                               oncomplete="empresaDialog.show();"
                                               update=":fmLay:groupAddEdit :fmLay:pnlBotoes" >#{emp.infoInfra.descricao}</p:commandLink>
                            </p:column>
                            <p:column headerText="Opções">
                                <p:commandLink partialSubmit="true"
                                               update=":fmLay:pnlResultado"
                                               action="#{EmpresaControle.editarCronTab(emp)}"
                                               title="Editar Crontab"
                                               ajax="true" >
                                    <i class="fa-icon-tasks"/>
                                </p:commandLink>
                            </p:column>
                            <p:column headerText="PGSQL Version">
                                <h:outputText value="#{emp.versaoSGBD}"/>
                            </p:column>
                            <p:column width="500" headerText="Cron">
                                <p:inputTextarea styleClass="editor"
                                                 rendered="#{not empty emp.crontab}"
                                                 style="background-color: black; color: greenyellow;width: 500px; height: 350px; font-family: Arial, Helvetica, sans-serif; font-size: 12px;"
                                                 value="#{emp.crontab}"/>
                                <p:commandLink rendered="#{not empty emp.crontab}" styleClass="btn btn-primary" action="#{EmpresaControle.gravarCronTab(emp)}"
                                               style="margin-top: 2px; margin-bottom: 2px; margin-left: 2px; width: 64px;"
                                               ajax="true">
                                    <i class="fa-icon-save"/> #{title['cadastros.salvar']}
                                </p:commandLink>
                            </p:column>
                        </p:dataTable>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <p:dialog header="Subir #{EmpresaControle.empresaSelecionada.name} para Nuvem" widgetVar="modalSubir" minHeight="40"
                      modal="true"
                      id="dlgModalSubir">
                <h:panelGroup id="pnlDadosSubirEmpresa" styleClass="pnlDadosSubirEmpresa">
                    <h:outputText style="font-weight: bold" value="Software"/>
                    <p:selectOneRadio value="#{EmpresaControle.flagSoftware}">
                        <f:selectItems value="#{EmpresaControle.listaFlagSoftware}"/>
                        <p:ajax process=":fmLay:pnlDadosSubirEmpresa"
                                event="change"
                                listener="#{EmpresaControle.prepararInfoServidor}" update="@(.pnlDadosSubirEmpresa)"/>
                    </p:selectOneRadio>

                    <h:panelGroup style="margin-bottom: 3px;" layout="block"
                                  rendered="#{EmpresaControle.usuario.adm and EmpresaControle.flagSoftware != 'FC'}">
                        <h:outputText style="font-weight: bold" value="Banco: "/>
                        <p:selectOneMenu value="#{EmpresaControle.schemaBancoEnum}" style="vertical-align: middle;">
                            <f:selectItems value="#{EmpresaControle.listaSchema}"/>
                        </p:selectOneMenu>
                    </h:panelGroup>

                    <h:panelGroup style="margin-bottom: 3px;" layout="block"
                                  rendered="#{EmpresaControle.usuario.adm}">
                        <h:outputText style="font-weight: bold" value="Infraestrutura: "/>
                        <p:selectOneMenu value="#{EmpresaControle.infra}" style="vertical-align: middle;">
                            <f:selectItems value="#{EmpresaControle.listaInfra}"/>
                            <p:ajax process=":fmLay:pnlDadosSubirEmpresa" event="change"
                                    listener="#{EmpresaControle.prepararInfoServidor}" update="@(.pnlDadosSubirEmpresa)"/>
                        </p:selectOneMenu>
                    </h:panelGroup>
                    <h:panelGroup id="pnlDadosSubirHost" style="margin-bottom: 3px;" layout="block"
                                  rendered="#{EmpresaControle.usuario.adm}">
                        <h:outputText style="font-weight: bold" value="Criar Banco em Servidor SGBD: "/>
                        <h:inputText value="#{EmpresaControle.hostPGSubir}" pt:placeholder="Host BD"/>
                        <h:inputText value="#{EmpresaControle.portaPGSubir}" pt:placeholder="Porta BD" size="8" maxlength="8"/>
                    </h:panelGroup>
                    <br/>
                    <p:commandLink styleClass="btn btn-primary" action="#{EmpresaControle.subirBanco}"
                                   update=":fmLay:groupAddEdit, :fmLay:dados"
                                   ajax="true" >
                        <i class="fa-icon-check"/> #{title['cadastros.subir']}
                    </p:commandLink>
                    <p:commandLink styleClass="btn btn-primary" rendered="#{EmpresaControle.flagSoftware != 'FC'}"
                                   action="#{EmpresaControle.updateServices}"
                                   update=":fmLay:groupAddEdit, :fmLay:dados"
                                   ajax="true" >
                        <i class="fa-icon-check"/> #{title['cadastros.updateServices']}
                    </p:commandLink>

                    <p:commandLink styleClass="btn btn-primary"
                                   action="#{EmpresaControle.updateScriptBackupUmaEmpresa(EmpresaControle.empresaSelecionada)}"
                                   update=":fmLay:groupAddEdit, :fmLay:dados"
                                   ajax="true" >
                        <i class="fa-icon-check"/> #{title['cadastros.gerarBackups']}
                    </p:commandLink>
                </h:panelGroup>
            </p:dialog>

            <p:hotkey bind="esc" handler="PF('empresaDialog').hide();"  />
            <p:dialog widgetVar="empresaDialog" showHeader="true"
                      header="Cadastro Empresa"
                      modal="true"
                      closable="true"
                      width="800"
                      height="600"
                      closeOnEscape="true"
                      dynamic="true"
                      id="modalEmpresa" showEffect="fade" hideEffect="fade" resizable="false">

                <h:panelGroup layout="block" id="empresaDetails" style="overflow-y: auto">

                    <h:panelGroup layout="block" styleClass="span3 menuLateral" id="addEdit"
                                  style="width: 100%"
                                  pt:data-spy="affix" pt:data-offset-top="0">
                        <h:panelGroup id="groupAddEdit" style="width: 100%">

                            <h:panelGroup layout="block" styleClass="internoMenuLateral" style="padding: 10px">
                                <p:selectBooleanCheckbox value="#{EmpresaControle.opcoesAvancadas}"
                                                         styleClass="comBorda">
                                    <p:ajax event="change" update=":fmLay:empresaDetails"/>
                                </p:selectBooleanCheckbox>
                                <h:outputText value="Opções avançadas"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" styleClass="internoMenuLateral" style="padding: 10px">
                                <h:panelGrid columns="2" width="100%">
                                    <h:outputText rendered="#{not empty EmpresaControle.empresaSelecionada.chave}"
                                                  value="#{EmpresaControle.empresaSelecionada.chave}"
                                                  pt:placeholder="Chave da Empresa"/>

                                    <h:inputText disabled="false"
                                                 rendered="#{empty EmpresaControle.empresaSelecionada.chave}"
                                                 value="#{EmpresaControle.empresaSelecionada.nomeEmpresa}"
                                                 pt:placeholder="Nome Empresa"/>

                                    <h:inputText disabled="true"
                                                 rendered="#{not empty EmpresaControle.empresaSelecionada.chave}"
                                                 value="#{EmpresaControle.empresaSelecionada.name}"
                                                 pt:placeholder="Nome Empresa"/>

                                    <h:panelGroup layout="block"
                                                  rendered="#{empty EmpresaControle.empresaSelecionada.chave}">

                                        <p:inputText id="cnpj"
                                                     pt:placeholder="CNPJ"
                                                     maxlength="18"
                                                     onkeypress="mascara(this, cpfCnpj)"
                                                     value="#{EmpresaControle.empresaSelecionada.empresaFinanceiro.cnpj}"/>

                                        <p:commandLink action="#{EmpresaControle.acaoConsultarCNPJFinanceiroPacto}"
                                                       style="padding-left: 5px; font-size: 20px; text-decoration: none"
                                                       title="Consultar CNPJ no Financeiro da Pacto">
                                            <i class="fa-icon-search"></i>
                                        </p:commandLink>
                                    </h:panelGroup>


                                    <c:if test="${EmpresaControle.opcoesAvancadas}">
                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.modulos}"

                                                     title="ZW = ZillyonWeb&#013;CRM = CRMWeb&#013;FIN = FinanceiroWeb&#013;CE = CentralEventos&#013;EST = Agenda Studio&#013;TR = TreinoWeb&#013;GP = Gestão de Personal&#013;SLC = Aula Cheia&#013;SBX = Smartbox&#013;GOR = Game Of Results&#013;ZAA = ZWAuto(Totem)&#013;NTR = Novo Treino&#013;NCR = Novo Crossfit&#013;NAV = Novo Avaliação&#013;IA = Pacto I.A"
                                                     pt:placeholder="Módulos Habilitados"/>

                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.hostBD}" pt:placeholder="Host BD"/>
                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.porta}" pt:placeholder="Porta BD"/>
                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.userBD}" pt:placeholder="User BD"/>
                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.roboControle}" pt:placeholder="URL ZillyonWeb"/>
                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.urlZaw}" pt:placeholder="URL ZAW"/>
                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.urlZawWrite}" pt:placeholder="URL ZAW Write"/>
                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.urlIntegracaoWS}" pt:placeholder="URL Integracao WS"/>
                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.urlTreino}" pt:placeholder="URL TreinoWeb"/>
                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.urlTreinoWeb}" pt:placeholder="URL Treino Web"/>
                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.urlTreinoMobile}" pt:placeholder="URL Treino Mobile"/>
                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.urlTreinoIntegracaoZW}" pt:placeholder="URL Treino Integração ZW"/>
                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.urlFacilite}" pt:placeholder="URL Facilite"/>
                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.urlApi}" pt:placeholder="URL API"/>
                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.urlZWAuto}" pt:placeholder="URL ZWAuto"/>

                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.userSsh}" pt:placeholder="Usuário SSH"/>
                                        <h:inputSecret redisplay="true" value="#{EmpresaControle.empresaSelecionada.pwdSsh}" pt:placeholder="Senha SSH"/>
                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.portaSsh}" pt:placeholder="Porta SSH" size="10" maxlength="8"/>
                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.versaoSGBD}" pt:placeholder="Versão SGBD" size="6" maxlength="4"/>
                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.identificadorEmpresa}" pt:placeholder="Identificador"/>

                                        <h:panelGrid columns="2">
                                            <p:selectBooleanCheckbox value="#{EmpresaControle.empresaSelecionada.ativa}" styleClass="comBorda"/>
                                            <h:outputText value="Ativa"/>

                                            <p:selectBooleanCheckbox value="#{EmpresaControle.empresaSelecionada.usarBDLocal}" styleClass="comBorda"/>
                                            <h:outputText value="ZW usa Infra Pacto?"/>

                                            <p:selectBooleanCheckbox value="#{EmpresaControle.empresaSelecionada.usarBDTreinoLocal}" styleClass="comBorda"/>
                                            <h:outputText value="Treino usa Infra Pacto?"/>

                                            <p:selectBooleanCheckbox value="#{EmpresaControle.empresaSelecionada.permiteMultiEmpresas}" styleClass="comBorda"/>
                                            <h:outputText value="Permite Multi-Empresas"/>

                                            <p:selectBooleanCheckbox styleClass="comBorda" value="#{EmpresaControle.empresaSelecionada.utilizarMoviDesk}"/>
                                            <h:outputText value="Utilizar MoviDesk"/>

                                            <p:selectBooleanCheckbox styleClass="comBorda" value="#{EmpresaControle.empresaSelecionada.utilizarChatMoviDesk}"/>
                                            <h:outputText value="Habilitar Chat MoviDesk"/>

                                            <p:selectBooleanCheckbox styleClass="comBorda" value="#{EmpresaControle.empresaSelecionada.corrigirProtocolo}"/>
                                            <h:outputText value="Corrigir Protocolo"/>

                                            <p:selectBooleanCheckbox styleClass="comBorda" value="#{EmpresaControle.empresaSelecionada.betaTester}"/>
                                            <h:outputText value="Beta tester"/>

                                            <p:selectBooleanCheckbox styleClass="comBorda" value="#{EmpresaControle.empresaSelecionada.gerarShellScript}"/>
                                            <h:outputText value="Gerar scripts serviços (Shell Script)"/>

                                            <p:selectBooleanCheckbox styleClass="comBorda" value="#{EmpresaControle.empresaSelecionada.integracaoSesiCe}"/>
                                            <h:outputText value="Integração Sesi - CE"/>

                                            <p:selectBooleanCheckbox styleClass="comBorda" value="#{EmpresaControle.empresaSelecionada.integracaoSesiSc}"/>
                                            <h:outputText value="Integração Sesi - SC"/>
                                        </h:panelGrid>

                                        <h:panelGrid columns="1">
                                            <p:selectOneMenu value="#{EmpresaControle.empresaSelecionada.infoInfra}">
                                                <f:selectItems value="#{EmpresaControle.listaInfra}"/>
                                                <p:ajax process=":fmLay:empresaDetails" event="change" listener="#{EmpresaControle.prepararInfoServidorNovo}" update=":fmLay:empresaDetails"/>
                                            </p:selectOneMenu>

                                            <p:selectOneMenu value="#{EmpresaControle.empresaSelecionada.tipoEmpresa}">
                                                <f:selectItems value="#{EmpresaControle.listaTipo}"/>
                                            </p:selectOneMenu>
                                        </h:panelGrid>

                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.treinofront}" pt:placeholder="URL SPA novo treino" title="É o caminho da raiz, do front end do novo ZW"/>

                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.zwfront}" pt:placeholder="URL novo Adm" title="É o caminho da raiz, do front end do novo ADM"/>

                                        <p:selectOneMenu value="#{EmpresaControle.empresaSelecionada.idiomaBanco}" pt:title="Idioma dos valores default no banco de dados">
                                            <f:selectItems value="#{EmpresaControle.listaIdioma}"/>
                                        </p:selectOneMenu>

                                        <h:inputText value="#{EmpresaControle.empresaSelecionada.codigoIntegracaoFera}" pt:placeholder="Código Integração Fera"/>

                                    </c:if>
                                </h:panelGrid>
                            </h:panelGroup>

                            <c:if test="${EmpresaControle.opcoesAvancadas}">
                                <h:panelGroup layout="block" styleClass="internoMenuLateral" style="width: 100%">
                                    <h:panelGroup layout="block" style="padding: 10px">
                                        <h:outputLabel id="sizeCarteiras" value="Carteiras">
                                        </h:outputLabel>
                                        <h:selectManyMenu styleClass="tagGrupos" value="#{EmpresaControle.carteirasSelecionadas}">
                                            <f:selectItems value="#{EmpresaControle.carteirasSelectItem}"/>
                                            <f:ajax event="change" execute="@this"/>
                                        </h:selectManyMenu>
                                    </h:panelGroup>
                                </h:panelGroup>

                                <h:panelGroup layout="block" styleClass="internoMenuLateral" style="width: 100%">
                                    <h:panelGroup layout="block" style="padding: 10px">
                                        <h:outputLabel value="Tags"/>
                                        <p:autoComplete id="tags" multiple="true" style="width: 100%;"
                                                        value="#{EmpresaControle.empresaSelecionada.tagsSelecionadas}"
                                                        completeMethod="#{EmpresaControle.completeTags}"
                                                        maxResults="10"/>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </c:if>

                            <h:panelGroup layout="block" styleClass="internoMenuLateral" style="padding: 10px"
                                          id="panelCriarConsultarNovaEmpresa"
                                          rendered="#{empty EmpresaControle.empresaSelecionada.chave}">

                                <p:selectBooleanCheckbox value="#{EmpresaControle.consultarFinanceiroPacto}"
                                                         styleClass="comBorda"
                                                         rendered="#{EmpresaControle.oamdPacto and EmpresaControle.usuario.adm}"/>
                                <h:outputText rendered="#{EmpresaControle.oamdPacto and EmpresaControle.usuario.adm}"
                                              value="Consultar CNPJ no Financeiro da Pacto"/>
                                <br/>
                                <p:selectBooleanCheckbox value="#{EmpresaControle.criarBanco}" styleClass="comBorda">
                                    <p:ajax event="change" update=":fmLay:testeCriar"/>
                                </p:selectBooleanCheckbox>
                                <h:outputText value="Criar banco"/>

                                <h:panelGroup layout="block" id="testeCriar">
                                    <h:panelGroup layout="block" styleClass="internoMenuLateral"
                                                  style="padding-top: 10px"
                                                  rendered="#{EmpresaControle.criarBanco}">
                                        <p:selectOneMenu value="#{EmpresaControle.schemaBancoEnum}"
                                                         style="vertical-align: middle;">
                                            <f:selectItems value="#{EmpresaControle.listaSchema}"/>
                                        </p:selectOneMenu>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>

                            <script>
                                atualizarSelectMany();
                            </script>

                            <h:panelGroup id="pnlBotoes" layout="block" style="margin-bottom: 6px;"
                                          styleClass="pull-right">
                                <p:commandLink styleClass="btn btn-primary" action="#{EmpresaControle.nova}"
                                               update=":fmLay:groupAddEdit :fmLay:dados"
                                               ajax="true">
                                    <i class="fa-icon-plus-sign-alt"/> #{title['cadastros.nova']}
                                </p:commandLink>

                                <p:commandLink styleClass="btn btn-primary" action="#{EmpresaControle.gravar(false)}"
                                               update=":fmLay:groupAddEdit :fmLay:dados"
                                               oncomplete="PF('empresaDialog').hide();"
                                               ajax="true">
                                    <i class="fa-icon-save"/> #{title['cadastros.salvar']}
                                </p:commandLink>

                                <p:commandLink styleClass="btn btn-primary" action="#{EmpresaControle.excluir()}"
                                               rendered="#{empty EmpresaControle.empresaSelecionada.roboControle}"
                                               update=":fmLay:groupAddEdit :fmLay:dados"
                                               ajax="true">
                                    <i class="fa-icon-remove"/> #{title['cadastros.excluir']}
                                </p:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </p:dialog>

            <p:dialog header="Selecione empresa  da  chave #{EmpresaControle.empresaSelecionada.name}" widgetVar="modalEmpresaMigracao" minHeight="40"
                      modal="true"
                      id="dlgModalEmpresaMigracao">
                <h:panelGroup id="pnlDadosMigrarEmpresa" styleClass="pnlDadosSubirEmpresa">
                    <p:dataTable value="#{EmpresaControle.empresaSelecionada.empresas}" var="empresaJSON">
                            <p:column headerText="Código" style="text-align: center">
                                <h:outputText value="#{empresaJSON.codigo}"/>
                            </p:column>
                            <p:column headerText="Empresa">
                                <h:outputText value="#{empresaJSON.nome}"/>
                            </p:column>
                            <p:column headerText="Selecionar" style="text-align: center">
                                <p:commandLink partialSubmit="true" value="Selecionar"
                                               style="padding: 0" ajax="false"
                                               action="#{EmpresaControle.irParaMigracaoDados(EmpresaControle.empresaSelecionada , empresaJSON.codigo)}"/>
                            </p:column>
                        </p:dataTable>
                </h:panelGroup>
            </p:dialog>


        </ui:define>
        <ui:define name="JS">
            <h:outputScript library="js" name="cadastrosV2.js"/>
        </ui:define>
    </ui:decorate>
</html>
