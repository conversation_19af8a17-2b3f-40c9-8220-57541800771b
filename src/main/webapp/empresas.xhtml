<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="CSS">
        <style  type="text/css">
            .ui-selectmanycheckbox label, .ui-selectoneradio label {
                display: block !important;
                margin-right: 20px !important;
                margin-left: 5px !important;
                background: none;
            }
            .ui-toggleswitch {
                vertical-align: bottom;
            }
            .gridRecente .ui-datagrid-data{
                width: auto !important;
            }
            .gridRecente .ui-datagrid-content{
                border: none !important;
            }
            .tabelaUsuarios table tbody tr td a {
                padding: 8px 0 8px 0;
            }

            .ui-menu .ui-menu-dynamic .ui-widget .ui-widget-content .ui-corner-all ui-helper-clearfix .ui-shadow {
                z-index: 3000 !important;
            }

            .tabelaUsuarios table tbody tr:nth-child(odd) {
                background-color: #FFFFFF !important;
            }

            .tabelaUsuarios table tbody tr:nth-child(odd):hover {
                background-color: #CBCBCB !important;
            }

            .w10 {
                width: 10% !important;
                display: initial !important;
                padding: 2px !important;
            }

            .ui-autocomplete-panel li {
                float: none !important;
            }
            .linkGame {
                margin-top: 5px;
                margin-left: 3px;
                -webkit-transition: -webkit-transform 1s ease-in-out;
                transition: transform 1s ease-in-out;
            }
            .linkGame:hover {
                -webkit-transform: rotate(90deg) scale(1.1);
                transform: rotate(90deg) scale(1.1);
            }
            .ui-datalist ul {
                padding: 0px 10px 0 20px;
                margin: 5px 0;
            }

            .ui-datalist {
                margin-bottom: 10px
            }

            .ui-noborder tr.ui-widget-content {
                background: none;
            }

            .panelIconMod {

            }

            .panelIconMod:hover {
                background: #eeeeee;
                text-decoration: none;
            }

            .iconModuloText {
                text-decoration: none !important;
            }

            .iconModuloText:hover {
                text-decoration: none !important;
            }

            .iconModulo {
                display: block;
                width: 62px;
                padding: 10px;
                margin: auto;
            }
        </style>

    </ui:define>
    <ui:define name="conteudo">

        <h:panelGroup layout="block" styleClass="jumbotron ">
            <div class="bread span12">


                <h:panelGroup layout="block" styleClass="pull-right">
                    <p:commandLink process="@this" action="#{OamdControle.mudarVisualizacao('HOME')}"
                                   update="geralpainel">
                        <i class="fa-icon-table"/>
                    </p:commandLink>
                    <p:commandLink process="@this" action="#{OamdControle.mudarVisualizacao('GRADE')}"
                                   update="geralpainel">
                        <i class="fa-icon-th-large"/>
                    </p:commandLink>

                </h:panelGroup>
            </div>

            <h:panelGroup layout="block" styleClass="container pull-left scrollInfinito" id="geralpainel">

                <h:panelGroup id="recentes">
                    <h:panelGroup rendered="#{fn:length(OamdControle.empresasHistorico) > 0 and fn:length(OamdControle.listaEmpresasFiltrada) == fn:length(OamdControle.listaEmpresas)}"
                                  layout="block"
                                  style="margin-left: 20px; display: block;">
                        <h:outputText style="font-family: 'Oxygen',sans-serif;" value="Últimas empresas acessadas"/>
                        <p:dataGrid columns="6" value="#{OamdControle.empresasHistorico}" var="empresaRecente"
                                    styleClass="gridRecente">

                                <p:commandLink style="margin-right: 20px; margin-top: 6px;"
                                               title="Go to Pacto ZillyonWeb"
                                               styleClass="thumbnail linkRecente"
                                               ajax="true"
                                               global="false" update=":fmLay:modalogin"
                                               action="#{OamdControle.abrirModal(empresaRecente)}">
                                    <p:graphicImage alt="#{empresaRecente.visualName}"
                                                    style="width: 136px; height: 50px;" value="#{empresaRecente.urlLogo}"/>
                                </p:commandLink>


                        </p:dataGrid>

                    </h:panelGroup>
                </h:panelGroup>



                <h:panelGroup style="margin: 20px 10px 10px 20px;"
                        styleClass="label label-warning">#{fn:length(OamdControle.listaEmpresas)} grupos empresariais</h:panelGroup>
                <h:panelGroup id="pnlLabelFiltrados">

                    <h:panelGroup rendered="#{OamdControle.usuario.pmg and OamdControle.codigoCarteiraFiltro != null}"
                                  style="margin-left: 6px;" styleClass="label label-warning">
                        <h:outputText value="#{OamdControle.descricaoCarteira}"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{OamdControle.totalEmpresas > 0}"
                                  style="margin-left: 6px;"
                                  styleClass="label label-warning">Total empresas: #{OamdControle.totalEmpresas}</h:panelGroup>
                    <h:panelGroup
                            rendered="#{fn:length(OamdControle.listaEmpresasFiltrada) != fn:length(OamdControle.listaEmpresas)}"
                            style="margin-left: 6px;"
                            styleClass="label label-warning">#{fn:length(OamdControle.visualizacao == 'LISTA' ? OamdControle.empresasConcatenadas : OamdControle.listaEmpresasFiltrada)} filtrados</h:panelGroup>
                </h:panelGroup>
                <h:outputLink id="abrirUrl" value="#{OamdControle.empresaSelecionada.urlLoginPreparada}" style="display: none;"/>
                <h:panelGroup layout="block" styleClass="listaEmpresasFiltradas " id="contlistas">
                    <h:panelGroup rendered="#{OamdControle.visualizacao == 'GRADE'}">
                        <ui:repeat value="#{OamdControle.listaEmpresasFiltradaSub}" var="empresa">
                                <span class="span2">
                                    <p:commandLink style="margin-top: 6px;"
                                                   title="Go to Pacto ZillyonWeb"
                                                   styleClass="thumbnail"
                                                   ajax="true"
                                                   global="false" update=":fmLay:modalogin, :fmLay:recentes"
                                                   action="#{OamdControle.abrirModal(empresa)}">
                                        <p:graphicImage alt="#{empresa.identificadorEmpresa != null ? empresa.identificadorEmpresa : empresa.name}"
                                                        style="width: 136px; height: 50px;" value="#{empresa.urlLogo}"/>
                                    </p:commandLink>
                                    <p:commandLink rendered="#{OamdControle.usuario.adm || OamdControle.usuario.permiteAlterarModulosChave}"
                                                   ajax="true"
                                                   update=":fmLay:modal, :fmLay:recentes"
                                                   oncomplete="modalDialog.show();"
                                                   action="#{OamdControle.selecionarEmpresa(empresa)}"
                                                   title="Opções">
                                        <span style="font-size: 8px;" class="label">#{empresa.visualName}</span>
                                    </p:commandLink>


                                    <h:panelGroup rendered="#{!OamdControle.usuario.adm}"
                                                  style="font-size: 8px;" styleClass="label">
                                        #{empresa.name}
                                    </h:panelGroup>

                                    <p:commandLink rendered="#{fn:contains(empresa.modulos, 'TR')}"
                                                   target="_blank" style="margin-top: 6px;"
                                                   ajax="true"
                                                   global="false" update=":fmLay:modalogin, :fmLay:modallogado"
                                                   action="#{OamdControle.abrirModal(empresa)}">
                                        <span title="Go To Pacto Treino" style="font-size: 8px;"
                                              class="label label-important">TR</span>
                                    </p:commandLink>

                                    <p:commandLink rendered="#{OamdControle.usuario.pmg}"
                                                   ajax="true"
                                                   update=":fmLay:modalpmg"
                                                   oncomplete="modalDialogpmg.show();"
                                                   action="#{OamdControle.selecionarCarteiraPMG(empresa)}"
                                                   title="Opções">
                                        <span style="font-size: 8px;" class="label">Carteira PMG</span>
                                    </p:commandLink>



                                    <h:panelGroup
                                            rendered="#{OamdControle.usuario.adm and fn:length(empresa.empresas) > 0}"
                                            style="font-size: 8px; margin-left: 6px;" styleClass="label label-warning">
                                        <h:outputText value="#{fn:length(empresa.empresas)}"
                                                      title="Nº de empresas desse Grupo"/>
                                    </h:panelGroup>
                                </span>
                        </ui:repeat>
                    </h:panelGroup>
                    <h:panelGroup style="margin: 6px 6px 6px 6px;" rendered="#{OamdControle.visualizacao == 'LISTA'}">
                        <h:panelGroup layout="block" styleClass="container-fluid noSlider" style="width: 100%;">
                            <h:panelGroup layout="block" id="dados"
                                          style="width: 100%; margin-left: auto; margin-right: auto;"
                                          styleClass="span12 caixaPrincipal">
                                <h4>Empresas Cadastradas</h4>
                                <hr/>
                                <p:outputPanel layout="block" styleClass="pesq pesq-tabelas buscaAuxiliar" id="filtrar">

                                    <p:commandButton icon="ui-icon-extlink" ajax="false" partialSubmit="false"
                                                     value="Exportar"
                                                     styleClass="pull-right"
                                                     style="margin-top: 2px; margin-bottom: 2px;">
                                        <p:dataExporter type="xls" target="tblEmpresas" fileName="OAMD"
                                                        postProcessor="#{OamdControle.postProcessXLS}"/>
                                    </p:commandButton>

                                </p:outputPanel>
                                <p:dataTable id="tblEmpresas" value="#{OamdControle.listaTela}" var="empJSON">
                                    <f:facet name="header">
                                        <center>
                                            <h:outputText value="#{fn:length(OamdControle.listaTela)} empresas"/>
                                        </center>
                                    </f:facet>
                                    <p:column sortBy="#{empJSON.nome}">
                                        <f:facet name="header">
                                            <h:outputText value="Empresa"/>
                                        </f:facet>
                                        <p:commandLink title="Opções"
                                                       rendered="#{OamdControle.usuario.adm }"
                                                       ajax="true"
                                                       update=":fmLay:modal"
                                                       oncomplete="modalDialog.show();"
                                                       action="#{OamdControle.selecionarEmpresaJSON(empJSON)}">
                                            <h:outputText value="#{empJSON.nome}"/>
                                        </p:commandLink>
                                        <h:outputText rendered="#{!OamdControle.usuario.adm}" value="#{empJSON.nome}"/>
                                    </p:column>

                                    <p:column sortBy="#{empJSON.grupoEmpresarial}">
                                        <f:facet name="header">
                                            <h:outputText value="Grupo"/>
                                        </f:facet>
                                        <h:outputText value="#{empJSON.grupoEmpresarial}"/>
                                    </p:column>

                                    <p:column sortBy="#{empJSON.chave}">
                                        <f:facet name="header">
                                            <h:outputText value="Chave"/>
                                        </f:facet>
                                        <h:outputText value="#{empJSON.chave}"/>
                                    </p:column>


                                    <p:column sortBy="#{empJSON.modulos}" exportable="false">
                                        <f:facet name="header">
                                            <h:outputText value="Módulos"/>
                                        </f:facet>
                                        <h:outputLink target="_blank" style="margin-top: 6px;"
                                                      value="#{empJSON.urlZW}">
                                            <span title="Go to Pacto ZillyonWeb" style="font-size: 8px;"
                                                  class="label label-info">ZW</span>
                                        </h:outputLink>

                                        <h:outputLink rendered="#{fn:contains(empJSON.modulos, 'TR')}"
                                                      target="_blank" style="margin-top: 6px;"
                                                      value="#{empJSON.urlZW}">
                                            <span title="Go To Pacto Treino" style="font-size: 8px;"
                                                  class="label label-important">TR</span>
                                        </h:outputLink>
                                    </p:column>

                                    <p:column sortBy="#{empJSON.dataexpiracao}" style="text-align: center;"
                                              rendered="#{OamdControle.filtroTipoServidor == 'TEM_BLOQUEIO'}">
                                        <f:facet name="header">
                                            <h:outputText value="Expiração"/>
                                        </f:facet>
                                        <h:outputText value="#{empJSON.dataexpiracao}">
                                            <f:convertDateTime pattern="dd/MM/yyyy"
                                                               locale="#{OamdControle.localeDefault}"/>
                                        </h:outputText>
                                        <h:outputText styleClass="label label-important"
                                                      rendered="#{!empJSON.disponivel}" value="Indisponível"/>
                                    </p:column>


                                    <p:column headerText="Opções" style="text-align: center;" exportable="false"
                                              rendered="#{OamdControle.filtroTipoServidor == 'TEM_BLOQUEIO'}">
                                        <p:menuButton rendered="#{empJSON.codigo != 0}" value="Bloquear"
                                                      style="margin-right: 2px">
                                            <p:menuitem value="Agora" action="#{OamdControle.bloquear(0, empJSON)}"
                                                        update=":fmLay:tabFuncoes:details"/>
                                            <p:menuitem value="Em 2 dias" action="#{OamdControle.bloquear(2, empJSON)}"
                                                        update=":fmLay:tabFuncoes:details"/>
                                            <p:menuitem value="Em 5 dias" action="#{OamdControle.bloquear(5, empJSON)}"
                                                        update=":fmLay:tabFuncoes:details"/>
                                        </p:menuButton>
                                        <p:commandLink value="Desbloquear"
                                                       rendered="#{empJSON.codigo != 0}"
                                                       action="#{OamdControle.desbloquear(empJSON)}"
                                                       update=":fmLay:tabFuncoes:details"/>
                                    </p:column>

                                </p:dataTable>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup style="margin: 6px 6px 6px 6px;" rendered="#{OamdControle.visualizacao == 'HOME'}">
                        <h:panelGroup layout="block" styleClass="container-fluid noSlider" style="width: 100%;">
                            <h:panelGroup layout="block" id="dadoshome"
                                          style="width: 100%; margin-left: auto; margin-right: auto;"
                                          styleClass="span12 caixaPrincipal">
                                <p:dataTable id="tblEmpresasHome" styleClass="tabelaUsuarios"
                                             value="#{OamdControle.listaEmpresasFiltradaSub}" var="empHome">

                                    <p:column rendered="#{OamdControle.permiteAcessarConfiguracoesChave or OamdControle.usuario.permiteControlarRecursoPadrao}"
                                            style="text-align: center">
                                        <f:facet name="header">
                                            <h:outputText value="ADM"/>
                                        </f:facet>
                                        <div style="display: flex">
                                            <p:commandLink
                                                    rendered="#{OamdControle.permiteAcessarConfiguracoesChave and fn:contains(empHome.modulos, 'ZW')}"
                                                    ajax="true"
                                                    process="@this"
                                                    update=":fmLay:modal, :fmLay:recentes"
                                                    oncomplete="modalDialog.show();"
                                                    title="Configurações"
                                                    action="#{OamdControle.selecionarEmpresa(empHome, false, true)}"
                                                    style="text-align: center;font-size: 3vh;">
                                                <i class="fa-icon-cog"/>
                                            </p:commandLink>
                                            <p:commandLink
                                                    rendered="#{OamdControle.usuario.permiteControlarRecursoPadrao and fn:contains(empHome.modulos, 'ZW')}"
                                                    ajax="true"
                                                    process="@this"
                                                    update=":fmLay:modalRecursoPadrao"
                                                    oncomplete="modalDialogRecursoPadrao.show();"
                                                    title="Configuração Recurso Padrão"
                                                    action="#{OamdControle.selecionarEmpresa(empHome, false, true)}"
                                                    style="text-align: center;font-size: 3vh;">
                                                <img src="#{resource['imagens/pct-sliders.svg']}" style="height: 27px"/>
                                            </p:commandLink>
                                            <p:commandLink
                                                    rendered="#{OamdControle.permiteAcessarConfiguracoesChave and not fn:contains(empHome.modulos, 'ZW')}"
                                                    ajax="true"
                                                    process="@this"
                                                    update=":fmLay:modalTreino, :fmLay:recentes"
                                                    oncomplete="modalDialogTreino.show();"
                                                    title="Configurações"
                                                    action="#{OamdControle.selecionarEmpresa(empHome, true, true)}"
                                                    style="text-align: center;font-size: 3vh; color: #B94A48">
                                                <i class="fa-icon-cog"/>
                                            </p:commandLink>
                                        </div>
                                    </p:column>
                                    <p:column sortBy="#{empHome.visualName}">
                                        <f:facet name="header">
                                            <h:outputText value="Empresa"/>
                                        </f:facet>
                                        <p:commandLink title="#{empHome.name}"
                                                       process="@this"
                                                       rendered="#{fn:contains(empHome.modulos, 'ZW') or fn:contains(empHome.modulos, 'TR')}"
                                                       update=":fmLay:modalogin, :fmLay:recentes"
                                                       action="#{OamdControle.abrirModal(empHome)}"
                                                       ajax="true">
                                            <h:outputText value="#{empHome.visualName}"/>
                                        </p:commandLink>
                                        <h:outputText style="padding: 10px; display: block;"
                                                      value="#{empHome.visualName}"
                                                      rendered="#{not fn:contains(empHome.modulos, 'ZW') and not fn:contains(empHome.modulos, 'TR')}"/>
                                    </p:column>

                                    <p:column sortBy="#{empHome.chave}">
                                        <f:facet name="header">
                                            <h:outputText value="Chave"/>
                                        </f:facet>
                                        <h:outputText value="#{empHome.chave}" style="font-weight: normal;"/>
                                    </p:column>

                                    <p:column sortBy="#{empHome.infoInfra}" rendered="#{OamdControle.oamdPacto}">
                                        <f:facet name="header">
                                            <h:outputText value="Infra"/>
                                        </f:facet>
                                        <h:outputText value="#{empHome.infoInfra.descricao}" style="font-weight: bold;"/>
                                    </p:column>

                                    <p:column rendered="#{OamdControle.oamdPacto}">
                                        <f:facet name="header">
                                            <h:outputText value="Módulos"/>
                                        </f:facet>
                                        <h:outputLink target="_blank" style="margin-top: 6px;" class="w10"
                                                      rendered="#{fn:contains(empHome.modulos, 'ZW')}"
                                                      value="#{empHome.urlLoginPreparada}">                                        
                                            <span title="Go to Pacto ZillyonWeb" style="font-size: 8px;"
                                                  class="label label-info">ZW</span>
                                        </h:outputLink>

                                        <h:outputLink rendered="#{fn:contains(empHome.modulos, 'TR')}"
                                                      target="_blank" style="margin-top: 6px;" class="w10"
                                                      value="#{empHome.urlLoginPreparada}">
                                            <span title="Go To Pacto Treino" style="font-size: 8px;"
                                                  class="label label-important">TR</span>
                                        </h:outputLink>

                                        <h:outputLink rendered="#{OamdControle.usuario.podeVisualizarGame and not empty empHome.urlGame}" title="Game of Results"
                                                      target="gameresults" style="margin-top: 6px;" class="w10"
                                                      value="#{empHome.urlLoginGame}">
                                            <p:graphicImage library="imagens"
                                                            name="game.svg"
                                                            cache="true"
                                                            width="22"
                                                            height="17"
                                                            styleClass="linkGame"/>
                                        </h:outputLink>
                                    </p:column>
                                </p:dataTable>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>

                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
        <ui:fragment rendered="#{OamdControle.scroll}">
            <script>
                aplicarScroll();
            </script>
        </ui:fragment>

        <p:remoteCommand name="carregaMais" action="#{OamdControle.mais()}" update=":fmLay:contlistas"/>

        <p:dialog widgetVar="modalDialog" modal="true" id="modal" showEffect="fade" hideEffect="fade" resizable="true"
                  width="1200">
            <f:facet name="header">
                <h:outputText value="Unidades"/>
            </f:facet>

            <p:tabView id="tabFuncoes" style="max-height: 650px; overflow-y: auto">
                <p:tab title="Detalhes">
                    <h:panelGroup layout="block" id="details">
                        <h:outputText value="#{OamdControle.empresaSelecionada.name}"/>
                        <p:dataTable value="#{OamdControle.empresaSelecionada.empresas}" var="empresaJSON">
                            <p:column headerText="Código" style="text-align: center">
                                <h:outputText value="#{empresaJSON.codigo}"/>
                            </p:column>
                            <p:column headerText="Empresa">
                                <h:outputText value="#{empresaJSON.nome}"/>
                            </p:column>
                            <p:column headerText="CNPJ">
                                <h:outputText value="#{empresaJSON.cnpj}"/>
                            </p:column>
                            <p:column headerText="Expiração" style="text-align: center">
                                <h:outputText value="#{empresaJSON.dataexpiracao}">
                                    <f:convertDateTime pattern="dd/MM/yyyy" locale="#{OamdControle.localeDefault}"/>
                                </h:outputText>
                            </p:column>
                            <p:column headerText="Suspensa?" style="text-align: center; font-weight: bolder">
                                <h:outputText value="SIM" rendered="#{empresaJSON.suspensa}" style="color: red;" />
                                <h:outputText value="NÃO" rendered="#{!empresaJSON.suspensa}" />
                            </p:column>
                            <p:column headerText="Data Suspensão" style="text-align: center">
                                <h:outputText value="#{empresaJSON.datasuspensao}">
                                    <f:convertDateTime pattern="dd/MM/yyyy" locale="#{OamdControle.localeDefault}"/>
                                </h:outputText>
                            </p:column>
                            <p:column headerText="Bloqueio Parcial" style="text-align: center; padding: 2px;"
                                      rendered="#{OamdControle.oamdPacto and OamdControle.usuario.adm}">
                                <p:menuButton value="Bloquear" rendered="#{!empresaJSON.suspensa}">
                                    <p:menuitem value="Agora" action="#{OamdControle.bloquear(0, empresaJSON)}"
                                                update=":fmLay:tabFuncoes:details"/>
                                    <p:menuitem value="Em 2 dias" action="#{OamdControle.bloquear(2, empresaJSON)}"
                                                update=":fmLay:tabFuncoes:details"/>
                                    <p:menuitem value="Em 5 dias" action="#{OamdControle.bloquear(5, empresaJSON)}"
                                                update=":fmLay:tabFuncoes:details"/>
                                    <p:menuitem value="Bloqueio personalizado"
                                                oncomplete="modalBloqueio.show();"
                                                update=":fmLay:tabBloqueio"
                                                title="Bloqueio Personalizado"/>
                                    <p:menuitem value="Desbloquear" rendered="#{empresaJSON.bloqueada}"
                                                action="#{OamdControle.desbloquear(empresaJSON)}"
                                                update=":fmLay:tabFuncoes:details"/>
                                    <p:menuitem value="Desbloquear" rendered="#{empresaJSON.bloqueada}"
                                                action="#{OamdControle.desbloquear(empresaJSON)}"
                                                update=":fmLay:tabFuncoes:details"/>
                                </p:menuButton>
                            </p:column>
                            <p:column headerText="Ação"
                                      style="max-width: 100px; padding: 3px;"
                                      rendered="#{OamdControle.oamdPacto and OamdControle.usuario.adm}">
                                <p:commandLink value="Desbloquear"
                                               title="Esta ação desbloqueia e também remove suspensão definitiva permitindo que o cliente volte a acessar o sistema."
                                               rendered="#{empresaJSON.suspensa}"
                                               styleClass="btn btn-sm btn-primary"
                                               style="padding: 1px; width: 100px; margin-left: 18px;"
                                               action="#{OamdControle.desbloquear(empresaJSON)}"
                                               update=":fmLay:tabFuncoes:details"/>
                                <p:commandLink value="Suspender"
                                               title="Esta ação suspende o acesso total do cliente ao sistema e só pode ser desfeita manualmente, mesmo após o cliente realizar os pagamentos em atraso."
                                               rendered="#{!empresaJSON.suspensa}"
                                               styleClass="btn btn-sm btn-danger"
                                               style="padding: 1px; width: 100px; margin-left: 18px;"
                                               action="#{OamdControle.suspender(empresaJSON)}"
                                               update=":fmLay:tabFuncoes:details"/>
                            </p:column>
                        </p:dataTable>
                    </h:panelGroup>
                </p:tab>
                <p:tab title="DCC" rendered="#{OamdControle.oamdPacto and OamdControle.usuario.adm}">
                    <h:panelGroup layout="block" id="dcc">
                        <h:outputText value="#{OamdControle.empresaSelecionada.name}"/>
                        <h:outputText value="Os créditos de DCC dessa empresa são configurados na rede da empresa."
                                      style="margin-top: 20px; display: block; font-weight: bold"
                                      rendered="#{OamdControle.redeDCC ne null and OamdControle.redeDCC.creditoPorRede}"/>
                        <p:dataTable value="#{OamdControle.empresaSelecionada.empresas}"
                                     rendered="#{OamdControle.redeDCC eq null or not OamdControle.redeDCC.creditoPorRede}"
                                     var="empresaJSON">

                            <p:column headerText="Código" style="text-align: center">
                                <h:outputText value="#{empresaJSON.codigo}"/>
                            </p:column>

                            <p:column headerText="Empresa">
                                <h:outputText value="#{empresaJSON.nome}"/>
                            </p:column>

                            <p:column headerText="Créditos" style="text-align: center">
                                <h:outputText value="#{empresaJSON.credito_Apresentar} #{empresaJSON.infoCredito}"/>
                            </p:column>

                            <p:column headerText="Crédito" style="text-align: center">
                                <p:commandLink rendered="#{empresaJSON.apresentarAdicionarCredito}"
                                               value="Adicionar"
                                               style="padding: 0px"
                                               update=":fmLay:modalCobrancaPacto"
                                               oncomplete="modalDialogCobrancaPacto.show();"
                                               action="#{OamdControle.selecionarUnidEmpresa(empresaJSON)}"
                                               title="Adicionar Crédito DCC">
                                </p:commandLink>

                                <p:commandLink rendered="#{empresaJSON.apresentarGerarCobranca}"
                                               value="Gerar Cobrança"
                                               style="padding: 0px"
                                               update=":fmLay:modalCobrancaPacto"
                                               oncomplete="modalDialogCobrancaPacto.show();"
                                               action="#{OamdControle.selecionarUnidEmpresa(empresaJSON)}"
                                               title="Gerar Cobrança Pós-Pago DCC">
                                </p:commandLink>
                            </p:column>

                            <p:column headerText="Remover" style="text-align: center">
                                <p:menuButton value="Remover" rendered="#{empresaJSON.tipoPrepago}">
                                    <p:menuitem value="100" action="#{OamdControle.removerCreditoDCC(100, empresaJSON)}"
                                                update=":fmLay:tabFuncoes:dcc"/>
                                    <p:menuitem value="500" action="#{OamdControle.removerCreditoDCC(500, empresaJSON)}"
                                                update=":fmLay:tabFuncoes:dcc"/>
                                    <p:menuitem value="1000"
                                                action="#{OamdControle.removerCreditoDCC(1000, empresaJSON)}"
                                                update=":fmLay:tabFuncoes:dcc"/>
                                    <p:menuitem value="5000"
                                                action="#{OamdControle.removerCreditoDCC(5000, empresaJSON)}"
                                                update=":fmLay:tabFuncoes:dcc"/>
                                    <p:menuitem value="10000"
                                                action="#{OamdControle.removerCreditoDCC(10000, empresaJSON)}"
                                                update=":fmLay:tabFuncoes:dcc"/>
                                </p:menuButton>
                            </p:column>
                            <p:column headerText="Expiração DCC" style="text-align: center">
                                <h:outputText value="#{empresaJSON.dataexpiracaocreditodcc}"/>
                            </p:column>
                            <p:column headerText="Bloquear DCC" style="text-align: center">
                                <p:menuButton value="Bloquear">
                                    <p:menuitem value="Agora" action="#{OamdControle.bloquearDCC(0, empresaJSON)}"
                                                update=":fmLay:tabFuncoes:dcc"/>
                                    <p:menuitem value="Em 2 dias" action="#{OamdControle.bloquearDCC(2, empresaJSON)}"
                                                update=":fmLay:tabFuncoes:dcc"/>
                                    <p:menuitem value="Em 5 dias" action="#{OamdControle.bloquearDCC(5, empresaJSON)}"
                                                update=":fmLay:tabFuncoes:dcc"/>
                                </p:menuButton>
                            </p:column>
                            <p:column headerText="Desbloquear DCC" style="text-align: center">
                                <p:commandLink value="Desbloquear"
                                               style="padding: 0px"
                                               action="#{OamdControle.desbloquearDCC(empresaJSON)}"
                                               update=":fmLay:tabFuncoes:dcc"/>
                            </p:column>
                            <p:column headerText="Tipo Cobrança">
                                <h:outputText value="#{empresaJSON.tipoCobrancaPacto_Apresentar}"/>
                            </p:column>
                            <p:column headerText="Configurações Cobrança" style="text-align: center">
                                <p:commandLink value="Configurar"
                                               style="padding: 0px"
                                               update=":fmLay:modalCobrancaConfig"
                                               oncomplete="modalDialogCobrancaConfig.show();"
                                               action="#{OamdControle.selecionarUnidEmpresaConfigurar(empresaJSON)}"
                                               title="Configurar data de início do método Pós-Pago">
                                </p:commandLink>
                            </p:column>
                            <p:column headerText="Processos" style="text-align: center">
                                <p:commandLink value="Início Pós-Pago"
                                               rendered="#{empresaJSON.permiteAjustarInicioCobranca}"
                                               style="padding: 0px"
                                               update=":fmLay:modalDataPosPago"
                                               oncomplete="modalDataPosPago.show();"
                                               action="#{OamdControle.selecionarUnidEmpresaConfigurarInicioPosPago(empresaJSON)}"
                                               title="Configurar ">
                                </p:commandLink>

                                <p:commandLink rendered="#{empresaJSON.permiteBonusCredito}"
                                               value="Bônus Crédito"
                                               style="padding: 0 0 0 5px;"
                                               update=":fmLay:modalCreditoPactoBonus"
                                               action="#{OamdControle.abrirAdicionarBonusCreditoPacto(empresaJSON)}"
                                               title="Bônus Crédito">
                                </p:commandLink>
                            </p:column>
                        </p:dataTable>
                    </h:panelGroup>
                </p:tab>
                <p:tab title="Log Bloqueio" rendered="#{OamdControle.oamdPacto and OamdControle.usuario.adm}">
                    <h:panelGroup layout="block" id="logBloqueios">
                        <p:dataTable value="#{OamdControle.empresaSelecionada.logBloqueios}" var="logBloqueio"
                                     rows="10"
                                     paginator="true"
                                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                     rowsPerPageTemplate="5,10,15">
                            <p:column headerText="Data/Hora" style="text-align: center">
                                <h:outputText value="#{logBloqueio.dataAlteracao}"/>
                            </p:column>

                            <p:column headerText="Empresa" style="text-align: center">
                                <h:outputText value="#{logBloqueio.obterNome(OamdControle.empresaSelecionada)}"/>
                            </p:column>


                            <p:column headerText="Ação" style="text-align: center">
                                <h:outputText value="#{logBloqueio.acao}"/>
                            </p:column>

                            <p:column headerText="Responsável" style="text-align: center">
                                <h:outputText value="#{logBloqueio.responsavel}"/>
                            </p:column>

                            <p:column headerText="Observação" style="text-align: center">
                                <h:outputText value="#{logBloqueio.observacao}"/>
                            </p:column>

                        </p:dataTable>
                    </h:panelGroup>
                </p:tab>

                <p:tab title="Ferramenta de Atendimento" rendered="#{OamdControle.empresaSelecionada != null and OamdControle.usuario.adm}">
                    <h:panelGroup id="pnlFerramentaAtendimento">
                        <p:selectOneRadio id="rdbFerramentaAtendimento"
                                       value="#{OamdControle.ferramentaAtual}"
                                       itemLabel="Ferramenta de Atendimento"
                                       rendered="#{OamdControle.empresaSelecionada != null and OamdControle.usuario.adm}">
                            <f:selectItem itemLabel="Nenhuma" itemValue="none" />
                            <f:selectItem itemLabel="Octadesk" itemValue="octadesk" itemDisabled="true" />
                            <f:selectItem itemLabel="Movidesk" itemValue="movidesk" />
                            <f:selectItem itemLabel="Gymbot" itemValue="gymbot" />
                            <p:ajax event="change" listener="${OamdControle.toggleFerramentaAtendimento}" update="pnlFerramentaAtendimento" />
                        </p:selectOneRadio>

                        <h:panelGroup id="pnlMovidesk" layout="block"
                                      rendered="#{OamdControle.empresaSelecionada.utilizarMoviDesk}"
                                      style="margin-top: 12px">
                            <div class="col-md-12">
                                <p:selectBooleanCheckbox id="chkbUtilizarChatMoviDesk" itemLabel="Utilizar Chat"
                                                         value="#{OamdControle.empresaSelecionada.utilizarChatMoviDesk}">
                                    <p:ajax event="change" listener="${OamdControle.toggleHabilitarChatMoviDesk}"/>
                                </p:selectBooleanCheckbox>
                            </div>

                            <div class="col-md-12">
                                <h:panelGroup id="pgGruposChat">
                                    <p:outputLabel for="radioGrupoMovidesk" value="Grupo:" />
                                    <p:selectOneRadio id="radioGrupoMovidesk"
                                                      value="#{OamdControle.empresaSelecionada.grupoChatMoviDesk}">
                                        <f:selectItems value="#{OamdControle.gruposChatMoviDesk}"/>
                                        <p:ajax listener="#{OamdControle.alterarGrupoChatMoviDesk}"/>
                                    </p:selectOneRadio>
                                </h:panelGroup>
                            </div>
                        </h:panelGroup>

                        <p:commandButton id="gravarFerramentaAtendimento"
                                         style="margin-top: 12px"
                                         action="#{OamdControle.gravarFerramentaAtendimento}"
                                         value="Salvar"/>

                    </h:panelGroup>
                </p:tab>

                <p:tab title="Módulos" rendered="#{OamdControle.apresentarAbaModulos}">
                    <div id="panelModuloss">
                        <h5 style="margin-left: 5px">Módulos</h5>
                        <p:selectManyCheckbox id="gridModulos" value="#{OamdControle.empresaSelecionada.modulosEmpresa}"
                                              layout="grid" columns="4" style="margin: 5px !important;">
                            <f:selectItems value="#{OamdControle.modEmpresas}" var="modulo" itemLabel="#{modulo}"
                                           itemValue="#{modulo}"/>
                        </p:selectManyCheckbox>

                        <p:commandButton value="Processar" style="margin-top: 5px" update="gridModulos"
                                         action="#{OamdControle.toggleHabilitaModulos(false)}"/>
                    </div>
                </p:tab>
            </p:tabView>
            <p:messages id="messagesModal" showSummary="true" showDetail="false" autoUpdate="true" closable="true"/>
        </p:dialog>

        <p:dialog widgetVar="modalDialogRecursoPadrao" modal="true"
                  id="modalRecursoPadrao" showEffect="fade" hideEffect="fade" resizable="true"
                  width="700">
            <f:facet name="header">
                <h:outputText value="Recurso Padrão"/>
            </f:facet>

            <div id="panelRecursoPadrao">
                <h5 style="margin-left: 5px">Empresa</h5>

                <p:remoteCommand name="alterarEmpresaRecursoPadrao" update="divItensRecursoPadrao"
                                 action="#{OamdControle.alterarEmpresaRecursoPadrao}"/>
                <h:selectOneMenu id="empresaRecursoPadrao"
                                 value="#{OamdControle.empresaRecursoPadrao}"
                                 onchange="alterarEmpresaRecursoPadrao();">
                    <f:selectItems value="#{OamdControle.selectItemEmpresaRecursoPadrao}"/>
                </h:selectOneMenu>

                <h:panelGroup layout="block" id="divItensRecursoPadrao">

                    <p:selectManyCheckbox id="gridRecursoPadrao"
                                          rendered="#{OamdControle.empresaRecursoPadrao != null}"
                                          value="#{OamdControle.tiposInfoMigracaoPadrao}"
                                          layout="grid" columns="4" style="margin: 5px !important;">
                        <f:selectItems value="#{OamdControle.selectItemTipoInfoMigracao}" var="modulo"
                                       itemLabel="#{modulo}"
                                       itemValue="#{modulo}"/>
                    </p:selectManyCheckbox>

                    <p:commandButton value="Salvar" id="btnSalvarRecursos"
                                     rendered="#{OamdControle.empresaRecursoPadrao != null}"
                                     style="margin-top: 5px" update="divItensRecursoPadrao"
                                     action="#{OamdControle.alterarRecursoPadrao}"/>
                </h:panelGroup>
            </div>
            <p:messages id="messagesModalRecurso" showSummary="true" showDetail="false" autoUpdate="true" closable="true"/>
        </p:dialog>

        <p:dialog widgetVar="modalDialogProcessos" modal="true" id="modalDialogProcessos" showEffect="fade" hideEffect="fade" resizable="true"
                  width="1042">
            <f:facet name="header">
                <h:outputText value="Processos de Implantação"/>
            </f:facet>

            <p:tabView id="tabProcessos">
                <p:tab title="Processos">
                    <h:panelGroup layout="block" id="process">
                        <h:outputText value="#{OamdControle.empresaSelecionada.name}" style="font-size: 20px"/>

                        <p:dataTable value="#{OamdControle.empresaSelecionada.empresas}" var="empresaProcesso"
                                     rows="10"
                                     paginator="true"
                                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                     rowsPerPageTemplate="5,10,15">
                            <p:column headerText="Empresa" style="text-align: center">
                                <p:outputLabel value="#{empresaProcesso.nome}" style="font-weight: bold; text-align: center"/>
                            </p:column>
                            <p:column headerText="Processo" style="text-align: center">
                                <p:outputLabel value="Configurar Latitude e Longitude" style="font-weight: bold; text-align: center"/>
                            </p:column>
                            <p:column headerText="Ação" style="text-align: center">
                                <p:commandLink style="width: 50%; text-align: center; display: inline-table"
                                               styleClass="btn btn-primary" value="Executar"
                                               action="#{OamdControle.executarProcessoLatitudeLongitude(empresaProcesso)}"/>
                            </p:column>
                        </p:dataTable>
                    </h:panelGroup>
                </p:tab>

                <p:tab title="Log Processos">
                    <h:panelGroup layout="block" id="logProcessos">
                        <h:outputText value="#{OamdControle.empresaSelecionada.name}" style="font-size: 20px"/>

                        <p:dataTable value="#{OamdControle.empresaSelecionada.logProcessos}" var="logProcesso"
                                     rows="10"
                                     paginator="true"
                                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                     rowsPerPageTemplate="5,10,15">
                            <p:column headerText="Data/Horário" style="text-align: center">
                                <h:outputText value="#{logProcesso.dataEHora}"/>
                            </p:column>
                            <p:column headerText="Ação" style="text-align: center">
                                <p:outputLabel value="#{logProcesso.acao}"/>
                            </p:column>
                            <p:column headerText="Resultado" style="text-align: center">
                                <p:outputLabel value="#{logProcesso.resultado}"/>
                            </p:column>
                            <p:column headerText="Usuário" style="text-align: center">
                                <h:outputText value="#{logProcesso.usuario}"/>
                            </p:column>
                        </p:dataTable>
                    </h:panelGroup>
                </p:tab>
            </p:tabView>
        </p:dialog>

        <ui:include src="include_modais_dcc.xhtml"/>

        <p:dialog widgetVar="modalDialogpmg" modal="true" id="modalpmg" showEffect="fade" hideEffect="fade"
                  resizable="true" width="400">
            <f:facet name="header">
                <h:outputText value="#{OamdControle.empresaSelecionada.name}"/>
            </f:facet>
            <h:panelGroup id="painelCarteira">

                <h:outputText value="Carteira PMG:"/>
                <h:selectOneMenu value="#{OamdControle.codigoCarteira}">
                    <f:selectItem itemLabel="Nenhuma"/>
                    <f:selectItems value="#{OamdControle.usuariosPMGSelect}"/>
                </h:selectOneMenu><br/>

            </h:panelGroup>

            <center>
                <p:commandLink styleClass="btn btn-primary" action="#{OamdControle.gravarCarteira}"
                               ajax="true"
                               update=":fmLay:conteudoPag :fmLay:pnlLabelFiltrados"
                               oncomplete="modalDialogpmg.hide();">
                    <i class="fa-icon-save"/> #{title['cadastros.salvar']}
                </p:commandLink>
            </center>


        </p:dialog>

        <p:dialog widgetVar="modalLogin" onShow="document.getElementById('fmLay:btnEntrar').focus();" modal="true"
                  id="modalogin" showEffect="fade" hideEffect="fade" width="400" draggable="false" resizable="false">
            <f:facet name="header">
                <h4>
                    Entrar
                </h4>
            </f:facet>
            <h:panelGroup id="painelGeral" layout="block" styleClass="modal-body">
                <h:panelGroup layout="block" style="margin: 0 auto;" rendered="#{OamdControle.oamdPacto}"
                              styleClass="avartarEditar user-photo esconderCarregando">
                    <center>
                        <p:graphicImage alt="#{OamdControle.empresaSelecionada.name}"
                                        style="width: 136px; height: 50px;"
                                        value="#{OamdControle.empresaSelecionada.urlLogo}"/>
                    </center>
                </h:panelGroup>
                <!--<h:panelGroup layout="block" styleClass="text-center" rendered="# {empty LoginControle.avatar}">-->
                <!--<p:graphicImage cache="true" styleClass="user-photo" library="imagens" name="default_user.png"/>-->
                <!--</h:panelGroup>-->
                <h4 class="text-center">
                    <h:outputText value="#{OamdControle.empresaSelecionada.visualName}"/>
                </h4>
                <h:panelGroup id="painelLogin" layout="block">
                    <ui:fragment rendered="#{OamdControle.oamdPacto}">
                        <h4>
                            COMO?
                        </h4>
                    </ui:fragment>


                    <h:panelGroup layout="block" styleClass="modal-footer" style="text-align: center;">
                        <h:panelGroup rendered="#{OamdControle.usuario.permiteDataBase and OamdControle.oamdPacto}">
                            <center>
                                <p:calendar id="popupButtonCal1" locale="br" pattern="dd/MM/yyyy"
                                            rendered="#{fn:contains(OamdControle.empresaSelecionada.modulos, 'ZW')}"
                                            value="#{MenuControle.dataSistema}"
                                            style="width: 25%;">
                                </p:calendar>
                                <br/>
                            </center>

                        </h:panelGroup>

                        <p:watermark for="popupButtonCal1" value="Data Sistema"/>
                        <p:commandLink action="#{OamdControle.logar('admin')}" styleClass="btn btn-default"
                                       id="btnEntrarA" ajax="true"
                                       rendered="#{OamdControle.oamdPacto and fn:contains(OamdControle.empresaSelecionada.modulos, 'ZW')}"
                                       value="ADMIN" update=":fmLay:modalogin, :fmLay:modallogado"/>

                        <p:commandLink action="#{OamdControle.logar('PACTOBR')}" styleClass="btn btn-primary"
                                       id="btnEntrar" ajax="true"
                                       rendered="#{OamdControle.oamdPacto}"
                                       value="PACTOBR" update=":fmLay:modalogin, :fmLay:modallogado"/>

                        <p:commandLink action="#{OamdControle.logar}" styleClass="btn btn-primary"
                                       id="btnEntrarHold" ajax="true"
                                       rendered="#{!OamdControle.oamdPacto}"
                                       value="ENTRAR" update=":fmLay:modalogin, :fmLay:modallogado"/>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </p:dialog>

        <p:dialog widgetVar="logadoModal" modal="true" id="modallogado" showEffect="fade" hideEffect="fade"
                  width="550" draggable="false" resizable="false">
            <f:facet name="header">
                <h4>
                    Bem vindo <h:outputText value="#{OamdControle.usuario.userName}" styleClass="text-muted"
                                            rendered="#{not empty OamdControle.usuario.userName}"/>
                </h4>
            </f:facet>
            <h:panelGroup id="painelLogado" layout="block">
                <h:panelGroup layout="block">
                    <h:panelGroup layout="block" style="display: flex; align-items: center;">
                        <h:panelGroup layout="block" style="width: 80%">
                            <h:outputText value="Empresa:"
                                          rendered="#{OamdControle.logado and not empty OamdControle.usuarioZW.empresas}"/>
                            <br/>
                            <p:remoteCommand name="atualizar" update=":fmLay:painelLogado"
                                             action="#{OamdControle.selecionarEmpresaZW}"/>
                            <h:selectOneMenu id="empresa" rendered="#{not empty OamdControle.usuarioZW.empresas}"
                                             value="#{OamdControle.usuarioZW.empresa.codigo}"
                                             onchange="atualizar();">
                                <f:selectItem itemValue="0" itemLabel=""/>
                                <f:selectItems value="#{OamdControle.usuarioZW.empresas}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                        <h:panelGroup layout="block" id="panelSairOAMD" style="width: 20%; text-align: right;">
                            <p:commandLink id="sair" action="#{OamdControle.logout}"
                                           styleClass="linkLogout btn btn-primary"
                                           update=":fmLay:painelGeral"
                                           rendered="#{OamdControle.logado}">
                                <i class="fa-icon-remove"/> Sair
                            </p:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                    <style>
                        li {
                            float: left;
                        }
                    </style>
                    <h:panelGroup layout="block" rendered="#{OamdControle.usuarioZW.empresaBloqueada}">
                        <h:outputLabel style="color: red;"
                                       value="O acesso para a empresa #{OamdControle.usuarioZW.empresa.nome} está liberado apenas para o usuário ADMIN."/>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block"
                              rendered="#{!OamdControle.usuarioZW.empresaBloqueada}"
                              style="display: flex; background-color: #f5f5f5; text-align: center;">

                    <h:panelGroup layout="block" styleClass="panelIconMod"
                                  rendered="#{MenuControle.habilitadoModuloZW and
                                            (OamdControle.usuarioZW.empresa.codigo > 0 or empty OamdControle.usuarioZW.empresas)}">
                            <h:outputLink rendered="#{OamdControle.usuarioZW.userName == 'admin'}" value="#{MenuControle.urlZW_Inicial}" styleClass="iconModuloText">
                                <h:graphicImage id="abrirZillyon" library="imagens" name="genteZW.png"
                                                styleClass="iconModulo"/>
                                Adm
                            </h:outputLink>
                        <h:outputLink rendered="#{OamdControle.usuarioZW.userName != 'admin'}"
                                      value="#{MenuControle.urlNovoZW}" styleClass="iconModuloText">
                                <h:graphicImage id="abrirZillyonZWUI" library="imagens" name="genteZW.png"
                                                styleClass="iconModulo"/>
                                Adm
                            </h:outputLink>
                    </h:panelGroup>
                    <h:panelGroup layout="block" styleClass="panelIconMod"
                                  rendered="#{MenuControle.habilitadoModuloFINAN and
                                            (OamdControle.usuarioZW.empresa.codigo > 0 or empty OamdControle.usuarioZW.empresas)}">
                            <h:outputLink value="#{MenuControle.urlFINAN}" styleClass="iconModuloText">
                                <h:graphicImage id="abrirFinanceiro" library="imagens" name="genteFIN.png"
                                                styleClass="iconModulo"/>
                                Financeiro
                            </h:outputLink>
                    </h:panelGroup>
                    <h:panelGroup layout="block" styleClass="panelIconMod"
                                  rendered="#{MenuControle.habilitadoModuloCRM and
                                            (OamdControle.usuarioZW.empresa.codigo > 0 or empty OamdControle.usuarioZW.empresas)}">
                            <h:outputLink value="#{MenuControle.urlCRM}" styleClass="iconModuloText">
                                <h:graphicImage id="abrirCRM" library="imagens" name="genteCRM.png"
                                                styleClass="iconModulo"/>
                                Crm
                            </h:outputLink>
                    </h:panelGroup>
                    <h:panelGroup layout="block" styleClass="panelIconMod"
                                  rendered="#{(MenuControle.habilitadoModuloPactoPay and
                                            (OamdControle.usuarioZW.empresa.codigo > 0 or empty OamdControle.usuarioZW.empresas)) and OamdControle.usuarioZW.userName != 'admin'}">
                            <h:outputLink value="#{MenuControle.urlPactoPay}" styleClass="iconModuloText">
                                <h:graphicImage id="abrirPactoPay" library="imagens" name="pacto-pay-v1.png"
                                                styleClass="iconModulo"/>
                                PactoPay
                            </h:outputLink>
                    </h:panelGroup>
                    <h:panelGroup layout="block" styleClass="panelIconMod"
                                  rendered="#{(MenuControle.habilitadoSistemaPacto and
                                            (OamdControle.usuarioZW.empresa.codigo > 0 or empty OamdControle.usuarioZW.empresas)) and OamdControle.usuarioZW.userName != 'admin'}">
                            <h:outputLink value="#{MenuControle.getUrlSistemaPacto()}" styleClass="iconModuloText">
                                <h:graphicImage id="abrirSistemaPacto" library="imagens" name="genteTR.png"
                                                styleClass="iconModulo"/>
                                Treino
                            </h:outputLink>
                    </h:panelGroup>
                    <h:panelGroup layout="block" styleClass="panelIconMod"
                                  rendered="#{(MenuControle.habilitadoModuloAulaCheia and
                                            (OamdControle.usuarioZW.empresa.codigo > 0 or empty OamdControle.usuarioZW.empresas)) and OamdControle.usuarioZW.userName != 'admin' }">
                            <h:outputLink value="#{MenuControle.urlAulaCheia}" styleClass="iconModuloText">
                                <h:graphicImage id="abrirAulaCheia" library="imagens" name="genteAC.png"
                                                styleClass="iconModulo"/>
                                Aula Cheia
                            </h:outputLink>
                    </h:panelGroup>
                    <h:panelGroup layout="block" styleClass="panelIconMod"
                                  rendered="#{(MenuControle.habilitadoModuloGestaoNotas and
                                            (OamdControle.usuarioZW.empresa.codigo > 0 or empty OamdControle.usuarioZW.empresas))}">
                            <h:outputLink value="#{MenuControle.urlNotaFiscal}"
                                          target="_blank" styleClass="iconModuloText">
                                <h:graphicImage id="abrirGestaoNota" library="imagens" name="genteNOTAS.png"
                                                styleClass="iconModulo"/>
                                Notas
                            </h:outputLink>
                    </h:panelGroup>
                    <h:panelGroup layout="block" styleClass="panelIconMod"
                                  rendered="#{((MenuControle.habilitadoModuloTreino and
                                            (OamdControle.usuarioZW.empresa.codigo > 0 or empty OamdControle.usuarioZW.empresas))
                                            or OamdControle.usuarioZW.somenteTreino) and OamdControle.usuarioZW.userName != 'admin'}">
                            <h:outputLink value="#{MenuControle.urlTreino}" style="text-align: center;">
                                <h:graphicImage id="abrirTreino" library="imagens" name="smileTreino.png"
                                                style="display: block;"/>
                                Treino Antigo
                            </h:outputLink>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </p:dialog>

        <p:dialog widgetVar="modalDialogTreino" modal="true" id="modalTreino" showEffect="fade" hideEffect="fade" resizable="true" width="1200">
            <f:facet name="header">
                <h:outputText value="Unidades"/>
            </f:facet>

            <p:tabView id="tabFuncoesTreino" style="max-height: 650px; overflow-y: auto">
                <p:tab title="Detalhes">
                    <h:panelGroup layout="block" id="detailsTreino">
                        <h:outputText value="#{OamdControle.empresaSelecionada.name}"/>
                        <p:dataTable value="#{OamdControle.empresaSelecionada.empresas}" var="empresaJSON">
                            <p:column headerText="Código" style="text-align: center">
                                <h:outputText value="#{empresaJSON.codigo}"/>
                            </p:column>
                            <p:column headerText="Empresa">
                                <h:outputText value="#{empresaJSON.nome}"/>
                            </p:column>
                            <p:column headerText="Expiração" style="text-align: center">
                                <h:outputText value="#{empresaJSON.dataexpiracao}">
                                    <f:convertDateTime pattern="dd/MM/yyyy" locale="#{OamdControle.localeDefault}"/>
                                </h:outputText>
                            </p:column>
                            <p:column headerText="Suspensa?" style="text-align: center; font-weight: bolder">
                                <h:outputText value="SIM" rendered="#{empresaJSON.suspensa}" style="color: red;" />
                                <h:outputText value="NÃO" rendered="#{!empresaJSON.suspensa}" />
                            </p:column>
                            <p:column headerText="Data Suspensão" style="text-align: center">
                                <h:outputText value="#{empresaJSON.datasuspensao}">
                                    <f:convertDateTime pattern="dd/MM/yyyy" locale="#{OamdControle.localeDefault}"/>
                                </h:outputText>
                            </p:column>
                            <p:column headerText="Bloquear" style="text-align: center; padding: 2px;"
                                      rendered="#{OamdControle.oamdPacto and OamdControle.usuario.adm}">
                                <p:menuButton value="Bloquear" rendered="#{!empresaJSON.suspensa}">
                                    <p:menuitem value="Agora" action="#{OamdControle.bloquear(0, empresaJSON, true)}"
                                                update=":fmLay:tabFuncoesTreino:detailsTreino"/>
                                    <p:menuitem value="Em 2 dias" action="#{OamdControle.bloquear(2, empresaJSON, true)}"
                                                update=":fmLay:tabFuncoesTreino:detailsTreino"/>
                                    <p:menuitem value="Em 5 dias" action="#{OamdControle.bloquear(5, empresaJSON, true)}"
                                                update=":fmLay:tabFuncoesTreino:detailsTreino"/>
                                </p:menuButton>
                            </p:column>

                            <p:column headerText="Ação"
                                      style="max-width: 100px; padding: 3px;"
                                      rendered="#{OamdControle.oamdPacto and OamdControle.usuario.adm}">
                                <p:commandLink value="Desbloquear"
                                               title="Esta ação desbloqueia e também remove suspensão definitiva permitindo que o cliente volte a acessar o sistema."
                                               rendered="#{empresaJSON.dataexpiracao != null}"
                                               styleClass="btn btn-sm btn-primary"
                                               style="padding: 1px; width: 100px; margin-left: 18px;"
                                               action="#{OamdControle.desbloquear(empresaJSON, true)}"
                                               update=":fmLay:tabFuncoesTreino:detailsTreino"/>
                            </p:column>
                        </p:dataTable>
                    </h:panelGroup>
                </p:tab>

                <p:tab title="Log Bloqueio" rendered="#{OamdControle.oamdPacto and OamdControle.usuario.adm}">
                    <h:panelGroup layout="block" id="logBloqueiosTreino">
                        <p:dataTable value="#{OamdControle.empresaSelecionada.logBloqueios}" var="logBloqueio"
                                     rows="10"
                                     paginator="true"
                                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                     rowsPerPageTemplate="5,10,15">
                            <p:column headerText="Data/Hora" style="text-align: center">
                                <h:outputText value="#{logBloqueio.dataAlteracao}"/>
                            </p:column>

                            <p:column headerText="Empresa" style="text-align: center">
                                <h:outputText value="#{logBloqueio.obterNome(OamdControle.empresaSelecionada)}"/>
                            </p:column>

                            <p:column headerText="Ação" style="text-align: center">
                                <h:outputText value="#{logBloqueio.acao}"/>
                            </p:column>

                            <p:column headerText="Responsável" style="text-align: center">
                                <h:outputText value="#{logBloqueio.responsavel}"/>
                            </p:column>

                            <p:column headerText="Observação" style="text-align: center">
                                <h:outputText value="#{logBloqueio.observacao}"/>
                            </p:column>

                        </p:dataTable>
                    </h:panelGroup>
                </p:tab>

                <p:tab title="Módulos" rendered="#{OamdControle.apresentarAbaModulos}">
                    <h:panelGroup layout="block" id="panelModulosTreino">
                        <h5 style="margin-left: 5px">Módulos</h5>
                        <p:selectManyCheckbox value="#{OamdControle.empresaSelecionada.modulosEmpresaTreino}" layout="grid" columns="4" style="margin: 5px !important;">
                            <f:selectItems value="#{OamdControle.modEmpresas}" var="modulo" itemLabel="#{modulo}" itemValue="#{modulo}"/>
                        </p:selectManyCheckbox>

                        <p:commandButton value="Processar" style="margin-top: 5px" update="panelModulosTreino"
                                         action="#{OamdControle.toggleHabilitaModulos(true)}"/>
                    </h:panelGroup>
                </p:tab>
            </p:tabView>
            <p:messages id="messagesModalTreino" showSummary="true" showDetail="false" autoUpdate="true" closable="true"/>
        </p:dialog>

        <p:dialog widgetVar="modalBloqueio" modal="true" id="modalBloqueio" showEffect="fade" hideEffect="fade" resizable="true" width="1200">
            <f:facet name="header">
                <h:outputText value="Bloqueio"/>
            </f:facet>
            <p:tabView id="tabBloqueio" style="max-height: 650px; overflow-y: auto">
                <p:tab title="Bloqueio Personalizado">
                    <h:panelGroup layout="block" id="bloqueioPersonalizado">
                        <h:outputText value="#{OamdControle.empresaSelecionada.name}"/>
                        <p:dataTable value="#{OamdControle.empresaSelecionada.empresas}" var="empresaJSON">
                            <p:column headerText="Código" style="text-align: center">
                                <h:outputText value="#{empresaJSON.codigo}"/>
                            </p:column>
                            <p:column headerText="Empresa" style="text-align: center">
                                <h:outputText value="#{empresaJSON.nome}"/>
                            </p:column>
                            <p:column headerText="Data Personalizada" style="text-align: center">
                                <h:form style="margin-top: 15px;">
                                    <p:outputLabel for="popupCalendario" value="Escolha uma data: "/>
                                    <p:calendar id="popupCalendario" value="#{OamdControle.dataEscolhida}"
                                                timeZone="America/Sao_Paulo"
                                                pattern="dd/MM/yyyy"
                                                mask="true"
                                                locale="pt" >

                                        <p:ajax event="dateSelect" listener="#{OamdControle.onDateSelect}"/>
                                    </p:calendar>
                                </h:form>
                            </p:column>
                            <p:column headerText="Ação" style="max-width: 100px; padding: 3px;">
                                <p:commandLink value="Bloquear"
                                               title="Esse botão vai bloquear a empresa com a data escolhida no calendario"
                                               action="#{OamdControle.diferencaDataPersonalizada(empresaJSON)}"
                                               styleClass="btn btn-sm btn-danger"
                                               style="padding: 1px; width: 100px; margin-left: 50px;"
                                               update=":fmLay:tabFuncoes:details"
                                               oncomplete="modalBloqueio.hide()"/>
                            </p:column>
                        </p:dataTable>
                    </h:panelGroup>
                </p:tab>
            </p:tabView>
        </p:dialog>

    </ui:define>

    <ui:define name="JS">
        <h:outputScript library="js" name="cadastrosV2.js"/>
        <h:outputScript target="head" library="js" name="srollinfinito.js"/>
        <script>
            PrimeFaces.widget.Dialog.prototype.applyFocus = function () {
            };

        </script>
    </ui:define>
</ui:decorate>
</html>
