<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="conteudo">
        <style>
            @media (max-width: 1400px) {
                .container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
                    width: 100% !important;
                }
            }

        </style>


        <div class="container-fluid noSlider" style="min-height: 800px;">
            <div class="bread span12"></div>

            <h:panelGroup layout="block" styleClass="span3 menuLateral" id="addEdit"
                          pt:data-spy="affix" pt:data-offset-top="0"
                          style="top: 0; position: absolute; overflow-y: auto;">
                <h4>Cupom de Desconto</h4>
                <h:panelGroup id="groupAddEdit">
                    <h:panelGroup layout="block" class="internoMenuLateral" style="padding-top: 15px;">


                        <h:panelGroup id="panelCupomDesconto" layout="block"
                                      style="margin-top: 10px; margin-left: 19px;">
                            <p:outputLabel value="Cupom de desconto"
                                           style="font-weight: bold"/>
                            <h:inputText disabled="#{ProdutoPactoCupomDescontoControle.cupomDesconto.id > 0}"
                                         style="text-transform: uppercase"
                                         value="#{ProdutoPactoCupomDescontoControle.cupomDesconto.cupom}"
                                         pt:placeholder="Cupom de Desconto"/>
                        </h:panelGroup>

                        <h:panelGroup id="panelTipoProduto" layout="block"
                                      style="margin-top: 10px; margin-left: 19px;">
                            <p:outputLabel value="Tipo produto"
                                           style="font-weight: bold"/>
                            <h:selectOneMenu
                                    value="#{ProdutoPactoCupomDescontoControle.cupomDesconto.tipoProdutoPacto}">
                                <f:selectItems value="#{ProdutoPactoCupomDescontoControle.listaTipoProduto}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:panelGroup id="panelQuantidade" layout="block"
                                      style="margin-top: 10px; margin-left: 19px;">
                            <p:outputLabel value="Quantidade"
                                           style="font-weight: bold"/>
                            <p:inputText pt:placeholder="Qtd disponível"
                                         maxlength="4"
                                         onkeypress="mascara(this, somenteNumeros)"
                                         value="#{ProdutoPactoCupomDescontoControle.cupomDesconto.qtd}"/>
                        </h:panelGroup>

                        <h:panelGroup layout="block" id="panelDescontoAdesao"
                                      style="margin-top: 19px; border-top: 1px solid; border-left: 19px !important; padding: 19px 19px 0px 19px;">
                            <p:outputLabel value="Desconto adesão"
                                           style="font-weight: bold"/>
                            <h:selectOneMenu
                                    value="#{ProdutoPactoCupomDescontoControle.cupomDesconto.tipoDescontoAdesao}">
                                <f:selectItems value="#{ProdutoPactoCupomDescontoControle.listaTipoDesconto}"/>
                                <p:ajax event="change" update=":fmLay:panelDescontoAdesao"/>
                            </h:selectOneMenu>

                            <p:outputLabel
                                    value="#{ProdutoPactoCupomDescontoControle.cupomDesconto.tipoDescontoAdesao.descricaoApresentar} de desconto"
                                    style="font-weight: bold"/>
                            <p:inputText pt:placeholder="Desconto na adesão"
                                         maxlength="18"
                                         onkeypress="mascara(this, moeda)"
                                         value="#{ProdutoPactoCupomDescontoControle.cupomDesconto.descontoAdesao}"/>

                        </h:panelGroup>

                        <h:panelGroup layout="block" id="panelDescontoMensalidade"
                                      style="margin-top: 19px; border-top: 1px solid; border-bottom: 1px solid; border-left: 19px !important; padding: 19px;">

                            <p:outputLabel value="Desconto mensalidade"
                                           style="font-weight: bold"/>
                            <h:selectOneMenu
                                    value="#{ProdutoPactoCupomDescontoControle.cupomDesconto.tipoDescontoMensalidade}"
                                    style="">
                                <f:selectItems value="#{ProdutoPactoCupomDescontoControle.listaTipoDesconto}"/>
                                <p:ajax event="change" update=":fmLay:panelDescontoMensalidade"/>
                            </h:selectOneMenu>

                            <p:outputLabel
                                    value="#{ProdutoPactoCupomDescontoControle.cupomDesconto.tipoDescontoMensalidade.descricaoApresentar} de desconto"
                                    style="margin-top: 5px; font-weight: bold"/>
                            <p:inputText pt:placeholder="Desconto na mensalidade"
                                         maxlength="18"
                                         onkeypress="mascara(this, moeda)"
                                         value="#{ProdutoPactoCupomDescontoControle.cupomDesconto.descontoMensalidade}"/>

                        </h:panelGroup>

                        <h:panelGroup id="panelAtivo" layout="block"
                                      style="margin-top: 10px; display: inline-flex; margin-left: 19px;">
                            <p:selectBooleanCheckbox value="#{ProdutoPactoCupomDescontoControle.cupomDesconto.ativo}"
                                                     styleClass="comBorda"/>
                            <p:outputLabel value="Ativo"
                                           style="font-weight: bold"/>
                        </h:panelGroup>

                    </h:panelGroup>

                    <h:panelGroup id="pnlBotoes" layout="block" style="margin-bottom: 6px;" styleClass="pull-right">
                        <p:commandLink styleClass="btn btn-primary"
                                       action="#{ProdutoPactoCupomDescontoControle.novoCupom}"
                                       update=":fmLay:groupAddEdit :fmLay:dados"
                                       ajax="true">
                            <i class="fa-icon-plus-sign-alt"/> Novo
                        </p:commandLink>
                        <p:commandLink styleClass="btn btn-secundary"
                                       rendered="#{ProdutoPactoCupomDescontoControle.cupomDesconto.id > 0}"
                                       action="#{ProdutoPactoCupomDescontoControle.excluir}"
                                       update=":fmLay:groupAddEdit :fmLay:dados"
                                       ajax="true">
                            <i class="fa-icon-remove"/> Excluir
                        </p:commandLink>
                        <p:commandLink styleClass="btn btn-primary" action="#{ProdutoPactoCupomDescontoControle.gravar}"
                                       update=":fmLay:groupAddEdit :fmLay:dados"
                                       ajax="true">
                            <i class="fa-icon-save"/> #{title['cadastros.salvar']}
                        </p:commandLink>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup styleClass="span10 offset3 caixaPrincipal" id="dados">
                <h4>Cupons cadastrados</h4>
                <hr/>
                <p:outputPanel layout="block" styleClass="pesq pesq-tabelas buscaAuxiliar" id="filtrar">
                    <p:inputText id="globalFilter" value="#{ProdutoPactoCupomDescontoControle.filtro}">
                        <f:ajax event="keyup"
                                listener="#{ProdutoPactoCupomDescontoControle.filtrar(ProdutoPactoCupomDescontoControle.cuponsCadastrados)}"
                                delay="#{ProdutoPactoCupomDescontoControle.tempoDelay}"
                                render=":fmLay:lista"
                        />
                    </p:inputText>

                    <p:watermark for="globalFilter" value="#{title['cadastros.filtrar']}"/>
                </p:outputPanel>

                <p:dataTable var="cupom" value="#{ProdutoPactoCupomDescontoControle.cuponsCadastrados}" id="lista"
                             widgetVar="tabelaAtual"
                             emptyMessage="#{msg['tabela.semregistros']}"
                             styleClass="tabelaPrincipal">

                    <f:facet name="header">
                        <h:panelGroup layout="block" id="totalTabela" styleClass="totalizaTabela">
                            <h:outputText value="#{fn:length(ProdutoPactoCupomDescontoControle.listaTela)} cupons"/>
                        </h:panelGroup>
                    </f:facet>

                    <p:column headerText="Cupom Desconto" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoCupomDescontoControle.editar(cupom)}"
                                       update=":fmLay:groupAddEdit :fmLay:pnlBotoes">#{cupom.cupom}</p:commandLink>
                    </p:column>

                    <p:column headerText="Desc Adesão" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoCupomDescontoControle.editar(cupom)}"
                                       update=":fmLay:groupAddEdit :fmLay:pnlBotoes">#{cupom.descontoAdesaoApresentar}</p:commandLink>
                    </p:column>

                    <p:column headerText="Desc Mensalidade" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoCupomDescontoControle.editar(cupom)}"
                                       update=":fmLay:groupAddEdit :fmLay:pnlBotoes">#{cupom.descontoMensalidadeApresentar}</p:commandLink>
                    </p:column>

                    <p:column headerText="Utilizado" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoCupomDescontoControle.editar(cupom)}"
                                       update=":fmLay:groupAddEdit :fmLay:pnlBotoes">#{cupom.qtdUtilizada}</p:commandLink>
                    </p:column>

                    <p:column headerText="Disponível" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoCupomDescontoControle.editar(cupom)}"
                                       update=":fmLay:groupAddEdit :fmLay:pnlBotoes">#{cupom.qtdDisponivel}</p:commandLink>
                    </p:column>

                    <p:column headerText="Ativo" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoCupomDescontoControle.editar(cupom)}"
                                       update=":fmLay:groupAddEdit :fmLay:pnlBotoes">#{cupom.ativoApresentar}</p:commandLink>
                    </p:column>

                    <p:column headerText="Usuário" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoCupomDescontoControle.editar(cupom)}"
                                       update=":fmLay:groupAddEdit :fmLay:pnlBotoes">#{cupom.usuarioGerou}</p:commandLink>
                    </p:column>

                    <p:column headerText="Dt Criação" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoCupomDescontoControle.editar(cupom)}"
                                       update=":fmLay:groupAddEdit :fmLay:pnlBotoes">#{cupom.dataRegistroApresentar}</p:commandLink>
                    </p:column>
                </p:dataTable>
            </h:panelGroup>

        </div>

    </ui:define>
    <ui:define name="JS">
        <h:outputScript library="js" name="cadastrosV2.js"/>
    </ui:define>
</ui:decorate>
</html>
