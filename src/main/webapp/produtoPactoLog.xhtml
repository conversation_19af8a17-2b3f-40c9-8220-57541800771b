<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="conteudo">
        <style>
            @media (max-width: 1400px) {
                .container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
                    width: 100% !important;
                }
            }

            .divLog {
                overflow: hidden;
                margin: 0;
                max-height: 100px;
            }
        </style>

        <div class="container-fluid noSlider" style="padding: 0px;">
            <div class="bread span12"></div>

            <h:panelGroup styleClass="span10 caixaPrincipal" id="dados"
                          style="width: 100%; padding: 0px; margin: 0px;">
                <h4>Logs</h4>
                <hr/>
                <p:outputPanel layout="block" styleClass="pesq pesq-tabelas buscaAuxiliar" id="filtrar">
                    <p:inputText id="globalFilter" value="#{ProdutoPactoLogControle.filtro}">
                        <f:ajax event="keyup"
                                listener="#{ProdutoPactoLogControle.filtrar(ProdutoPactoLogControle.listaLog)}"
                                delay="#{ProdutoPactoLogControle.tempoDelay}"
                                render=":fmLay:lista"
                        />
                    </p:inputText>

                    <p:watermark for="globalFilter" value="#{title['cadastros.filtrar']}"/>
                </p:outputPanel>

                <p:dataTable var="log" value="#{ProdutoPactoLogControle.listaLog}" id="lista"
                             widgetVar="tabelaAtual"
                             emptyMessage="#{msg['tabela.semregistros']}"
                             style="margin: 0"
                             styleClass="tabelaPrincipal">

                    <f:facet name="header">
                        <h:panelGroup layout="block" id="totalTabela" styleClass="totalizaTabela">
                            <h:outputText value="#{fn:length(ProdutoPactoLogControle.listaTela)} logs"/>
                        </h:panelGroup>
                    </f:facet>

                    <p:column headerText="ID" style="text-align: center">
                        <h:outputText value="#{log.id}"/>
                    </p:column>

                    <p:column headerText="Chave" style="text-align: center">
                        <h:outputText value="#{log.chave}"/>
                    </p:column>

                    <p:column headerText="Empresa Financeiro" style="text-align: center">
                        <h:outputText value="#{log.codigoFinanceiro}"/>
                    </p:column>

                    <p:column headerText="Data" style="text-align: center">
                        <h:outputText value="#{log.dataRegistroApresentar}"/>
                    </p:column>

                    <p:column headerText="Nível Urgência" style="text-align: center">
                        <h:outputText value="#{log.urgencia}"/>
                    </p:column>

                    <p:column headerText="Operação" style="text-align: center">
                        <h:outputText value="#{log.operacao}"/>
                    </p:column>

                    <p:column headerText="Log" style="text-align: center">
                        <div class="divLog">
                            <h:outputText value="#{log.log}"/>
                        </div>
                    </p:column>
                </p:dataTable>
            </h:panelGroup>
        </div>

    </ui:define>
    <ui:define name="JS">
        <h:outputScript library="js" name="cadastrosV2.js"/>
    </ui:define>
</ui:decorate>
</html>
