<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="conteudo">
        <style>
            @media (max-width: 1400px) {
                .container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
                    width: 100% !important;
                }
            }

            @media (min-width: 1200px) {
                .container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
                    width: 95%;
                }
            }
        </style>

        <h:panelGroup layout="block" style="padding: 0;" id="divGeral">
            <div class="bread span12"></div>

            <h:panelGroup styleClass="span10 caixaPrincipal" id="dados"
                          layout="block"
                          style="width: 100%; padding: 0px; margin: 0px;">
                <h4>Recurso - Data desativação</h4>

                <h:panelGroup layout="block" style="display: grid; grid-template-columns: 3fr 1fr">

                    <h:panelGroup layout="block">
                        <p:selectManyCheckbox id="gridRecursoPadrao2222"
                                              value="#{MigracaoRecursoControle.zonasSelecionadas}"
                                              layout="grid" columns="4" style="margin: 5px !important;">
                            <f:selectItems value="#{MigracaoRecursoControle.selectItemZonas}" var="modulo"
                                           itemLabel="#{modulo.label}"
                                           itemValue="#{modulo.value}"/>
                        </p:selectManyCheckbox>
                    </h:panelGroup>

                    <h:panelGroup layout="block" style="display: block">

                        <h:panelGroup layout="block" style="display: grid; gap: 5px; align-items: center;">
                            <h:outputText value="Recurso:"/>
                            <p:selectOneMenu value="#{MigracaoRecursoControle.tipoInfoMigracao}">
                                <f:selectItem itemLabel="Selecione " itemValue=""/>
                                <f:selectItems value="#{MigracaoRecursoControle.selectItemTipoInfoMigracaoEnum}" />
                            </p:selectOneMenu>
                        </h:panelGroup>

                        <h:panelGroup layout="block" style="display: grid; gap: 5px; padding-top: 20px; align-items: center;">
                            <h:outputText value="Data desativação:"/>
                            <p:calendar id="dataMigracao" locale="br" pattern="dd/MM/yyyy"
                                        value="#{MigracaoRecursoControle.dataMigracao}">
                            </p:calendar>
                        </h:panelGroup>

                        <h:panelGroup layout="block" style="display: flex; gap: 20px; padding-top: 20px;">

                            <h:panelGroup layout="block" id="divLimpar">
                                <p:commandLink action="#{MigracaoRecursoControle.novoMigracao}"
                                               styleClass="btn btn-secundary"
                                               update=":fmLay:dados"
                                               ajax="true">
                                    Novo
                                </p:commandLink>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="divGravarAutomatico">
                                <p:commandLink action="#{MigracaoRecursoControle.gravarMigracao}"
                                               styleClass="btn btn-primary"
                                               update=":fmLay:divGeral"
                                               ajax="true">
                                    Gravar
                                </p:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup layout="block" style="display: flex; gap: 20px; padding-top: 20px; align-items: center;">
                            <h:panelGroup layout="block" id="totalFeedback">
                                <h:outputText value="#{MigracaoRecursoControle.totalFeedback} feedbacks"/>
                            </h:panelGroup>
                            <h:panelGroup layout="block" id="btnFeedback">
                                <p:commandLink action="pretty:migracaoRecursoFeedback"
                                               styleClass="btn btn-secundary"
                                               ajax="true">
                                    Acessar feedback
                                </p:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup styleClass="span10 caixaPrincipal" id="dados3"
                          layout="block"
                          style="width: 100%; padding: 0px; margin: 0px; margin-top: 20px">
                <h4>Configurações ativas</h4>

                <p:dataTable var="item" value="#{MigracaoRecursoControle.lista}" id="listaLog"
                             widgetVar="tabelaAtual3"
                             emptyMessage="#{msg['tabela.semregistros']}"
                             style="margin: 0"
                             paginator="true" rowsPerPageTemplate="5,10,20,50,100,200" rows="10" paginatorPosition="bottom"
                             styleClass="tabelaPrincipal">
                    <p:column headerText="ID" style="text-align: center">
                        <h:outputText value="#{item.id}"/>
                    </p:column>

                    <p:column headerText="DT. REGISTRO" style="text-align: center">
                        <h:outputText value="#{item.dadosDTO.dataRegistroApresentar}"/>
                    </p:column>

                    <p:column headerText="USUARIO" style="text-align: center">
                        <h:outputText value="#{item.dadosDTO.usuarioOAMDUsername}"/>
                    </p:column>

                    <p:column headerText="RECURSO" style="text-align: center">
                        <h:outputText value="#{item.tipoInfoMigracao}"/>
                    </p:column>

                    <p:column headerText="ZONAS" style="text-align: center">
                        <h:outputText value="#{item.zonasLista}"/>
                    </p:column>

                    <p:column headerText="DT. DESATIVAÇÃO" style="text-align: center">
                        <h:outputText value="#{item.dataMigracao}">
                            <f:convertDateTime pattern="dd/MM/yyyy" />
                        </h:outputText>
                    </p:column>

                    <p:column headerText="" style="text-align: center">
                        <p:commandLink ajax="true" update=":fmLay:divGeral"
                                       action="#{MigracaoRecursoControle.desativar(item)}">
                            Excluir
                        </p:commandLink>
                    </p:column>
                </p:dataTable>
            </h:panelGroup>
        </h:panelGroup>
    </ui:define>
    <ui:define name="JS">
        <h:outputScript library="js" name="cadastrosV2.js"/>
    </ui:define>
</ui:decorate>
</html>
