<!DOCTYPE html>
<html lang="br"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:h="http://java.sun.com/jsf/html">

<style>
    .nome {
        padding: 30px;
        text-align: center;
        font-size: 30px;
        font-weight: bold;
    }

    .termos {
        padding: 0 10% 5% 10%;
    }
</style>

<h:head>
    <title>Termos e Condições</title>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/>
</h:head>

<h:body>
    <h:panelGroup layout="block" id="nome" styleClass="nome">
        <h:outputText value="#{ProdutoPactoTermosControle.produtoPacto.nome}"/>
    </h:panelGroup>

    <h:panelGroup layout="block" id="termos" styleClass="termos">
        <h:outputText value="#{ProdutoPactoTermosControle.produtoPacto.termosCondicoes}"
                      escape="false"/>
    </h:panelGroup>
</h:body>
</html>
