<!DOCTYPE html>
<html lang="br"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <h:head>
        <title>OAMD</title>
        <link class="component" href="css/oamd.css" rel="stylesheet" type="text/css"/>
        <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet"/>
        <script src="bootstrap/js/jquery.js"></script>
        <script src="bootstrap/js/bootstrap.min.js"></script>
        <script src="bootstrap/js/bootstrap-typeahead.js"></script>
        <link href="bootstrap/css/bootstrap-responsive.min.css" rel="stylesheet"/>

        <link rel="apple-touch-icon-precomposed" sizes="144x144" href="bootstrap/ico/apple-touch-icon-144-precomposed.png"/>
        <link rel="apple-touch-icon-precomposed" sizes="114x114" href="bootstrap/ico/apple-touch-icon-114-precomposed.png"/>
        <link rel="apple-touch-icon-precomposed" sizes="72x72" href="bootstrap/ico/apple-touch-icon-72-precomposed.png"/>
        <link rel="apple-touch-icon-precomposed" href="bootstrap/ico/apple-touch-icon-57-precomposed.png"/>
        <link rel="shortcut icon" href="bootstrap/ico/favicon.png"/>

        <h:outputStylesheet library="css" name="font-awesome.min.css"/>
        <h:outputStylesheet library="css" name="chosen.css"/>
        <h:outputStylesheet library="css" name="custom1302.css"/>
        <h:outputStylesheet library="css" name="bootstrap.min.css"/>
        <h:outputStylesheet library="css" name="addAluno.css"/>
        <h:outputStylesheet library="css" name="cadastros_v1.css"/>
        <h:outputScript library="js" name="bootstrap.min.js"/>
        <h:outputScript library="js" name="w.js"/>

        <style>

            .alinhar{
                vertical-align: top !important;
            }
            .alinharCentro{
                vertical-align: middle !important;
            }
            tr{
                height: 35px !important;
            }
            .btnCheckIn{
                width:300px;
                height:100px;
                background-color:#D6D6D6;
                border-radius: 10px;
                border-width: 7px !important; 
                color: black !important;
                text-align: center;
                border-style: solid;
            }
            .btnCheckIninterno{
                top: 50%; 
                display: table-cell; 
                vertical-align: middle;
                text-align: center; 
                width: 300px;
            }
            a.linkCheckin:hover{
                text-decoration:none!important;
            }
            .coluna20 {
                position:relative;
                width: 20%;
                float: left;
            }
            .coluna80 {
                position: relative;
                width: 80%;
                float: left;
                vertical-align: top;
            }
            .coluna65 {
                position: relative;
                width: 65%;
                text-align: center; 
                float: left;
                vertical-align: top;
            }

            .coluna35 {
                position: relative;
                width: 35%;
                float: left;
                vertical-align: top;
            }


            .coluna50{
                width: 50%;
            }
            .ui-autocomplete-panel{
                z-index: 99999 !important;
            }

            .resumoPrograma .nomesEvento img{
                width: 60px !important;
                height: 60px !important;
            }
            .notificacaoAtividades{
                -webkit-background-clip: padding-box;
                -webkit-box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
                -webkit-font-smoothing: subpixel-antialiased;
                background-clip: padding-box;
                background-color: rgb(220, 13, 23) !important;
                background-image: -webkit-linear-gradient(top, rgb(250, 60, 69), rgb(220, 13, 23));
                border-bottom-left-radius: 50px;
                border-bottom-right-radius: 50px;
                border-top-left-radius: 50px;
                border-top-right-radius: 50px;
                box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
                color: rgb(255, 255, 255) !important;
                cursor: pointer;
                font-family: 'Helvetica Neue', Helvetica, sans-serif;
                font-size: 10px !important;
                height: 13px !important;
                line-height: normal;
                list-style-type: none;
                padding-bottom: 4px !important;
                padding-left: 10px !important;
                padding-right: 10px !important;
                padding-top: 7px !important;
                text-align: center;
                text-shadow: rgba(0, 0, 0, 0.4) 0px -1px 0px;
                zoom: 1;
            }
            .btAddGreen{
                background-image: -webkit-linear-gradient(top, rgb(1, 145, 19), rgb(1, 145, 19)) !important;
            }  
            body {
                padding: 0;
                margin-bottom: -20px;
                font-family: 'Oxygen', sans-serif;
                position: relative;
                font-size: 14px;
                width: 100%;

                /**/
                background: url(#{resource["imagens/background_site.jpg"]}) no-repeat center center fixed; 
                background-color:rgba(240,237,237,0.9);/*#F0EDED*/    
                -webkit-background-size: cover;
                -moz-background-size: cover;
                -o-background-size: cover;
                background-size: cover;
            }
            .navbar-inverse .navbar-inner{
                border-radius: 0;
                background:#f6eeee;
                border:none;
            }
            @media screen and (max-height: 900px) {
                body {
                    background-position: left 58%;
                    overflow-y: auto;
                    bottom: -100px;
                }
                .passoGrande {
                    height: 50%;
                }
                .passoB {
                    top: 25%;
                }
            }

            @media screen and (orientation: landscape) and (max-height: 600px) {
                body {
                    background-position: left 69%;
                    overflow-y: auto;
                    bottom: -200px;
                }
            }

        </style>

        <ui:insert name="CSS">

        </ui:insert>

    </h:head>
    <h:body>
        <h:form prependId="true" id="fmLay">
            <p:hotkey bind="esc" handler="$('.ui-dialog').hide(); $('.ui-widget-overlay').hide(); $('.ui-growl').hide();"  />

            <h:panelGroup layout="block" id="conteudoPag" styleClass="container">
                <p:dialog modal="true" id="panelCarregando"                                                                  
                          styleClass="panelCarregando"                      
                          showHeader="false"
                          closeOnEscape="false"
                          widgetVar="carregando"
                          draggable="false"
                          closable="false"
                          maximizable="false"
                          minimizable="false"
                          resizable="false"
                          width="210"
                          minWidth="220">
                    <i class="fa-icon-spin fa-icon-refresh fa-icon-2x" style="vertical-align: middle;" />
                    <h:outputText style="font-weight: bold; vertical-align: middle; margin-left: 10px; font-size: 14px;" value="Por favor, aguarde..."/>
                </p:dialog>

                <p:ajaxStatus style="width:64px;height:64px;position:fixed;right:5px;bottom:5px"                          
                              onstart="carregando.show();"
                              oncomplete="carregando.hide();"
                              onerror="carregando.hide();"/>

                <ui:insert name="JS">

                </ui:insert>
                <ui:insert name="conteudo">
                    <a class="brand" href="#">
                        <p:graphicImage  cache="true" library="imagens" name="pacto_home.png" style="height:50px;"/>
                    </a>
                    <h4>Cadastrar Empresa</h4>
                    <hr />
                    <div class="navbar-inner">
                        <div class="container-fluid">
                            <h:panelGroup layout="block" styleClass="container-fluid noSlider" style="position: relative;"
                                          id="geral">

                                <h:panelGroup layout="block" id="contemUpdate" style="margin: 50px 50px 50px 50px">

                                    <h:panelGroup styleClass="coluna20">
                                        <h:inputText value="#{EmpresaSiteControle.empresa.nomeFantasia}" pt:data-placeholder="Nome Fantasia"/>
                                    </h:panelGroup>


                                </h:panelGroup>
                                <br/>
                                <p:commandLink styleClass="btn btn-primary margin-20" 
                                               style="margin-left: 20px !important;"
                                               action="#{EmpresaSiteControle.cadastrar()}"
                                               ajax="true" update="contemUpdate">
                                    <i class="fa-icon-save"/> #{title['cadastros.salvar']}
                                </p:commandLink>
                            </h:panelGroup>
                        </div>
                    </div>
                </ui:insert>
            </h:panelGroup>




            <h:outputScript target="head" library="primefaces" name="jquery/jquery.js"/>
            <!-- -->
            <h:outputScript target="head" library="js" name="bootstrap.min.js"/>
            <h:outputScript target="head" library="js" name="bootstrap-affix.js"/>
            <h:outputScript target="head" library="js" name="chosen.proto.js"/>
            <h:outputScript target="head" library="js" name="jquery.cookie.js"/>
            <h:outputScript target="head" library="js" name="time.js"/>
            <h:outputScript target="head" library="js" name="allPaginas.js"/>


            <p:growl id="growl" life="3000" autoUpdate="true" globalOnly="true" sticky="true" />
            <p:growl id="messages" life="2000" autoUpdate="true" sticky="false" severity="info" />
        </h:form>

    </h:body>
</html>
