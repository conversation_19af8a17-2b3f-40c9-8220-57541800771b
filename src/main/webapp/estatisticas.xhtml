<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
    <ui:decorate template="/template/layout.xhtml">
        <ui:define name="CSS">
            <style>
                .ui-autocomplete-token-icon.ui-icon.ui-icon-close {
                    background-image: url("imagens/icons.png");
                    margin-right: 10px;
                }
                .ui-autocomplete-token-label{
                    background-color: #363636;
                    background-image: none;
                    color: white;
                    text-transform: lowercase;
                    box-shadow: none;
                    border: none;
                    padding: 13.5px 30px 13.5px 10px;
                    border-radius: 3px;
                }


                .ui-autocomplete-input-token > input[type="text"]{
                    border: none !important;
                }
                .ui-autocomplete-multiple-container.ui-inputfield{
                    width: 90% !important;
                }
                .chosen-container-multi .chosen-choices li.search-choice .search-choice-close {
                    top: 0;
                    right: 0;
                    height: 100%;
                    width: 100%;
                    background: none;
                    color: #cbcbcb;
                }
                .chosen-container-multi .chosen-choices li.search-choice .search-choice-close:hover {
                    color: white;
                }
                .chosen-container-multi .chosen-choices li.search-choice .search-choice-close::before {
                    font-family: FontAwesome;
                    font-size: 20px;
                    content: "\f00D";
                    position: absolute;
                    top: 13px;
                    right: 6px;
                }
                td[role="gridcell"] {                         
                    word-break: break-all;
                    word-wrap: break-word;
                }
                .ui-selectmanycheckbox label, .ui-selectoneradio label {
                    font-size: 10px;
                }
                .ui-datatable-data > tr > td > a{
                    font-size: 12px !important;
                    font-weight: bold;
                }
            </style>
        </ui:define>


        <ui:define name="conteudo">
            <h:panelGroup layout="block" styleClass="container-fluid noSlider" style="width: 100%;">        
                <h:panelGroup layout="block" id="dados" style="width: 100%; margin-left: auto; margin-right: auto;" 
                              styleClass="span12 caixaPrincipal">
                    <h4>Estatísticas da Infra</h4>                    
                    <hr/>
                    <h:panelGroup id="pnlResultado" layout="block">
                        <p:commandLink styleClass="btn btn-primary" actionListener="#{EstatisticasControle.atualizar}"
                                       update=":fmLay:pnlResultado"                                       
                                       style="margin-top: 2px; margin-bottom: 2px;"
                                       ajax="true">
                            <i class="fa-icon-cloud"/> Atualizar
                        </p:commandLink>
                        <p:dataTable var="est" value="#{EstatisticasControle.listaTela}" id="lista"                                     
                                     emptyMessage="#{msg['tabela.semregistros']}"
                                     styleClass="tabelaPrincipal">
                            <f:facet name="header">
                                <center>
                                    <h:outputText value="#{fn:length(EstatisticasControle.listaTela)} infras"  />
                                </center>
                            </f:facet>
                            <p:column headerText="Infra" sortBy="#{est.infoInfra.descricao} - #{est.serverName}">
                                <h:outputText value="#{est.infoInfra.descricao} - #{est.serverName}"/>
                            </p:column>
                            <p:column  colspan="2" headerText="Storage" width="180" sortBy="#{est.diskFree}">
                                <h:panelGroup>
                                    <meter min="0" low="#{est.diskFree}" style="width: 280px; height: 30px;"                                           
                                           max="#{est.diskSize}"                                           
                                           value="#{est.diskUsed}" 
                                           title="#{est.title}">
                                    </meter>
                                </h:panelGroup>
                                <h:panelGroup layout="block">
                                    <h:outputText style="font-weight: bold;" value="Free: #{est.diskFree_Apresentar}"/>                                
                                </h:panelGroup>
                            </p:column>
                        </p:dataTable>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <p:hotkey bind="esc" handler="$('.ui-dialog').hide(); $('.ui-widget-overlay').hide(); $('.ui-growl').hide();"  />
        </ui:define>        
    </ui:decorate>
</html>
