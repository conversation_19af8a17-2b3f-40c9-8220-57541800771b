<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
    <ui:decorate template="/template/layout.xhtml">
        <ui:define name="conteudo">
            <style>
                @media (max-width: 1400px){
                    .container,
                    .navbar-static-top
                    .container,
                    .navbar-fixed-top
                    .container,
                    .navbar-fixed-bottom
                    .container {
                        width: 100% !important;
                    }
                }
            </style>
            <div id="divConfigPipz" class="container-fl uid noSlider" >
                <h:panelGroup layout="block" id="addEdit"
                              pt:data-spy="affix" styleClass="span7 menuLateral" pt:data-offset-top="0"
                              style="top: 70px; position: absolute; display: table;">
                    <h4>Configuração do Pipz</h4>

                    <h:panelGroup id="groupAddEdit" style="width: 100%">
                        <h:panelGrid columns="2" width="100%" >
                            <h:outputText value="URL Requisição: " style="margin-left: 19px;" />
                            <h:inputText  style="margin-bottom: 0; width: 400px;margin-left: 19px;" value="#{PipzBean.pipz.urlPost}" ></h:inputText>
                            <h:outputText value="Chave Zillyon: " style="margin-left: 19px;" />
                            <h:inputText style="margin-bottom: 0; width: 200px; margin-left: 19px;" readonly="false" value="#{PipzBean.pipz.chaveZillyon}" ></h:inputText>
                            <h:outputText value="Chave Treino: " style="margin-left: 19px;" />
                            <h:inputText style="margin-bottom: 0; width: 200px; margin-left: 19px;" readonly="false" value="#{PipzBean.pipz.chaveTreino}" ></h:inputText>
                            <h:outputText value="Chave Ucp: " style="margin-left: 19px;" />
                            <h:inputText style="margin-bottom: 0; width: 200px; margin-left: 19px;" readonly="false" value="#{PipzBean.pipz.chaveUcp}" ></h:inputText>
                            <h:outputText value="Chave Site: " style="margin-left: 19px;" />
                            <h:inputText style="margin-bottom: 0; width: 200px; margin-left: 19px;" readonly="false" value="#{PipzBean.pipz.chaveSite}" ></h:inputText>
                            <h:outputText value="Chave Blog: " style="margin-left: 19px;" />
                            <h:inputText style="margin-bottom: 0; width: 200px; margin-left: 19px;" readonly="false" value="#{PipzBean.pipz.chaveBlog}" ></h:inputText>
                            <h:outputText value="Chave NFSe: " style="margin-left: 19px;" />
                            <h:inputText style="margin-bottom: 0; width: 200px; margin-left: 19px;" readonly="false" value="#{PipzBean.pipz.chaveNFSE}" ></h:inputText>
                            <h:outputText value="Chave Aula Cheia: " style="margin-left: 19px;" />
                            <h:inputText style="margin-bottom: 0; width: 200px; margin-left: 19px;" readonly="false" value="#{PipzBean.pipz.chaveAulaCheia}" ></h:inputText>
                            <h:outputText value="Chave Central de Eventos: " style="margin-left: 19px;" />
                            <h:inputText style="margin-bottom: 0; width: 200px; margin-left: 19px;" readonly="false" value="#{PipzBean.pipz.chaveCentralDeEventos}" ></h:inputText>
                            <h:outputText value="Chave CRM " style="margin-left: 19px;" />
                            <h:inputText style="margin-bottom: 0; width: 200px; margin-left: 19px;" readonly="false" value="#{PipzBean.pipz.chaveCRM}" ></h:inputText>
                            <h:outputText value="Chave Financeiro " style="margin-left: 19px;" />
                            <h:inputText style="margin-bottom: 0; width: 200px; margin-left: 19px;" readonly="false" value="#{PipzBean.pipz.chaveFinanceiro}" ></h:inputText>
                            <h:outputText value="Chave Game of Results " style="margin-left: 19px;" />
                            <h:inputText style="margin-bottom: 0; width: 200px; margin-left: 19px;" readonly="false" value="#{PipzBean.pipz.chaveGameOfResults}" ></h:inputText>
                            <h:outputText value="Chave Studio " style="margin-left: 19px;" />
                            <h:inputText style="margin-bottom: 0; width: 200px; margin-left: 19px;" readonly="false" value="#{PipzBean.pipz.chaveStudio}" ></h:inputText>
                            <h:outputText value="Chave Gestao de Personal " style="margin-left: 19px;" />
                            <h:inputText style="margin-bottom: 0; width: 200px; margin-left: 19px;" readonly="false" value="#{PipzBean.pipz.chaveGestaoDePersonal}" ></h:inputText>
                            <h:outputText value="Chave Smart Box " style="margin-left: 19px;" />
                            <h:inputText style="margin-bottom: 0; width: 200px; margin-left: 19px;" readonly="false" value="#{PipzBean.pipz.chaveSmartBox}" ></h:inputText>
                            <h:outputText value="Chave Zillyon Auto Atendimento " style="margin-left: 19px;" />
                            <h:inputText style="margin-bottom: 0; width: 200px; margin-left: 19px;" readonly="false" value="#{PipzBean.pipz.chaveZillyonAutoAtendimento}"  ></h:inputText>
                            <h:outputText value="Chave Novo Treino " style="margin-left: 19px;" />
                            <h:inputText style="margin-bottom: 0; width: 200px; margin-left: 19px;" readonly="false" value="#{PipzBean.pipz.chaveNovoTreino}"  ></h:inputText>
                        </h:panelGrid>
                    </h:panelGroup>
                    <h:panelGroup id="pnlBotoes" layout="block" style="margin-bottom: 6px; margin-top: 20px" styleClass="pull-right">
                        <p:commandLink styleClass="btn btn-primary" action="#{PipzBean.salvar}"
                                       update=":fmLay:addEdit"
                                       ajax="true" >
                            <i class="fa-icon-save"/> Salvar
                        </p:commandLink>
                    </h:panelGroup>
                </h:panelGroup>
            </div>
        </ui:define>
    </ui:decorate>
</html>
