<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
    <ui:decorate template="/template/layout.xhtml">
        <ui:define name="CSS">

        </ui:define>
        <ui:define name="conteudo">
            
       <h:panelGroup styleClass="span9 offset3 caixaPrincipal" id="dados"
                     style="padding: 20px; width: 90%; margin-left: 3%;">
                    <h4>Feeds cadastrados</h4>
                    <br />
                    <h:panelGroup id="dadosEdicaoCapa"
                                  rendered="#{FeedGestaoControle.editandoCapa}">
                        <h:panelGrid columns="2"  style="font-weight: bold;"> 
                            <h:outputText value="Título Grande:" />
                            <h:inputText value="#{FeedGestaoControle.paginaInicial.tituloGrande}" size="26" maxlength="26"/>    
                            <h:outputText value="Imagem:"/>
                            <h:inputText value="#{FeedGestaoControle.paginaInicial.linkImagem}"/>    
                            <h:outputText value="Vídeo:"/>
                            <h:inputText value="#{FeedGestaoControle.paginaInicial.linkVideo}"/>    
                               
                        </h:panelGrid>
                        <h:panelGrid columns="1" width="100%" style="font-weight: bold;">
                            <h:outputText value="Texto Capa:"/>
                            <h:inputTextarea id="editor1" value="#{FeedGestaoControle.paginaInicial.mensagemCapa}"
                                             style="width: 700px; height: 80px;"
                                             rows="3"
                                             />  
                            <h:outputText value="Texto Balão Grande:"/>
                            <h:inputTextarea id="editor2" value="#{FeedGestaoControle.paginaInicial.textoBalaoGrande}"
                                             style="width: 700px; height: 80px;"
                                             rows="2"
                                             />  
                        </h:panelGrid>
                        
                        <p:commandLink styleClass="btn btn-primary" action="#{FeedGestaoControle.salvarPaginaInicial}"
                                       update=":fmLay:dados"
                                       ajax="true" >
                            <i class="fa-icon-save"/> #{title['cadastros.salvar']}
                        </p:commandLink>
                        
                        <p:spacer width="5" />
                        
                        <p:commandLink styleClass="btn btn-primary" action="#{FeedGestaoControle.cancelar}"
                                       update=":fmLay:dados"
                                       ajax="true" >Voltar para feeds
                        </p:commandLink>
                    </h:panelGroup>

                    <h:panelGroup id="dadosEdicao"
                                  rendered="#{FeedGestaoControle.editando}">

                        <h:graphicImage url="resources/imagens/indicadores/#{FeedGestaoControle.feed.indicadorGrupo.image}"
                                        style="position: absolute; right: 30px; top: 30px;"
                                        rendered="#{FeedGestaoControle.feed.indicadorGrupo != null}"/>

                        <h:panelGroup layout="block" >
                            <h:panelGrid columns="2"  style="font-weight: bold;">
                                <h:outputText value="Indicador:"/>
                                <p:selectOneMenu value="#{FeedGestaoControle.feed.indicadorGrupo}"
                                                 style="width: 250px;"
                                                 id="idindgrupo">
                                    <f:selectItem itemLabel="" itemValue=""/>
                                    <f:selectItems value="#{FeedGestaoControle.indicadores}" var="indG" itemLabel="#{title[indG.nome]}"/>
                                    <p:ajax  update=":fmLay:dadosEdicao" event="change"/>
                                </p:selectOneMenu>

                                <h:outputText value="Nome:"/>
                                <h:inputText value="#{FeedGestaoControle.feed.nome}"
                                             id="idnome"/>    
                            </h:panelGrid>
                            <h:panelGrid columns="1" width="100%" style="font-weight: bold;">
                                <h:outputText value="Mensagem:"/>
                                <h:inputTextarea id="editor" value="#{FeedGestaoControle.feed.mensagem}"
                                                 style="width: 700px; height: 80px;"
                                                 />  
                            </h:panelGrid>
                            <h:panelGrid columns="2" style="font-weight: bold;">
                                <h:outputText value="Dica:"/>
                                <p:selectOneMenu value="#{FeedGestaoControle.feed.dica}"
                                                 style="width: 350px;"
                                                 id="idind">
                                    <f:selectItem itemLabel="" itemValue=""/>
                                    <f:selectItems value="#{FeedGestaoControle.dicas}" var="ind" itemLabel="#{title[ind.nome]}"/>
                                    <p:ajax listener="#{FeedGestaoControle.abrirTags}" update=":fmLay:tagtag"
                                            event="change"/>
                                </p:selectOneMenu>

                                <h:outputText value="Condição:"/>
                                <p:selectOneMenu value="#{FeedGestaoControle.feed.condicao}"
                                                 id="idcondicao">
                                    <f:selectItems value="#{FeedGestaoControle.condicoes}" var="cond" itemLabel="#{title[cond.nome]}"/>
                                </p:selectOneMenu>

                                <h:outputText value="Valor de Condição:"/>
                                <h:inputText value="#{FeedGestaoControle.feed.valorCondicao}"

                                             id="idvcondicao"/>

                                <h:outputText value="Início:"/>
                                <p:calendar id="popupButtonCal1"  locale="br" pattern="dd/MM/yyyy"
                                            style="width: 75%;" value="#{FeedGestaoControle.feed.vigenciaDe}">
                                </p:calendar>

                                <h:outputText value="Fim:"/>
                                <p:calendar id="popupButtonCal2"  locale="br" pattern="dd/MM/yyyy"
                                            style="width: 75%;" value="#{FeedGestaoControle.feed.vigenciaAte}">
                                </p:calendar>

                                <h:outputText value="Periodicidade:"/>
                                <h:inputText value="#{FeedGestaoControle.feed.periodicidade}"

                                             id="idperiod"/>

                                <h:outputText value="Até X dia do mês:"/>
                                <h:inputText value="#{FeedGestaoControle.feed.diaMes}"
                                             id="iddia"/>

                                <h:outputText value="Perfil alvo:"/>
                                <p:selectOneMenu value="#{FeedGestaoControle.feed.perfil}">
                                    <f:selectItems value="#{FeedGestaoControle.perfis}" var="perf" itemLabel="#{title[perf.nome]}"/>
                                </p:selectOneMenu>
                            </h:panelGrid>
                        </h:panelGroup>
                        <p:commandLink styleClass="btn btn-primary" action="#{FeedGestaoControle.novoFeed}"
                                       update=":fmLay:dados"
                                       ajax="true" >
                            <i class="fa-icon-save"/> Novo
                        </p:commandLink>
                        <p:commandLink styleClass="btn btn-primary" action="#{FeedGestaoControle.gravar}"
                                       update=":fmLay:dados"
                                       ajax="true" >
                            <i class="fa-icon-save"/> #{title['cadastros.salvar']}
                        </p:commandLink>
                        <p:spacer width="5" rendered="#{FeedGestaoControle.feed.codigo > 0}"/>
                        <p:commandLink styleClass="btn btn-primary" action="#{FeedGestaoControle.excluir}" 
                                       update=":fmLay:dados"
                                       rendered="#{FeedGestaoControle.feed.codigo > 0}"
                                       ajax="true" >
                            <i class="fa-icon-remove"/> #{title['cadastros.excluir']}
                        </p:commandLink>
                        <p:spacer width="5" />
                        <p:commandLink styleClass="btn btn-primary" 
                                       update=":fmLay:modalTags" oncomplete="wvTags.show();"
                                       action="#{FeedGestaoControle.abrirTags}"
                                       ajax="true" >Ver tags
                        </p:commandLink>
                        <p:commandLink styleClass="btn btn-primary" action="#{FeedGestaoControle.cancelar}"
                                       update=":fmLay:dados"
                                       ajax="true" >Cancelar
                        </p:commandLink>
                    </h:panelGroup>


                    <p:outputPanel layout="block" styleClass="pesq pesq-tabelas buscaAuxiliar" id="filtrar" 
                                   rendered="#{!FeedGestaoControle.editando and !FeedGestaoControle.editandoCapa}">
                        <p:inputText  id="globalFilter" value="#{FeedGestaoControle.filtro}">
                            <f:ajax event="keyup" listener="#{FeedGestaoControle.filtrar(FeedGestaoControle.feeds)}" 
                                    delay="#{FeedGestaoControle.tempoDelay}"
                                    render=":fmLay:lista"  
                                    />
                        </p:inputText>

                        <p:watermark for="globalFilter" value="#{title['cadastros.filtrar']}" />
                    </p:outputPanel>


                    <p:dataTable var="feed" value="#{FeedGestaoControle.listaTela}" id="lista" widgetVar="tabelaAtual" 
                                 emptyMessage="#{msg['tabela.semregistros']}"
                                 styleClass="tabelaPrincipal" 
                                 rendered="#{!FeedGestaoControle.editando  and !FeedGestaoControle.editandoCapa}">
                        <f:facet name="header">
                            <h:panelGroup layout="block" id="totalTabela" styleClass="totalizaTabela">
                                <h:outputText rendered="#{FeedGestaoControle.totalTabela != fn:length(FeedGestaoControle.feeds)}" 
                                              value="#{FeedGestaoControle.totalTabela} de "  />
                                <h:outputText   value="#{fn:length(FeedGestaoControle.feeds)} "  />
                                <h:outputText   value="feeds"  />
                            </h:panelGroup>
                        </f:facet>
                        <p:column id="colunaImg" headerText="-">
                            <p:commandLink ajax="true" partialSubmit="true" 
                                           action="#{FeedGestaoControle.editar(feed)}"
                                           update=":fmLay:dados" 
                                           >
                                <h:graphicImage url="resources/imagens/indicadores/#{feed.indicadorGrupo.image}"
                                                style="width: 30px; height: 30px;"
                                                rendered="#{feed.indicadorGrupo != null}"/></p:commandLink>
                        </p:column>
                        <p:column id="colunaNome" headerText="Nome" sortBy="#{feed.nome}">
                            <p:commandLink ajax="true" partialSubmit="true" 
                                           action="#{FeedGestaoControle.editar(feed)}"
                                           update=":fmLay:dados" 
                                           >#{feed.nome}</p:commandLink>
                        </p:column>
                        <p:column id="colunaMsg" headerText="Mensagem" sortBy="#{feed.mensagemShort}">
                            <p:commandLink ajax="true" partialSubmit="true" 
                                           action="#{FeedGestaoControle.editar(feed)}"
                                           update=":fmLay:dados" 
                                           >#{feed.mensagemShort}</p:commandLink>
                        </p:column>
                        <p:column id="colunaPerf" headerText="Perfil" sortBy="#{feed.perfil.nome}">
                            <p:commandLink ajax="true" partialSubmit="true" 
                                           action="#{FeedGestaoControle.editar(feed)}"
                                           update=":fmLay:dados" 
                                           >#{title[feed.perfil.nome]}</p:commandLink>
                        </p:column>

                        <p:column id="colunaVigente" headerText="Vigente" sortBy="#{feed.vigente}">
                            <p:commandLink ajax="true" partialSubmit="true" 
                                           action="#{FeedGestaoControle.editar(feed)}"
                                           update=":fmLay:dados" 
                                           >#{feed.vigente}</p:commandLink>
                        </p:column>
                        
                        <p:column id="estatisticas" headerText="Estatísticas">
                            <p:commandLink ajax="true" partialSubmit="true" 
                                           oncomplete="modalEstats.show();"
                                           action="#{FeedGestaoControle.verEstatisticas(feed)}"
                                           update=":fmLay:modalEstatsId">
                                <i class="fa-icon-bar-chart"/>
                            </p:commandLink>
                        </p:column>
                    </p:dataTable>
                    <br/>
                    <p:commandLink styleClass="btn btn-primary" action="#{FeedGestaoControle.novoFeed}"
                                   update=":fmLay:dados"
                                   ajax="true" 
                                   rendered="#{!FeedGestaoControle.editando  and !FeedGestaoControle.editandoCapa}">
                        <i class="fa-icon-save"/> Novo
                    </p:commandLink>
                    <p:commandLink styleClass="btn btn-primary" action="#{FeedGestaoControle.editarPaginaInicial}"
                                   update=":fmLay:dados"
                                   ajax="true" 
                                   rendered="#{!FeedGestaoControle.editando  and !FeedGestaoControle.editandoCapa}">
                        <i class="fa-icon-save"/> Editar Capa
                    </p:commandLink>

                </h:panelGroup>

    

            <p:dialog header="Tags para colocar na mensagem" widgetVar="wvTags" minHeight="40"
                      id="modalTags">
                <h:outputText id="tagtag" value="#{FeedGestaoControle.tags}" />
            </p:dialog>
            
            <p:dialog header="Estatísticas do feed #{FeedGestaoControle.estatisticas.nome}" 
                      widgetVar="modalEstats" minHeight="40"
                      id="modalEstatsId">
                <h:outputText value="Total de visualizações:" />
                <h:outputText value="#{FeedGestaoControle.estatisticas.vistas}" /><br/>
                <h:outputText value="Total de curtidas:" />
                <h:outputText value="#{FeedGestaoControle.estatisticas.curtidas}" /><br/>
                <h:outputText value="Total de descurtidas:" />
                <h:outputText value="#{FeedGestaoControle.estatisticas.descurtidas}" /><br/>
            </p:dialog>



        </ui:define>

        <ui:define name="JS">
            <h:outputScript library="js" name="cadastrosV2.js"/>
        </ui:define>
    </ui:decorate>
</html>
