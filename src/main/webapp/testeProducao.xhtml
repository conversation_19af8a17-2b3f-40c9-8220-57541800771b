<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:p="http://primefaces.org/ui">

<h:head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>Dashboard - Teste Producao</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no"/>

    <link href="resources/css/bootstrap.min.css" rel="stylesheet"/>
    <link href="resources/css/bootstrap-responsive.css" rel="stylesheet"/>
    <link href="resources/css/DT_bootstrap.css" rel="stylesheet"/>
    <link href="resources/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css" rel="stylesheet" integrity="sha384-T8Gy5hrqNKT+hzMclPo118YTQO6cYprQmhrYwIiQ/3axmI1hQomh7Ud2hPOy8SP1" crossorigin="anonymous"/>


    <style type="text/css">

        .tituloMenuSuperior {
            color: white !important;
            font-size: 18px;
            font-weight: bold;
        }

        .tituloTeste {
            color: white !important;
            font-size: 16px;
            font-weight: bold;
        }

        .moduloAfetado {
            color: white !important;
            font-size: 14px;
            font-weight: bold;
        }

        .moduloNaoAfetado {
            color: #666 !important;
            font-size: 14px;
            font-weight: bold;
        }

        .panelMenuTotalizadores {
            display: flex;
            margin: 20px;
            margin-top: 10px;
            margin-bottom: 10px;
        }

        .caixaGeralTeste {
            /*height: 9em;*/
            /*float: left;*/
            /*padding: 10px;*/
            /*min-width: 150px;*/
            /*text-align: center;*/

            float: left;
            padding: 10px;
            text-align: center;
        }

        .panelTesteSuperior {
            padding: 12px 0px;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }

        .panelTesteCentro {
            /*padding: 25px 0px;*/
            display: inline-flex;
            width: 100%;
        }

        .panelTesteInferior {
            padding: 5px;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
            display: flex;
            text-align: center;
            border-top: 1px solid #666;
        }

        .panelModuloAfetados {
            padding: 5px;
        }

        .menuSuperior {
            padding: 8px;
            padding-left: 21px;
            padding-right: 21px;
        }

        .backPretoOpacity {
            background: #030904;
        }

        .panelFalha {
            text-align: center;
            padding: 20px;
            width: 18%;
        }

        .backgroundFalha {
            background: #971316; /* Old browsers */
            background: -moz-linear-gradient(top, #971316 79%, #e51d16 100%); /* FF3.6-15 */
            background: -webkit-linear-gradient(top, #971316 79%, #e51d16 100%); /* Chrome10-25,Safari5.1-6 */
            background: linear-gradient(to bottom, #971316 79%, #e51d16 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#971316', endColorstr='#e51d16', GradientType=0); /* IE6-9 */
        }

        .panelSucesso {
            text-align: center;
            padding: 20px;
            width: 18%;
            border-bottom-left-radius: 10px;
            border-top-left-radius: 10px;
        }

        .backgroundSucesso {
            background: #256c16; /* Old browsers */
            background: -moz-linear-gradient(top, #256c16 79%, #00e524 100%); /* FF3.6-15 */
            background: -webkit-linear-gradient(top, #256c16 79%, #00e524 100%); /* Chrome10-25,Safari5.1-6 */
            background: linear-gradient(to bottom, #256c16 79%, #00e524 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#256c16', endColorstr='#00e524', GradientType=0); /* IE6-9 */
        }

        .panelFalhaParcial {
            text-align: center;
            padding: 20px;
            width: 18%;
        }

        .backgroundFalhaParcial {
            background: #ff7916; /* Old browsers */
            background: -moz-linear-gradient(top, #ff7916 79%, #ffd300 100%); /* FF3.6-15 */
            background: -webkit-linear-gradient(top, #ff7916 79%, #ffd300 100%); /* Chrome10-25,Safari5.1-6 */
            background: linear-gradient(to bottom, #ff7916 79%, #ffd300 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff7916', endColorstr='#ffd300', GradientType=0); /* IE6-9 */
        }

        .panelFundo {
            /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#202e08+0,002e20+48,050019+98,110019+100 */
            background: #202e08; /* Old browsers */
            background: -moz-linear-gradient(left, #202e08 0%, #002e20 48%, #050019 98%, #110019 100%); /* FF3.6-15 */
            background: -webkit-linear-gradient(left, #202e08 0%, #002e20 48%, #050019 98%, #110019 100%); /* Chrome10-25,Safari5.1-6 */
            background: linear-gradient(to right, #202e08 0%, #002e20 48%, #050019 98%, #110019 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#202e08', endColorstr='#110019', GradientType=1); /* IE6-9 */
        }
    </style>
</h:head>
<body class="panelFundo">
<f:view>
    <h:form id="form">
        <h:panelGroup layout="block" id="conteudo">
            <p:poll async="true" listener="#{TesteProducaoControle.atualizarDash}" update="menuTotalizadores, panelTodosTestes, menusuperio" interval="30"/>
            <h:panelGroup layout="block" id="menusuperio" style="display: flex;">
                <h:panelGroup layout="block" id="areabotao" styleClass="menuSuperior backPretoOpacity"
                              style="width: 30%">
                    <h:commandLink action="#{TesteProducaoControle.atualizarDash}" id="carregarDados" style="text-decoration: none; font-size: 18px;">
                        <i class="fa-icon-refresh"></i>
                    </h:commandLink>
                </h:panelGroup>

                <h:panelGroup layout="block" id="infra" styleClass="menuSuperior backPretoOpacity"
                              style="width: 100%; text-align: center;">
                    <h:outputText styleClass="tituloMenuSuperior" value="Infraestrutura: #{TesteProducaoControle.infraApresentar}"/>
                </h:panelGroup>

                <h:panelGroup layout="block" id="data" styleClass="menuSuperior backPretoOpacity"
                              style="width: 70%; text-align: right;">
                    <h:outputText styleClass="tituloMenuSuperior" value="#{TesteProducaoControle.hojePorExtenso}"/>
                    <h:outputText styleClass="tituloMenuSuperior" value="#{TesteProducaoControle.agora}" style="margin-left: 16px;"/>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" id="menuTotalizadores" styleClass="panelMenuTotalizadores">

                <h:panelGroup layout="block" id="totalSucesso" styleClass="panelSucesso backgroundSucesso">
                    <h:outputText value="#{TesteProducaoControle.totalSucesso} Sucesso"
                                  styleClass="tituloTeste"/>
                </h:panelGroup>

                <h:panelGroup layout="block" id="totalFalhaParcial"
                              styleClass="panelFalhaParcial backgroundFalhaParcial">
                    <h:outputText value="#{TesteProducaoControle.totalFalhaParcial} Falha Parcial"
                                  styleClass="tituloTeste"/>
                </h:panelGroup>

                <h:panelGroup layout="block" id="totalFalha" styleClass="panelFalha backgroundFalha">
                    <h:outputText value="#{TesteProducaoControle.totalFalha} Falha" styleClass="tituloTeste"/>
                </h:panelGroup>

                <h:panelGroup layout="block" id="totalizadorDireita" styleClass="backPretoOpacity"
                              style="width: 64%; padding: 20px; text-align: right; border-bottom-right-radius: 10px; border-top-right-radius: 10px;">
                    <h:outputText value="#{TesteProducaoControle.totalTestes} Testes" styleClass="tituloTeste"/>
                </h:panelGroup>

            </h:panelGroup>


            <h:panelGroup layout="block" id="panelTodosTestes" style=" margin: 10px;">

                <ui:repeat value="#{TesteProducaoControle.listaTestes}" var="obj">
                    <h:panelGroup layout="block" styleClass="caixaGeralTeste" id="testeID">

                        <h:panelGroup layout="block" id="panelTesteSuperior"
                                      styleClass="panelTesteSuperior #{obj.background}">
                            <h:outputLink onclick="window.open('#{obj.url}','_blank')" title="Clique para abrir Jenkins">
                                <h:outputText value="#{obj.titulo}" styleClass="tituloTeste"/>
                            </h:outputLink>
                        </h:panelGroup>

                        <h:panelGroup layout="block" id="panelTesteCentro"
                                      styleClass="panelTesteCentro backPretoOpacity">

                            <h:panelGroup layout="block" id="tempoUltExecucao" style="padding: 10px;">
                                <h:outputText value="#{obj.minutosUltimaExecucao}" styleClass="tituloTeste"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="frequenciaExecucao" style="padding: 10px 0px;">
                                <h:outputText value="#{obj.frequencia}" title="Frequência de Execução"
                                              style="color: #666 !important; font-size: 16px; font-weight: bold;">
                                    <i class="fa-icon-refresh" style="font-size: 18px"></i>
                                </h:outputText>
                            </h:panelGroup>
                        </h:panelGroup>


                        <h:panelGroup layout="block" id="panelTesteInferior"
                                      styleClass="panelTesteInferior backPretoOpacity">
                            <h:panelGroup layout="block" id="zw" styleClass="panelModuloAfetados">
                                <h:outputText value="ZW" styleClass="#{obj.styleModuloZW}" title="Módulos Afetados"/>
                            </h:panelGroup>
                            <h:panelGroup layout="block" id="tr" styleClass="panelModuloAfetados">
                                <h:outputText value="TR" styleClass="#{obj.styleModuloTR}" title="Módulos Afetados"/>
                            </h:panelGroup>
                            <h:panelGroup layout="block" id="login" styleClass="panelModuloAfetados">
                                <h:outputText value="LOGIN" styleClass="#{obj.styleModuloLOGIN}" title="Módulos Afetados"/>
                            </h:panelGroup>
                            <h:panelGroup layout="block" id="zaw" styleClass="panelModuloAfetados">
                                <h:outputText value="ZAW" styleClass="#{obj.styleModuloZAW}" title="Módulos Afetados"/>
                            </h:panelGroup>
                            <h:panelGroup layout="block" id="oamd" styleClass="panelModuloAfetados">
                                <h:outputText value="OAMD" styleClass="#{obj.styleModuloOAMD}" title="Módulos Afetados"/>
                            </h:panelGroup>
                            <h:panelGroup layout="block" id="outros" styleClass="panelModuloAfetados">
                                <h:outputText value="OUTROS" styleClass="#{obj.styleModuloOUTROS}" title="Módulos Afetados"/>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </ui:repeat>
            </h:panelGroup>
        </h:panelGroup>
    </h:form>
</f:view>

</body>

</html>