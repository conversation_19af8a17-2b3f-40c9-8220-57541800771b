<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
    <ui:decorate template="/template/layout.xhtml">
        <ui:define name="CSS">

        </ui:define>
        <ui:define name="conteudo">
    <!-- EXEMPLO FEED -->
            <script>
                indiceAtual = 1;

                function proximaDica() {
                    jQuery('.blocoDica').removeClass('ativo');
                    indiceAtual++;
                    var seletor = ".blocoDica:nth-child(" + indiceAtual + ")";
                    jQuery(seletor).addClass('ativo');
                }

                function voltaDica() {
                    jQuery('.blocoDica').removeClass('ativo');
                    indiceAtual--;
                    var seletor = ".blocoDica:nth-child(" + indiceAtual + ")";
                    jQuery(seletor).addClass('ativo');
                }

                function mudarCorBack() {
                    var cor = '#FFFFFF';
                    alert(cor);
                }
            </script>
            <style>
                .botoesIndicadores{
                    padding-top: 3px;
                    width: 81px;
                    height: 81px;
                }
                .caixaTexto{
                    background-color: white !important;
                    border-radius: 10px !important;
                    width: 420px;
                    padding: 10px;
                }
                .like{
                    position:relative;
                    float:right; 
                    padding-right: 8px;
                }
                .dislike{
                    float:right;
                }
                .tituloDica{
                    font-weight: bold;
                    color: #1A4267;
                    font-family: 'Trebuchet MS', Helvetica, sans-serif !important;
                    font-size: 29.25px;
                    font-style: italic;
                }
                .contador{
                    font-weight: bold;
                    color: #1A4267;
                    font-family: 'Trebuchet MS', Helvetica, sans-serif !important;
                    font-size: 20px;
                }
                .textoDica{
                    font-size: 13pt;
                    font-family: 'Trebuchet MS', Helvetica, sans-serif !important; 
                    color: #1A4267;
                    text-align: justify;
                    line-height:150%;
                }
                .textoCapa{
                    font-size: 14pt; 
                    color: #1A4267; 
                    font-family: 'Trebuchet MS', Helvetica, sans-serif; 
                    line-height:150%;text-align: justify; 
                }
                /* Modal Dicas e Novidades */
                .modalDicNovos .rich-mpnl-mask-div {
                    background-color: rgba(0,0,0,0.7);
                }
                .modalDicNovos .rich-mpnl-shadow {
                    display: none;
                }
                .modalDicNovos .rich-mpnl-body,
                .modalDicNovos .rich-mpnl-content {
                    background: transparent;
                    color: white;
                    border: none;
                }
                .modalDicNovos p[class*=stepy-] {
                    margin-top: 10px;
                }
                .modalDicNovos .step,
                .modalDicNovos .step h4 {
                    font-size: 18px !important;
                }
                .modalDicNovos h1 {
                    font-size: 48px;
                    margin-bottom: 0px;
                    margin-top: 0px;
                }
                .modalDicNovos p.descr {
                    margin-bottom: 10px;
                }
                .modalDicNovos .fecharModal {
                    color: white;
                    font-size: 32px !important;
                    margin-right: -35px;
                }
                /* Modal Assistente Pacto */
                .modalDicNovos.assistente .rich-mpnl-text.rich-mpnl-controls {
                    z-index: 10 !important;
                }
                .modalDicNovos.assistente .fecharModal {
                    margin-right: 5px;
                    opacity: 0.5;
                    font-size: 24px !important;
                    position: relative;
                }
                .modalDicNovos.assistente .fecharModal:hover {
                    opacity: 0.8;
                }
                .modalDicNovos.assistente .blocoFeed {
                    position: relative;
                    float: left;
                    background-color: #00456C;
                    top: 0px;
                    border-radius: 10px;
                    background-image: url("./feed_gestao/assistente/balao-menor.png"), url("./feed_gestao/assistente/balaocinza.png"),url("./feed_gestao/assistente/carinha-home.png");
                    background-position: 520px 200px,520px 50px, 780px 230px;
                    background-repeat: no-repeat;
                    width: 940px;
                    height: 460px;
                    padding: 10px;
                }
                .modalDicNovos.assistente .blocoDica {
                    position: absolute;
                    float: left;
                    top: 0px;
                }

                .modalDicNovos.assistente .blocoFeed.indicador {
                    background: none;

                }
                .modalDicNovos.assistente .blocoDica {
                    opacity: 0;
                    z-index: 1;
                    font-size: 12px;
                    -webkit-transition: opacity 1s;
                    -moz-transition: opacity 1s;
                    -ms-transition: opacity 1s;
                    -o-transition: opacity 1s;
                    transition: opacity 1s;
                    display: none;
                }
                .modalDicNovos.assistente .blocoDica.ativo {
                    opacity: 1;
                    z-index: 2;
                    display: block;
                }
                .modalDicNovos.assistente .blocoFeed a[class*='btn-pr'] {
                    opacity: 0.5;
                    text-decoration: none !important;
                }
                .modalDicNovos.assistente .blocoFeed a[class*='btn-pr']:hover {
                    opacity: 0.8;
                }
                .modalDicNovos.assistente .blocoDica .btn-prev {
                    color: white;
                    position: absolute;
                    left: 215px;
                    top: 430px;

                }

                .modalDicNovos.assistente .blocoDica .btn-prox {
                    color: white;
                    position: absolute;
                    left: 245px;
                    top: 430px;
                }
            </style>
            
                <h:panelGroup layout="block" id="feedPainelModal"  styleClass="default margin-v-10">

                    <h:panelGroup layout="block" style="float: left">

                        <p:commandLink action="#{FeedGestaoControle.voltarCapa}" update="feedPainelModal" 
                                       onclick="indiceAtual = 1;">
                            <h:graphicImage url="/feed_gestao/assistente/carinha-icon.png" width="81px" height="81px"/>

                        </p:commandLink>

                        <br/>
                        <h:panelGroup layout="block" style="position: absolute;left: 70px; top: 120px; " 
                                      rendered="#{FeedGestaoControle.naoLidasFinanceiro > 0}">
                            <div class="notificacaoAtividades">
                                <h:outputText  value="#{FeedGestaoControle.naoLidasFinanceiro}"></h:outputText>
                            </div>
                        </h:panelGroup>
                        <p:commandLink id="dcc" actionListener="#{FeedGestaoControle.mudarIndicador}" update="feedPainelModal" 
                                       onclick="indiceAtual = 1;">
                            <h:graphicImage url="/feed_gestao/assistente/financeiro.png" styleClass="botoesIndicadores" />   

                            <f:attribute name="codigoIndicador" value="0"/>
                        </p:commandLink ><br/>



                        <h:panelGroup layout="block" style="position: absolute;left: 70px; top: 205px; "
                                      rendered="#{FeedGestaoControle.naoLidasVendas > 0}">
                            <div class="notificacaoAtividades">
                                <h:outputText  value="#{FeedGestaoControle.naoLidasVendas}"></h:outputText>
                            </div>
                        </h:panelGroup>
                        <p:commandLink actionListener="#{FeedGestaoControle.mudarIndicador}" update="feedPainelModal" 
                                       id="ticket" onclick="indiceAtual = 1;">
                            <h:graphicImage url="/feed_gestao/assistente/vendas.png" styleClass="botoesIndicadores"/>
                            <f:attribute name="codigoIndicador" value="1"/>
                        </p:commandLink>
                        <br/>

                        <h:panelGroup layout="block" style="position: absolute;left: 70px; top: 290px; "
                                      rendered="#{FeedGestaoControle.naoLidasCRM > 0}">
                            <div class="notificacaoAtividades">
                                <h:outputText  value="#{FeedGestaoControle.naoLidasCRM}"></h:outputText>
                            </div>
                        </h:panelGroup>
                        <p:commandLink id="icv" actionListener="#{FeedGestaoControle.mudarIndicador}" update="feedPainelModal" 
                                       onclick="indiceAtual = 1;">
                            <h:graphicImage url="/feed_gestao/assistente/crm.png" styleClass="botoesIndicadores"/>  
                            <f:attribute name="codigoIndicador" value="2"/>
                        </p:commandLink><br/>



                        <h:panelGroup layout="block" style="position: absolute;left: 70px; top: 372px; "
                                      rendered="#{FeedGestaoControle.naoLidasRetencao > 0}">
                            <div class="notificacaoAtividades">
                                <h:outputText  value="#{FeedGestaoControle.naoLidasRetencao}"></h:outputText>
                            </div>
                        </h:panelGroup>    
                        <p:commandLink id="contrato" actionListener="#{FeedGestaoControle.mudarIndicador}" update="feedPainelModal"
                                       onclick="indiceAtual = 1;">
                            <h:graphicImage url="/feed_gestao/assistente/retencao.png" styleClass="botoesIndicadores"/>    
                            <f:attribute name="codigoIndicador" value="3"/>
                        </p:commandLink><br/>


                        <h:panelGroup layout="block" style="position: absolute;left: 70px; top: 455px; "
                                      rendered="#{FeedGestaoControle.naoLidasTreino > 0}">
                            <div class="notificacaoAtividades">
                                <h:outputText  value="#{FeedGestaoControle.naoLidasTreino}"></h:outputText>
                            </div>
                        </h:panelGroup>  
                        <p:commandLink id="treino" actionListener="#{FeedGestaoControle.mudarIndicador}" update="feedPainelModal" 
                                       onclick="indiceAtual = 1;">
                            <h:graphicImage url="/feed_gestao/assistente/treino.png" styleClass="botoesIndicadores"/>    
                            <f:attribute name="codigoIndicador" value="4"/>
                        </p:commandLink><br/>


                    </h:panelGroup>
                    <h:panelGroup layout="block" style="position:relative; float: left">
                        <h:panelGroup layout="block" styleClass="blocoFeed ativo" rendered="#{FeedGestaoControle.capa}"
                                      style="background-color: #FFFFFF; border-color: #1A4267;border-width: 10px;border-style: solid;">

                            <h:panelGroup style="padding-right: 470px; padding-left: 20px; padding-top: 50px; position: absolute;">
                                <h:outputText styleClass="textoCapa" 
                                              value="A Pacto Software e Gestão atráves do Pacto Método de Gestão - PMG trás periodicamente análises para apoiar a gerência de sua empresa."/>  

                            </h:panelGroup>

                            <h:panelGroup layout="block" style="padding-left: 540px;padding-top: 60px;padding-right: 50px;">
                                <h4 style="font-size: 14pt; color: black;  font-family: 'Trebuchet MS', Helvetica, sans-serif;">Olá Parceiro!</h4>  

                                <h4 style="font-size: 12.5pt; color: black;font-family: 'Trebuchet MS', Helvetica, sans-serif; padding-top: 8px;">
                                    Vamos rever seus indicadores e encontrar meios de aprimorar seus resultados?</h4>  
                            </h:panelGroup>
                            <h:panelGroup layout="block" style="padding-left: 520px;padding-top: 75px;padding-right: 150px;">
                                <h:outputText value="O PMG revisou seus indicadores e tem #{fn:length(FeedGestaoControle.feeds)} dicas para você. Vem ver!"
                                              style="font-size: 12.5pt; color: black;font-family: 'Trebuchet MS', Helvetica, sans-serif; padding-top: 8px;"
                                              escape="false"/>
                            </h:panelGroup>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="blocoFeed ativo" 
                                      rendered="#{!FeedGestaoControle.capa}"
                                      style="background-image: url('./feed_gestao/assistente/balao-menor.png'),url('./feed_gestao/assistente/balaocinza.png'),url('./feed_gestao/assistente/#{FeedGestaoControle.indicadorSelecionado.image}');
                                      background-color: #FFFFFF; 
                                      position: absolute; border-color: #{FeedGestaoControle.indicadorSelecionado.cor};border-width: 10px;border-style: solid;">


                            <h:panelGroup layout="block" style="width: 450px; padding-left: 10px;"
                                          styleClass="blocoDica ativo" 
                                          rendered="#{FeedGestaoControle.nrDicasIndicador == 0}">
                                <h:panelGroup layout="block">

                                    <h:panelGroup layout="block" style="height: 210px; padding-top:20px;">
                                        <h2><h:outputText styleClass="tituloDica" value="Fique atento"></h:outputText>     </h2>
                                        <h:panelGroup layout="block" styleClass="textoDica">
                                            <h:outputText value="Os indicadores aqui contidos vão avaliar questões disso, daquilo, daquela outra coisa e de mais um pouco. Treino é treino, treino treino e treino."></h:outputText>
                                        </h:panelGroup> 
                                    </h:panelGroup>
                                </h:panelGroup>


                            </h:panelGroup>

                            <ui:repeat value="#{FeedGestaoControle.feedsApresentar}"
                                       var="tela">
                                <h:panelGroup layout="block" style="width: 450px; padding-left: 10px;"
                                              styleClass="blocoDica #{tela.primeira ? 'ativo' : ''}" >
                                    <div layout="block" style="position:absolute; right: 0px; ">
                                        <h2 class="contador"><h:outputText value="#{tela.index}/#{fn:length(FeedGestaoControle.feedsApresentar)}"/></h2>  
                                    </div>

                                    <h:panelGroup layout="block" rendered="#{!tela.primeira}">
                                        <p:commandLink styleClass="btn-prev"
                                                       onclick="voltaDica()"
                                                       style="color: #{FeedGestaoControle.indicadorSelecionado.cor};">
                                            <span class="fa fa-icon-angle-left fa-icon-4x"/>
                                        </p:commandLink>
                                    </h:panelGroup>


                                    <h:panelGroup layout="block">

                                        <h:panelGroup layout="block" style="height: 210px; padding-top:20px;">
                                            <h2><h:outputText styleClass="tituloDica" value="#{tela.feed1.nome}"></h:outputText>     </h2>
                                            <h:panelGroup layout="block" styleClass="textoDica">
                                                <h:outputText value="#{tela.feed1.mensagem}"></h:outputText>
                                            </h:panelGroup>     

                                            <p:commandLink update="feedPainelModal"  styleClass="dislike"
                                                           title="Esta dica não é relevante!">
                                                <h:graphicImage url="/feed_gestao/assistente/dislike.png"/>
                                            </p:commandLink>
                                            <p:commandLink update="feedPainelModal" styleClass="like"
                                                           title="Curti esta dica!">
                                                <h:graphicImage url="/feed_gestao/assistente/like.png"/>
                                            </p:commandLink>
                                        </h:panelGroup>    

                                        <h:panelGroup layout="block" style="height: 210px;" rendered="#{tela.feed2 != nome}">
                                            <h2><h:outputText styleClass="tituloDica" value="#{tela.feed2.nome}"></h:outputText>      </h2>
                                            <h:panelGroup layout="block" styleClass="textoDica">
                                                <h:outputText value="#{tela.feed2.mensagem}"></h:outputText>
                                            </h:panelGroup>  
                                            <p:commandLink update="feedPainelModal" styleClass="dislike"
                                                           title="Esta dica não é relevante!">
                                                <h:graphicImage url="/feed_gestao/assistente/dislike.png"/>
                                            </p:commandLink>

                                            <p:commandLink update="feedPainelModal" styleClass="like">
                                                <h:graphicImage url="/feed_gestao/assistente/like.png"
                                                                title="Curti esta dica!"/>
                                            </p:commandLink>


                                        </h:panelGroup>  

                                    </h:panelGroup> 


                                    <p:commandLink styleClass="btn-prox" rendered="#{!tela.ultima}"
                                                   onclick="proximaDica()"
                                                   style="color: #{FeedGestaoControle.indicadorSelecionado.cor};">
                                        <span class="fa fa-icon-angle-right fa-icon-4x"/>
                                    </p:commandLink>
                                </h:panelGroup>
                            </ui:repeat>

                            <h:panelGroup layout="block" style="padding-left: 540px;padding-top: 60px;padding-right: 50px; position:absolute;">
                                <h4 style="font-size: 14pt; color: black;  font-family: 'Trebuchet MS', Helvetica, sans-serif;">Olá Parceiro!</h4>  

                                <h4 style="font-size: 12.5pt; color: black;font-family: 'Trebuchet MS', Helvetica, sans-serif; padding-top: 8px;">
                                    <h:outputText value="Vamos rever seus indicadores #{FeedGestaoControle.indicadorSelecionado.texto} e encontrar meios de aprimorar seus resultados?"/></h4>  


                            </h:panelGroup>
                            <h:panelGroup layout="block" style="padding-left: 520px;padding-top: 205px;padding-right: 150px;" 
                                          rendered="#{FeedGestaoControle.nrDicasIndicador > 0}">
                                <h:outputText value="O PMG revisou seus indicadores e tem #{FeedGestaoControle.nrDicasIndicador} dicas para você. Vem ver!"
                                              style="font-size: 12.5pt; color: black;font-family: 'Trebuchet MS', Helvetica, sans-serif; padding-top: 8px;"
                                              escape="false"/>

                            </h:panelGroup>
                            <h:panelGroup layout="block" style="padding-left: 530px;padding-top: 205px;padding-right: 150px;" 
                                          rendered="#{FeedGestaoControle.nrDicasIndicador == 0}">
                                <h:outputText value="Você não tem dicas para este indicador hoje. Bom trabalho!"
                                              style="font-size: 12.5pt; color: black;font-family: 'Trebuchet MS', Helvetica, sans-serif; padding-top: 8px;"
                                              escape="false"/>
                            </h:panelGroup>

                        </h:panelGroup>


                    </h:panelGroup>


                </h:panelGroup>
            
            
        </ui:define>

        <ui:define name="JS">
            <h:outputScript library="js" name="cadastrosV2.js"/>
        </ui:define>
    </ui:decorate>
</html>
