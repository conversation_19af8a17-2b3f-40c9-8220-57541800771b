<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="conteudo">
        <style>
            @media (max-width: 1400px) {
                .container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
                    width: 100% !important;
                }
            }

            @media (min-width: 1200px) {
                .container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
                    width: 95%;
                }
            }
        </style>

        <h:panelGroup layout="block" style="padding: 0;" id="divGeral">
            <div class="bread span12"></div>

            <h:panelGroup styleClass="span10 caixaPrincipal" id="dados"
                          layout="block"
                          style="width: 100%; padding: 0px; margin: 0px;">
                <h4>Recurso Padrão</h4>

                <h:panelGroup layout="block"
                              style="display:grid; grid-template-columns: 5fr 1fr 1fr; padding: 10px 20px">
                    <h:panelGroup layout="block" id="divFiltroEmpresa">
                        <p:outputLabel value="Buscar" style="font-weight: bold"/>
                        <p:inputText id="globalFilter" value="#{InfoMigracaoControle.filtroEmpresa}"
                                     style="width: 95%;">
                        </p:inputText>
                        <p:watermark for="globalFilter" value="#{title['cadastros.filtrar']}"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="divFiltroInfoInfra">
                        <p:outputLabel value="Zona" style="font-weight: bold"/>
                        <h:selectOneMenu value="#{InfoMigracaoControle.filtroInfoInfra}"
                                         style="margin-top: 0;width: 100%">
                            <f:selectItems value="#{InfoMigracaoControle.listaSelectItemInfoInfra}"/>
                            <p:ajax event="change" listener="#{InfoMigracaoControle.buscarEmpresas}"
                                    update=":fmLay:listaEmpresas"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="divBuscar">
                        <p:outputLabel value="" style="font-weight: bold; padding: 10px;"/>
                        <p:commandLink action="#{InfoMigracaoControle.buscarEmpresas}"
                                       styleClass="btn btn-primary"
                                       style="width: 80%;"
                                       update=":fmLay:listaEmpresas"
                                       ajax="true">
                            <i class="fa-icon-search"/>
                            Buscar
                        </p:commandLink>
                    </h:panelGroup>
                </h:panelGroup>

                <p:dataTable var="empresaFiltro" value="#{InfoMigracaoControle.empresasFiltro}" id="listaEmpresas"
                             widgetVar="tabelaAtual1"
                             emptyMessage="#{msg['tabela.semregistros']}"
                             style="margin: 0"
                             paginator="true" rowsPerPageTemplate="5,10,20,50,100,200" rows="10" paginatorPosition="bottom"
                             styleClass="tabelaPrincipal">

                    <f:facet name="header">
                        <h:panelGroup layout="block" style="display: grid; grid-template-columns: 7fr 1fr">
                            <h:panelGroup layout="block" id="divEmpresasFiltro">
                                <h:outputText
                                        value="#{fn:length(InfoMigracaoControle.empresasFiltro)} empresa(s) encontradas"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="divAdicionarTodas">
                                <p:commandLink action="#{InfoMigracaoControle.adicionarTodas}"
                                               styleClass="btn btn-secundary"
                                               style="width: 80%;"
                                               update=":fmLay:dados2"
                                               ajax="true">
                                    Adicionar todas
                                </p:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                    </f:facet>

                    <p:column headerText="CHAVE" style="text-align: center">
                        <h:outputText value="#{empresaFiltro.chave}"/>
                    </p:column>

                    <p:column headerText="IDENTIFICADOR" style="text-align: center">
                        <h:outputText value="#{empresaFiltro.identificadorEmpresa}"/>
                    </p:column>

                    <p:column headerText="COD_EMPRESA" style="text-align: center">
                        <h:outputText value="#{empresaFiltro.empresaFinanceiro.empresazw}"/>
                    </p:column>

                    <p:column headerText="EMPRESA" style="text-align: center">
                        <h:outputText value="#{empresaFiltro.empresaFinanceiro.nomeResumo}"/>
                    </p:column>

                    <p:column headerText="ZONA" style="text-align: center">
                        <h:outputText value="#{empresaFiltro.infoInfra.descricao}"/>
                    </p:column>

                    <p:column headerText="" style="text-align: center">
                        <p:commandLink ajax="true" update=":fmLay:empresasSelecionadas"
                                       action="#{InfoMigracaoControle.adicionarEmpresa(empresaFiltro)}">
                            <i class="fa-icon-plus"></i>
                        </p:commandLink>
                    </p:column>
                </p:dataTable>
            </h:panelGroup>

            <h:panelGroup styleClass="span10 caixaPrincipal" id="dados2"
                          layout="block"
                          style="width: 100%; padding: 0px; margin: 0px; margin-top: 20px">
                <h4>Novo</h4>

                <h:panelGroup layout="block"
                              style="display:grid; grid-template-columns: 5fr 1fr 1fr 1fr; padding: 10px 20px">
                    <h:panelGroup layout="block">
                        <p:selectManyCheckbox id="gridRecursoPadrao"
                                              value="#{InfoMigracaoControle.tiposInfoMigracaoPadrao}"
                                              layout="grid" columns="4" style="margin: 5px !important;">
                            <f:selectItems value="#{InfoMigracaoControle.selectItemTipoInfoMigracao}" var="modulo"
                                           itemLabel="#{modulo}"
                                           itemValue="#{modulo}"/>
                        </p:selectManyCheckbox>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="divLimpar">
                        <p:commandLink action="#{InfoMigracaoControle.novoInfoMigracao}"
                                       styleClass="btn btn-secundary"
                                       style="width: 80%;"
                                       update=":fmLay:dados2"
                                       ajax="true">
                            Novo
                        </p:commandLink>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="divGravarAutomatico">
                        <p:commandLink action="#{InfoMigracaoControle.gravarAutomatico}"
                                       styleClass="btn btn-primary"
                                       style="width: 80%;"
                                       update=":fmLay:divGeral"
                                       ajax="true">
                            Update usuários (Automático)
                        </p:commandLink>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="divGravar">
                        <p:commandLink action="#{InfoMigracaoControle.gravarEmpresa}"
                                       styleClass="btn btn-primary"
                                       style="width: 80%;"
                                       update=":fmLay:divGeral"
                                       ajax="true">
                            Gravar (Recurso Padrão Empresa)
                        </p:commandLink>
                        <p:selectOneMenu value="#{InfoMigracaoControle.tipoOperacao}">
                            <f:selectItem itemLabel="PADRÃO" itemValue="PADRAO"/>
                            <f:selectItem itemLabel="ADICIONAR SELECIONADOS" itemValue="ADICIONAR"/>
                            <f:selectItem itemLabel="REMOVER SELECIONADOS" itemValue="REMOVER"/>
                        </p:selectOneMenu>
                    </h:panelGroup>
                </h:panelGroup>

                <p:dataTable var="item" value="#{InfoMigracaoControle.empresasSelecionadas}" id="empresasSelecionadas"
                             widgetVar="tabelaAtual2"
                             emptyMessage="#{msg['tabela.semregistros']}"
                             style="margin: 0"
                             paginator="true" rowsPerPageTemplate="5,10,20,50,100,200" rows="10" paginatorPosition="bottom"
                             styleClass="tabelaPrincipal">

                    <f:facet name="header">
                        <h:panelGroup layout="block" id="totalempresasSelecionadas">
                            <h:outputText
                                    value="#{fn:length(InfoMigracaoControle.empresasSelecionadas)} empresa(s) selecionada(s)"/>
                        </h:panelGroup>
                    </f:facet>

                    <p:column headerText="CHAVE" style="text-align: center">
                        <h:outputText value="#{item.chave}"/>
                    </p:column>

                    <p:column headerText="IDENTIFICADOR" style="text-align: center">
                        <h:outputText value="#{item.identificadorEmpresa}"/>
                    </p:column>

                    <p:column headerText="COD_EMPRESA" style="text-align: center">
                        <h:outputText value="#{item.empresaFinanceiro.empresazw}"/>
                    </p:column>

                    <p:column headerText="EMPRESA" style="text-align: center">
                        <h:outputText value="#{item.empresaFinanceiro.nomeResumo}"/>
                    </p:column>

                    <p:column headerText="ZONA" style="text-align: center">
                        <h:outputText value="#{item.infoInfra.descricao}"/>
                    </p:column>

                    <p:column headerText="" style="text-align: center">
                        <p:commandLink ajax="true" update=":fmLay:dados2"
                                       action="#{InfoMigracaoControle.removerEmpresa(item)}">
                            <i class="fa-icon-minus"></i>
                        </p:commandLink>
                    </p:column>
                </p:dataTable>
            </h:panelGroup>

            <h:panelGroup styleClass="span10 caixaPrincipal" id="dados3"
                          layout="block"
                          style="width: 100%; padding: 0px; margin: 0px; margin-top: 20px">
                <h4>Histórico</h4>

                <p:dataTable var="item" value="#{InfoMigracaoControle.listaLog}" id="listaLog"
                             widgetVar="tabelaAtual3"
                             emptyMessage="#{msg['tabela.semregistros']}"
                             style="margin: 0"
                             paginator="true" rowsPerPageTemplate="5,10,20,50,100,200" rows="10" paginatorPosition="bottom"
                             styleClass="tabelaPrincipal">
                    <p:column headerText="ID" style="text-align: center">
                        <h:outputText value="#{item.id}"/>
                    </p:column>

                    <p:column headerText="DT. REGISTRO" style="text-align: center">
                        <h:outputText value="#{item.dadosDTO.dataRegistroApresentar}"/>
                    </p:column>

                    <p:column headerText="USUARIO" style="text-align: center">
                        <h:outputText value="#{item.dadosDTO.usuarioOAMDUsername}"/>
                    </p:column>

                    <p:column headerText="RECURSOS" style="text-align: center">
                        <h:outputText value="#{item.dadosDTO.tiposInfo}"/>
                    </p:column>

                    <p:column headerText="TIPO" style="text-align: center">
                        <h:outputText value="#{item.dadosDTO.tipoLog}"/>
                    </p:column>

                    <p:column headerText="SUCESSO" style="text-align: center">
                        <h:outputText value="#{item.dadosDTO.qtdSucesso}"/>
                    </p:column>

                    <p:column headerText="FALHA" style="text-align: center">
                        <h:outputText value="#{item.dadosDTO.qtdFalha}"/>
                    </p:column>

                    <p:column headerText="" style="text-align: center">
                        <p:commandLink ajax="true" update=":fmLay:divGeral"
                                       action="#{InfoMigracaoControle.usarLog(item)}">
                            Usar empresas
                        </p:commandLink>
                    </p:column>
                </p:dataTable>
            </h:panelGroup>

            <h:panelGroup styleClass="span10 caixaPrincipal" id="dados4"
                          layout="block"
                          style="width: 100%; padding: 0px; margin: 0px; margin-top: 20px">
                <h4>Histórico - Individual</h4>

                <p:dataTable var="item" value="#{InfoMigracaoControle.listaLogIndividual}" id="listaLogInd"
                             widgetVar="tabelaAtual3"
                             emptyMessage="#{msg['tabela.semregistros']}"
                             style="margin: 0"
                             paginator="true" rowsPerPageTemplate="5,10,20,50,100,200" rows="10" paginatorPosition="bottom"
                             styleClass="tabelaPrincipal">
                    <p:column headerText="ID" style="text-align: center">
                        <h:outputText value="#{item.id}"/>
                    </p:column>

                    <p:column headerText="DT. REGISTRO" style="text-align: center">
                        <h:outputText value="#{item.dadosDTO.dataRegistroApresentar}"/>
                    </p:column>

                    <p:column headerText="USUARIO" style="text-align: center">
                        <h:outputText value="#{item.dadosDTO.usuarioOAMDUsername}"/>
                    </p:column>

                    <p:column headerText="CHAVE" style="text-align: center">
                        <h:outputText value="#{item.dadosDTO.itemSucesso.chave}"/>
                    </p:column>

                    <p:column headerText="EMPRESA" style="text-align: center">
                        <h:outputText value="#{item.dadosDTO.itemSucesso.empresa == '0' ? 'TODAS' : item.dadosDTO.itemSucesso.empresa}"/>
                    </p:column>

                    <p:column headerText="ANTERIOR" style="text-align: center">
                        <h:outputText value="#{item.dadosDTO.tiposInfoAnterior}"/>
                    </p:column>

                    <p:column headerText="RECURSOS" style="text-align: center">
                        <h:outputText value="#{item.dadosDTO.tiposInfo}"/>
                    </p:column>
                </p:dataTable>
            </h:panelGroup>
        </h:panelGroup>
    </ui:define>
    <ui:define name="JS">
        <h:outputScript library="js" name="cadastrosV2.js"/>
    </ui:define>
</ui:decorate>
</html>
