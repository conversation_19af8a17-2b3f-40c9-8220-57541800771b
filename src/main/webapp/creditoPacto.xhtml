<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="conteudo">
        <style>
            @media (max-width: 1400px) {
                .container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
                    width: 100% !important;
                }
            }

            @media (min-width: 1200px) {
                .container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
                    width: 80%;
                }
            }
        </style>

        <div style="padding: 0;">
            <div class="bread span12"></div>

            <h:panelGroup styleClass="span10 caixaPrincipal" id="dados"
                          layout="block"
                          style="width: 100%; padding: 0px; margin: 0px;">
                <h4>Tabela Recorrência - Pós-Pago Efetivado Mensal</h4>
                <hr/>
                <p:outputPanel layout="block" styleClass="pesq pesq-tabelas buscaAuxiliar" id="filtrar">
                    <p:inputText id="globalFilter" value="#{CreditoPactoControle.filtro}">
                        <f:ajax event="keyup"
                                listener="#{CreditoPactoControle.filtrarCredito}"
                                delay="#{CreditoPactoControle.tempoDelay}"
                                render=":fmLay:lista"/>
                    </p:inputText>
                    <p:watermark for="globalFilter" value="#{title['cadastros.filtrar']}"/>
                </p:outputPanel>

                <p:dataTable var="credito"
                             value="#{CreditoPactoControle.listaCreditoPactoCadastradosApresentar}" id="lista"
                             widgetVar="tabelaAtual"
                             emptyMessage="#{msg['tabela.semregistros']}"
                             style="margin: 0"
                             styleClass="tabelaPrincipal">

                    <f:facet name="header">
                        <h:panelGroup layout="block" id="totalTabela">
                            <h:outputText
                                    value="#{fn:length(CreditoPactoControle.listaCreditoPactoCadastradosApresentar)} cadastrados"/>
                            <p:commandLink styleClass="btn btn-primary"
                                           style="float: right"
                                           update=":fmLay:modalCreditoPacto"
                                           action="#{CreditoPactoControle.novoCreditoPacto}">
                                <i class="fa-icon-plus-sign-alt"/> Novo
                            </p:commandLink>
                        </h:panelGroup>
                    </f:facet>

                    <p:column headerText="Código" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       update=":fmLay:modalCreditoPacto"
                                       action="#{CreditoPactoControle.editar(credito)}"
                        >#{credito.codigo}</p:commandLink>
                    </p:column>

                    <p:column headerText="Descrição" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       update=":fmLay:modalCreditoPacto"
                                       action="#{CreditoPactoControle.editar(credito)}"
                        >#{credito.descricao}</p:commandLink>
                    </p:column>

                    <p:column headerText="Qtd Mínima" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       update=":fmLay:modalCreditoPacto"
                                       action="#{CreditoPactoControle.editar(credito)}"
                        >#{credito.qtdMinima}</p:commandLink>
                    </p:column>

                    <p:column headerText="Qtd Máxima" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       update=":fmLay:modalCreditoPacto"
                                       action="#{CreditoPactoControle.editar(credito)}"
                        >#{credito.qtdMaxima}</p:commandLink>
                    </p:column>

                    <p:column headerText="Valor Mensal" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       update=":fmLay:modalCreditoPacto"
                                       action="#{CreditoPactoControle.editar(credito)}"
                        >#{credito.valorMensalApresentar}</p:commandLink>
                    </p:column>

                    <p:column headerText="Unid. Excedente" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       update=":fmLay:modalCreditoPacto"
                                       action="#{CreditoPactoControle.editar(credito)}"
                        >#{credito.valorUnitarioExcedenteApresentar}</p:commandLink>
                    </p:column>

                    <p:column headerText="Empresas" style="text-align: center">
                        <h:panelGroup layout="block" style="display: flex; width: 99%;"
                                      rendered="#{not empty credito.empresas}">
                            <p:dataTable value="#{credito.empresas}" var="emp"
                                         styleClass="noHeader" style="width: 100%">
                                <p:column style="text-align: center">
                                    <p:commandLink ajax="true" partialSubmit="true"
                                                   update=":fmLay:modalCreditoPacto"
                                                   action="#{CreditoPactoControle.editar(credito)}">
                                        #{emp.nomeApresentarLista}
                                    </p:commandLink>
                                </p:column>
                            </p:dataTable>
                        </h:panelGroup>
                    </p:column>
                </p:dataTable>
            </h:panelGroup>
        </div>

        <ui:include src="include_modais_creditoPacto.xhtml"/>
    </ui:define>
    <ui:define name="JS">
        <h:outputScript library="js" name="cadastrosV2.js"/>
    </ui:define>
</ui:decorate>
</html>
