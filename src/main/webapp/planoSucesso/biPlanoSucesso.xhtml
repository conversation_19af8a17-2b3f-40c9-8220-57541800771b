<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:p="http://primefaces.org/ui">
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="conteudo">
        <script src="#{request.contextPath}/resources/js/amcharts/amcharts.js"></script>
        <script src="#{request.contextPath}/resources/js/amcharts/pie.js"></script>
        <link type="text/css" href="#{request.contextPath}/resources/js/amcharts/plugins/export/export.css"
              rel="stylesheet"/>
        <!-- Styles -->
        <style>
            .chartdiv {
                width: 200px;
                height: 200px;
                float: left;
                margin-left: 15%;
                cursor: pointer;
            }

            .ui-column-filter {
                display: block !important;
            }

            input.ui-inputfield {
                color: #000;
                width: 40% !important;
                height: 10px !important;
                font-size: 0.7em !important;
            }

            .alinhaCentro {
                text-align: center
            }

            p.textoGrafico {
                word-break: break-all;
            }

            span.indicadorQtd {
                font-size: 18px;
                color: #fff;
                width: 85px;
                height: 35px;
                padding-top: 10px;
                display: inline-block;
                text-align: center;
                border-radius: 10px;
            }
        </style>
        <script>
            function montarGrafico(nomeChart, porcentagem, cor) {
                var chart = AmCharts.makeChart(nomeChart, {
                    "type": "pie",
                    "marginBottom": 50,
                    "allLabels": [{
                        "x": "50%",
                        "y": "35%",
                        "text": porcentagem + " %",
                        "size": 20,
                        "align": "middle",
                        "color": "#555"
                    }],
                    "dataProvider": [{
                        "value": porcentagem,
                        "color": cor
                    }, {
                        "value": 100 - porcentagem,
                        "color": "#bebdc3"
                    }],
                    "colorField": "color",
                    "valueField": "value",
                    "labelRadius": -130,
                    "pullOutRadius": 0,
                    "labelRadius": 0,
                    "innerRadius": "80%",
                    "labelText": "",
                    "balloonText": ""
                });
            }
        </script>
        <f:metadata>
            <f:event type="preRenderView" listener="#{ModeloPlanoSucessoControle.filtrar}"/>
        </f:metadata>

        <h:panelGroup styleClass="span12 offset3 caixaPrincipal" id="biPlanoSucesso"
                      style="padding: 20px; width: 100%; margin-left: 1%;">
            <h4 style="margin-left: auto">NÍVEL DE ENGAJAMENTO</h4>
            <br/>
            <style type="text/css">
                .value {
                    font-weight: bold;
                }
            </style>
            <div style="width:100%; display: inline-flex;">
                <div style="width:50%">
                    <p:outputPanel>
                        <p:inputText id="globalFilter" value="#{ModeloPlanoSucessoControle.filtro}"
                                     placeholder="Filtrar por: Empresa ou CS"
                                     style="height: 25px !important;">
                            <f:ajax event="keyup" listener="#{ModeloPlanoSucessoControle.filtrar()}"
                                    delay="#{ModeloPlanoSucessoControle.tempoDelay}" render=":fmLay:biPlanoSucesso"/>
                        </p:inputText>
                    </p:outputPanel>
                </div>
                <div style="width:50%">
                    <span style="text-align: right">Empresas que <b>não</b> possuem plano(s) de sucesso iniciado(s):
                        <span style=" font-size: 18px; font-weight: bold; display: inline-block; text-align: center;"
                              class="tooltipster"
                              title="Somente empresas do grupo: ACADEMIA ZILLYON WEB e ACADEMIA ZILLYON WEB - SP">#{ModeloPlanoSucessoControle.totalEmpresasSemPlanos}</span>
                    </span>
                </div>
            </div>


            <p:accordionPanel activeIndex="-1" id="empresasAltoEngajadas">
                <p:tab>
                    <f:facet name="title">
                        <div>
                            <h:outputText value="Quantidade de Clientes com Alto Engajamento:"
                                          title="Alto Engajamento: Quando as datas de conclusão dos planos de sucesso estão em dias e as ações do mesmo estão em dias"
                                          styleClass="tooltipster"/>
                            <span style="background: green; margin-left: 32px;"
                                  class="indicadorQtd">#{ModeloPlanoSucessoControle.total[2]}</span>
                        </div>
                    </f:facet>
                    <p:dataTable var="empresasEngajadas"
                                 value="#{ModeloPlanoSucessoControle.empresasPlanoSucessoAltoEngajamento}"
                                 rowStyleClass="alinhaCentro"
                                 paginator="true" rowIndexVar="indEmpresaEngajada"
                                 id="tabelaEngajada"
                                 rowsPerPageTemplate="20,50,100"
                                 rows="10" emptyMessage="Nenhum registro encontrado.">
                        <p:column style="width:16px">
                            <p:rowToggler/>
                        </p:column>
                        <p:ajax event="rowToggle" listener="#{ModeloPlanoSucessoControle.limparListaTes}"
                                update="@(.styleEngajado#{indEmpresaEngajada})"/>

                        <p:column headerText="Empresa" filterBy="#{empresasEngajadas.nomeFantasia}"
                                  filterMatchMode="contains">
                            <h:outputText
                                    value="#{empresasEngajadas.nomeFantasia}" style="display: table !important;"/>
                        </p:column>
                        <p:column headerText="CS" filterBy="#{empresasEngajadas.nomeResponsavelPacto}"
                                  filterMatchMode="contains">
                            <h:outputText
                                    value="#{empresasEngajadas.nomeResponsavelPacto}"/>
                        </p:column>
                        <p:column headerText="INÍCIO">
                            <h:outputText
                                    value="#{empresasEngajadas.dataInicioPlanoSucesso}"
                                    title="Data referente ao primeiro plano inciado" styleClass="tooltipster">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </p:column>
                        <p:column headerText="FIM">
                            <h:outputText
                                    value="#{empresasEngajadas.dataFimPlanoSucesso}"
                                    title="Data referente ao ultimo plano inciado" styleClass="tooltipster">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </p:column>

                        <p:rowExpansion>
                            <h:panelGroup>
                                <div style="width:100%; display: inline-flex;">
                                    <div style="width:50%; overflow-y: auto; max-height: 500px; text-transform: uppercase;">
                                        <ui:repeat value="#{empresasEngajadas.planosSucesso}"
                                                   var="planosEngajados" id="repeatEngajado#{indEmpresaEngajada}"
                                                   varStatus="indStatus">
                                            <div style="width:50%; float: left; display: inline-grid; "
                                                 id="divGraficoEngajado#{indEmpresaEngajada}">
                                                <script>montarGrafico('piechart#{planosEngajados.codigo}', '#{planosEngajados.porcentagemConcluida != null ? planosEngajados.porcentagemConcluida : 0}', '#{planosEngajados.corGrafico}')</script>
                                                <h:panelGroup id="panelEngajado#{indEmpresaEngajada}">
                                                    <div id="piechart#{planosEngajados.codigo}"
                                                         class="tooltipstertop chartdiv"
                                                         title="#{planosEngajados.modeloPlanoSucesso.descricao}">
                                                    </div>
                                                    <p:ajax listener="#{ModeloPlanoSucessoControle.atualizarAcoesView(planosEngajados)}"
                                                            event="click"
                                                            update="@(.styleEngajado#{indEmpresaEngajada})"/>
                                                </h:panelGroup>
                                                <p style="font-size: 10pt; font-weight: bold; color: #555; text-align: center"
                                                   class="textoGrafico">#{planosEngajados.modeloPlanoSucesso.nome}</p>
                                            </div>
                                        </ui:repeat>
                                    </div>
                                    <div style="width:50%;" id="divAcoesEngajado#{indEmpresaEngajada}">
                                        <p:dataTable rendered="true" var="acaoEngajadas"
                                                     id="tableAcaoEngajada#{indEmpresaEngajada}"
                                                     value="#{ModeloPlanoSucessoControle.tes}"
                                                     scrollable="true" scrollHeight="450"
                                                     styleClass="styleEngajado#{indEmpresaEngajada}"
                                                     rowStyleClass="alinhaCentro"
                                                     emptyMessage="Nenhum registro encontrado.">
                                            <p:column headerText="Nome">
                                                <h:outputText value="#{acaoEngajadas.modeloPlanoSucessoAcao.nome}"
                                                              style="text-transform: uppercase" styleClass="tooltipstertop" title="#{acaoEngajadas.modeloPlanoSucessoAcao.notas}"/>
                                            </p:column>
                                            <p:column headerText="Responsavel">
                                                <h:outputText value="#{acaoEngajadas.nomeResponsavel}"/>
                                            </p:column>
                                            <p:column headerText="Fim">
                                                <h:outputText value="#{acaoEngajadas.datafinal}">
                                                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                                                </h:outputText>
                                            </p:column>
                                            <p:column headerText="Status">
                                                <img src="#{request.contextPath}#{acaoEngajadas.situacaoCaminho}" class="tooltipster" title="#{acaoEngajadas.situacaoCaminhoTooltipster}"/>
                                            </p:column>
                                        </p:dataTable>
                                    </div>
                                </div>
                            </h:panelGroup>
                        </p:rowExpansion>
                    </p:dataTable>
                </p:tab>
            </p:accordionPanel>

            <p:accordionPanel activeIndex="-1" id="empresasMediaEngajadas">
                <p:tab>
                    <f:facet name="title">
                        <div>
                            <h:outputText value="Quantidade de Clientes com Médio Engajamento:"
                                          title="Médio Engajamento: Quando as datas de conclusão dos planos de sucesso estão em dias mas as ações do mesmo estão vencidas"
                                          styleClass="tooltipster"/>
                            <span style="background: #ffc349; margin-left: 17px;"
                                  class="indicadorQtd">#{ModeloPlanoSucessoControle.total[1]}</span>
                        </div>
                    </f:facet>
                    <p:dataTable var="empresasMedioEngajadas"
                                 value="#{ModeloPlanoSucessoControle.empresasPlanoSucessoMedioEngajamento}"
                                 rowStyleClass="alinhaCentro"
                                 paginator="true" rowIndexVar="indEmpresaMedio"
                                 id="tabelaMedioEngajada"
                                 rowsPerPageTemplate="20,50,100"
                                 rows="10" emptyMessage="Nenhum registro encontrado.">
                        <p:column style="width:16px">
                            <p:rowToggler/>
                        </p:column>
                        <p:ajax event="rowToggle" listener="#{ModeloPlanoSucessoControle.limparListaTes}"
                                update="@(.styleEngajado#{indEmpresaMedio})"/>
                        <p:column headerText="Empresa" filterBy="#{empresasMedioEngajadas.nomeFantasia}"
                                  filterMatchMode="contains">
                            <h:outputText
                                    value="#{empresasMedioEngajadas.nomeFantasia}" style="display: table !important;"/>
                        </p:column>
                        <p:column headerText="CS" filterBy="#{empresasMedioEngajadas.nomeResponsavelPacto}"
                                  filterMatchMode="contains">
                            <h:outputText
                                    value="#{empresasMedioEngajadas.nomeResponsavelPacto}"/>
                        </p:column>
                        <p:column headerText="INÍCIO">
                            <h:outputText
                                    value="#{empresasMedioEngajadas.dataInicioPlanoSucesso}"
                                    title="Data referente ao primeiro plano inciado" styleClass="tooltipster">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </p:column>
                        <p:column headerText="FIM">
                            <h:outputText
                                    value="#{empresasMedioEngajadas.dataFimPlanoSucesso}"
                                    title="Data referente ao ultimo plano inciado" styleClass="tooltipster">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </p:column>

                        <p:rowExpansion>
                            <h:panelGroup>
                                <div style="width:100%; display: inline-flex;">
                                    <div style="width:50%; overflow-y: auto; max-height: 500px; text-transform: uppercase;">
                                        <ui:repeat value="#{empresasMedioEngajadas.planosSucesso}"
                                                   var="planosMedioEngajados" id="repeatMedioEngajado#{indEmpresaMedio}"
                                                   varStatus="indStatus">
                                            <div style="width:50%; float: left; display: inline-grid; "
                                                 id="divGraficoMedioEngajado#{indEmpresaMedio}">
                                                <script>montarGrafico('piechart#{planosMedioEngajados.codigo}', '#{planosMedioEngajados.porcentagemConcluida != null ? planosMedioEngajados.porcentagemConcluida : 0}', '#{planosMedioEngajados.corGrafico}')</script>
                                                <h:panelGroup id="panelMedioEngajado#{indEmpresaMedio}">
                                                    <div id="piechart#{planosMedioEngajados.codigo}"
                                                         class="tooltipstertop chartdiv"
                                                         title="#{planosMedioEngajados.modeloPlanoSucesso.descricao}">
                                                    </div>
                                                    <p:ajax listener="#{ModeloPlanoSucessoControle.atualizarAcoesView(planosMedioEngajados)}"
                                                            event="click"
                                                            update="@(.styleMedioEngajado#{indEmpresaMedio})"/>
                                                </h:panelGroup>
                                                <p style="font-size: 10pt; font-weight: bold; color: #555; text-align: center"
                                                   class="textoGrafico">#{planosMedioEngajados.modeloPlanoSucesso.nome}</p>
                                            </div>
                                        </ui:repeat>
                                    </div>
                                    <div style="width:50%;" id="divAcoesMedioEngajado#{indEmpresaMedio}">
                                        <p:dataTable rendered="true" var="acaoMedioEngajadas"
                                                     id="tableAcaoMedioEngajada#{indEmpresaMedio}"
                                                     value="#{ModeloPlanoSucessoControle.tes}"
                                                     scrollable="true" scrollHeight="450"
                                                     styleClass="styleMedioEngajado#{indEmpresaMedio}"
                                                     rowStyleClass="alinhaCentro"
                                                     emptyMessage="Nenhum registro encontrado.">
                                            <p:column headerText="Nome">
                                                <h:outputText value="#{acaoMedioEngajadas.modeloPlanoSucessoAcao.nome}"
                                                              style="text-transform: uppercase"/>
                                            </p:column>
                                            <p:column headerText="Responsavel">
                                                <h:outputText value="#{acaoMedioEngajadas.nomeResponsavel}"/>
                                            </p:column>
                                            <p:column headerText="Fim">
                                                <h:outputText value="#{acaoMedioEngajadas.datafinal}">
                                                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                                                </h:outputText>
                                            </p:column>
                                            <p:column headerText="Status">
                                                <img src="#{request.contextPath}#{acaoMedioEngajadas.situacaoCaminho}" class="tooltipster" title="#{acaoMedioEngajadas.situacaoCaminhoTooltipster}"/>
                                            </p:column>
                                        </p:dataTable>
                                    </div>
                                </div>
                            </h:panelGroup>
                        </p:rowExpansion>
                    </p:dataTable>
                </p:tab>
            </p:accordionPanel>

            <p:accordionPanel activeIndex="-1" id="empresasMenosEngajadas">
                <p:tab>
                    <f:facet name="title">
                        <div>
                            <h:outputText value="Quantidade de Clientes com Baixo Engajamento:"
                                          title="Baixo Engajamento: Quando as datas de conclusão dos planos de sucesso estão vencidas e as ações do mesmo estão vencidas"
                                          styleClass="tooltipster"/>
                            <span style="background: red; margin-left: 22px;"
                                  class="indicadorQtd">#{ModeloPlanoSucessoControle.total[0]}</span>
                        </div>
                    </f:facet>
                    <p:dataTable var="empresasNaoEngajadas"
                                 value="#{ModeloPlanoSucessoControle.empresasPlanoSucessoNaoEngajado}"
                                 rowStyleClass="alinhaCentro"
                                 paginator="true" rowIndexVar="indEmpre"
                                 id="tabelaNaoEngajada"
                                 rowsPerPageTemplate="20,50,100"
                                 rows="10" emptyMessage="Nenhum registro encontrado.">
                        <p:column style="width:16px">
                            <p:rowToggler/>
                        </p:column>
                        <p:ajax event="rowToggle" listener="#{ModeloPlanoSucessoControle.limparListaTes}"
                                update="@(.styleEngajado#{indEmpre})"/>
                        <p:column headerText="Empresa" filterBy="#{empresasNaoEngajadas.nomeFantasia}"
                                  filterMatchMode="contains">
                            <h:outputText
                                    value="#{empresasNaoEngajadas.nomeFantasia}" style="display: table !important;"/>
                        </p:column>
                        <p:column headerText="CS" filterBy="#{empresasNaoEngajadas.nomeResponsavelPacto}"
                                  filterMatchMode="contains">
                            <h:outputText
                                    value="#{empresasNaoEngajadas.nomeResponsavelPacto}"/>
                        </p:column>
                        <p:column headerText="INÍCIO">
                            <h:outputText
                                    value="#{empresasNaoEngajadas.dataInicioPlanoSucesso}"
                                    title="Data referente ao primeiro plano inciado" styleClass="tooltipster">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </p:column>
                        <p:column headerText="FIM">
                            <h:outputText
                                    value="#{empresasNaoEngajadas.dataFimPlanoSucesso}"
                                    title="Data referente ao ultimo plano inciado" styleClass="tooltipster">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </p:column>

                        <p:rowExpansion>
                            <h:panelGroup>
                                <div style="width:100%; display: inline-flex;">
                                    <div style="width:50%; overflow-y: auto; max-height: 500px; text-transform: uppercase;">
                                        <ui:repeat value="#{empresasNaoEngajadas.planosSucesso}"
                                                   var="planosNaoEngajados" id="repeatMenosEngajado#{indEmpre}"
                                                   varStatus="indStatus">
                                            <div style="width:50%; float: left; display: inline-grid; "
                                                 id="divGraficoMenosEngajado#{indEmpre}">
                                                <script>montarGrafico('piechart#{planosNaoEngajados.codigo}', '#{planosNaoEngajados.porcentagemConcluida != null ? planosNaoEngajados.porcentagemConcluida : 0}', '#{planosNaoEngajados.corGrafico}')</script>
                                                <h:panelGroup id="panelMenosEngajado#{indEmpre}">
                                                    <div id="piechart#{planosNaoEngajados.codigo}"
                                                         class="tooltipstertop chartdiv"
                                                         title="#{planosNaoEngajados.modeloPlanoSucesso.descricao}">
                                                    </div>
                                                    <p:ajax listener="#{ModeloPlanoSucessoControle.atualizarAcoesView(planosNaoEngajados)}"
                                                            event="click" update="@(.styleMenosEngajado#{indEmpre})"/>
                                                </h:panelGroup>
                                                <p style="font-size: 10pt; font-weight: bold; color: #555; text-align: center"
                                                   class="textoGrafico">#{planosNaoEngajados.modeloPlanoSucesso.nome}</p>
                                            </div>
                                        </ui:repeat>
                                    </div>
                                    <div style="width:50%;" id="divAcoesMenosEngajado#{indEmpre}">
                                        <p:dataTable rendered="true" var="acaoNaoEngajadas"
                                                     id="tableAcaoNaoEngajada#{indEmpre}"
                                                     value="#{ModeloPlanoSucessoControle.tes}"
                                                     scrollable="true" scrollHeight="450"
                                                     styleClass="styleMenosEngajado#{indEmpre}"
                                                     rowStyleClass="alinhaCentro"
                                                     emptyMessage="Nenhum registro encontrado.">
                                            <p:column headerText="Nome">
                                                <h:outputText value="#{acaoNaoEngajadas.modeloPlanoSucessoAcao.nome}"
                                                              style="text-transform: uppercase"/>
                                            </p:column>
                                            <p:column headerText="Responsavel">
                                                <h:outputText value="#{acaoNaoEngajadas.nomeResponsavel}"/>
                                            </p:column>
                                            <p:column headerText="Fim">
                                                <h:outputText value="#{acaoNaoEngajadas.datafinal}">
                                                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                                                </h:outputText>
                                            </p:column>
                                            <p:column headerText="Status">
                                                <img src="#{request.contextPath}#{acaoNaoEngajadas.situacaoCaminho}" class="tooltipster" title="#{acaoNaoEngajadas.situacaoCaminhoTooltipster}"/>
                                            </p:column>
                                        </p:dataTable>
                                    </div>
                                </div>
                            </h:panelGroup>
                        </p:rowExpansion>
                    </p:dataTable>
                </p:tab>
            </p:accordionPanel>
        </h:panelGroup>
    </ui:define>
</ui:decorate>
</html>
