<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui">


<p:dialog widgetVar="modalConfirmacaoExclusaoPlano"
          id="modalConfirmacaoExclusaoPlano"
          header="Exclusão de plano de sucesso"
          modal="true"
          width="400">
        <div style="text-align: center;margin-top: 40px;">
            <h:outputText value="Você confirma a exclusão do plano #{ModeloPlanoSucessoControle.modeloPlanoSucesso.nome} ?"/>
            <br/>
            <h:outputText value="Esta operação não pode ser desfeita" />
        </div>
        <div style="display: flex; justify-content: space-between; margin-top: 40px;">
            <p:commandButton value="Excluir"
                             style="background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ee5f5b), to(#bd362f)) !important;"
                             styleClass="btn btn-danger"
                             action="#{ModeloPlanoSucessoControle.excluirModeloPlanoSucesso}"
                             oncomplete="PF('modalConfirmacaoExclusaoPlano').hide();" />
            <p:commandButton value="Cancelar"
                             styleClass="btn btn-secundary"
                             oncomplete="PF('modalConfirmacaoExclusaoPlano').hide();" />
        </div>
</p:dialog>
</html>