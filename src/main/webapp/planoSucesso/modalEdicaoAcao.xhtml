<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:f="http://java.sun.com/jsf/core">

<p:dialog widgetVar="modalEdicaoAcao"
          id="modalEdicaoAcao"
          header="Alterando uma ação ao plano"
          modal="true"
          width="800">
    <h:panelGroup styleClass="span9 offset3 caixaPrincipal"
                  style="padding: 20px; width: 90%; margin-left: 3%;">
        <div>
            <label>Nome da ação:</label>
            <h:inputText value="#{ModeloPlanoSucessoControle.modeloPlanoSucessoAcaoEditar.nome}" style="width: 100%;"/>
        </div>
        <div>
            <label>Ordem ação:</label>
            <h:inputText type="number"
                         value="#{ModeloPlanoSucessoControle.modeloPlanoSucessoAcaoEditar.ordem}" style="width: 20%;"
                         onkeypress="if(event.which &lt; 48 || event.which &gt; 57) return false;" maxlength="2"/>
        </div>

        <div>
            <label>Duração</label>
            <h:panelGrid columns="2" styleClass="borderless no-padding">
                <h:inputText type="number"
                             value="#{ModeloPlanoSucessoControle.modeloPlanoSucessoAcaoEditar.duracao}"
                             onkeypress="if(event.which &lt; 48 || event.which &gt; 57) return false;" maxlength="2"/>
                <p:selectOneMenu value="#{ModeloPlanoSucessoControle.modeloPlanoSucessoAcaoEditar.strDuracao}" style="margin-bottom: 6px; margin-left: 8px">
                    <f:selectItem itemLabel="Selecione " itemValue=""/>
                    <f:selectItems value="#{ModeloPlanoSucessoControle.tipoDuracao}" var="duracao" itemLabel="#{duracao.desc}" itemValue="#{duracao}"/>
                </p:selectOneMenu>
            </h:panelGrid>
        </div>

        <div>
            <label>Opções</label>
            <h:panelGroup layout="block" style="justify-content: left">
                <p:selectBooleanCheckbox  value="#{ModeloPlanoSucessoControle.modeloPlanoSucessoAcaoEditar.acaoMarco}"/>
                <h:outputText value="Ação marco"
                              style="margin-left: 8px;"
                              title="É uma ação com relevância superior ao normal para andamento do plano de sucesso"/>
            </h:panelGroup>
        </div>

        <div style="margin-top: 8px">
            <label>Descrição da ação:</label>
            <h:inputTextarea rows="10"
                             value="#{ModeloPlanoSucessoControle.modeloPlanoSucessoAcaoEditar.notas}"
                             style="width: 100%;"/>
        </div>
        <p:commandButton value="Alterar"
                         style="background-image: none !important; background-color: #0044cc !important;"
                         styleClass="btn btn-primary"
                         update="tblCadastroPlanoSucesso"
                         oncomplete="PF('modalEdicaoAcao').hide();"/>

        <p:commandButton value="Excluir"
                         style="background-image: none !important; background-color: #da4f49 !important;"
                         styleClass="btn btn-danger"
                         update="tblCadastroPlanoSucesso"
                         action="#{ModeloPlanoSucessoControle.excluirAcao}"
                         oncomplete="PF('modalEdicaoAcao').hide();"/>
    </h:panelGroup>
</p:dialog>
</html>
