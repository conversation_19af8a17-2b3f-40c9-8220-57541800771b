<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui">

<p:dialog widgetVar="modalCadastroMaterialApoio"
          id="modalCadastroMaterialApoio"
          header="Adicionando materiais de apoio a ação"
          modal="true"
          width="800">
    <h:panelGroup styleClass="span9 offset3 caixaPrincipal"
                  style="padding: 20px; width: 90%; margin-left: 3%;">
        <h:panelGroup id="formCadastroMaterialApoio">
            <div>
                <label>Titulo:</label>
                <h:inputText value="#{ModeloPlanoSucessoControle.materialApoio.titulo}" style="width: 100%;"/>
            </div>
            <div>
                <label>Descrição:</label>
                <h:inputTextarea rows="3"
                                 value="#{ModeloPlanoSucessoControle.materialApoio.descricao}"
                                 style="width: 100%;"/>
            </div>
            <div>
                <label>Url da página na UCP:</label>
                <h:inputText value="#{ModeloPlanoSucessoControle.materialApoio.paginaUrl}" style="width: 100%;"/>
            </div>
            <div>
                <label>Url do video:</label>
                <h:inputText value="#{ModeloPlanoSucessoControle.materialApoio.videoUrl}" style="width: 100%;"/>
            </div>
        </h:panelGroup>

        <p:commandButton value="Adicionar"
                         style="background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc)) !important;"
                         styleClass="btn btn-primary"
                         update="tabelaMaterialApoio formCadastroMaterialApoio"
                         action="#{ModeloPlanoSucessoControle.adicionarMaterialApoio}" />


        <p:dataTable id="tabelaMaterialApoio" value="#{ModeloPlanoSucessoControle.planoSucessoAcaoMaterialApoio.materiaisApoio}" var="materialApoio" >
            <p:column>
                <f:facet name="header">
                    <h:outputText value="Titulo"/>
                </f:facet>
                <h:outputText value="#{materialApoio.titulo}" />
            </p:column>
            <p:column>
                <f:facet name="header">
                    <h:outputText value="Descrição"/>
                </f:facet>
                <h:outputText value="#{materialApoio.descricao}" />
            </p:column>
            <p:column>
                <f:facet name="header">
                    <h:outputText value="Video"/>
                </f:facet>
                <h:commandLink target="_blank" value="#{materialApoio.videoUrl}"/>
            </p:column>
            <p:column>
                <f:facet name="header">
                    <h:outputText value="Video"/>
                </f:facet>
                <h:commandLink target="_blank" value="#{materialApoio.paginaUrl}"/>
            </p:column>
            <p:column>
                <f:facet name="header">

                </f:facet>
                <p:commandLink action="#{ModeloPlanoSucessoControle.editarMaterialApoio(materialApoio)}"
                               update=":fmLay:formCadastroMaterialApoio"
                               title="Editar">
                    <i class="fa-icon-ok-circle" style="font-size: 18px"></i>
                </p:commandLink>
                <p:commandLink action="#{ModeloPlanoSucessoControle.excluirMaterialApoio(materialApoio)}"
                               update="tabelaMaterialApoio"
                               title="Excluir">
                    <i class="fa-icon-remove-circle" style="font-size: 18px"></i>
                </p:commandLink>
            </p:column>
        </p:dataTable>

        <div style="display: flex; justify-content: center; margin-top: 20px;">
            <p:commandButton value="Fechar"
                             style="background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc)) !important;"
                             onclick="PF('modalCadastroMaterialApoio').hide();"
                             styleClass="btn btn-primary"
                             update="tblCadastroPlanoSucesso" />
        </div>
    </h:panelGroup>
</p:dialog>
</html>
