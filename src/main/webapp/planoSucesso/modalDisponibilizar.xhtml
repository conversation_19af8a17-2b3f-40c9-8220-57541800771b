<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:f="http://xmlns.jcp.org/jsf/core">
<p:dialog widgetVar="modalCadastroEmpresas"
          id="modalCadastroEmpresas"
          header="Disponibilizar plano de sucesso"
          modal="true"
          width="800">

    <h:panelGroup layout="block" id="panelEmpresas">
        <div style="display: flex; flex-direction: row;">
            <p:selectBooleanCheckbox value="#{ModeloPlanoSucessoControle.modeloPlanoSucesso.todasEmpresas}">
                <p:ajax event="change" update="panelEmpresas" />
            </p:selectBooleanCheckbox>
            <p:outputLabel style="margin-left: 10px;" value="Disponibilizar para todas as empresas" />
        </div>
        <h:panelGroup layout="block"
                      rendered="#{!ModeloPlanoSucessoControle.modeloPlanoSucesso.todasEmpresas}"
                      style="margin-top: 40px;">
            <p:outputLabel value="Selecione as empresasSelecionadas" />
            <p:autoComplete id="empresasSelecionadas"
                            styleClass="full-width"
                            panelStyleClass="full-width"
                            multiple="true"
                            scrollHeight="300"
                            value="#{ModeloPlanoSucessoControle.empresasSelecionadas}"
                            completeMethod="#{ModeloPlanoSucessoControle.consultarEmpresas}"
                            var="empresa"
                            converter="empresaFinanceiroConverter"
                            minQueryLength="3"
                            itemLabel="#{empresa.nomeResumo}"
                            itemValue="#{empresa}"
                            forceSelection="true">
                <p:column>
                    <h:outputText value="#{empresa.nomeResumo}" />
                </p:column>

                <p:ajax event="itemSelect" listener="#{ModeloPlanoSucessoControle.adicionarEmpresa(empresa)}" process="@form"/>
            </p:autoComplete>
            <p:dataTable value="#{ModeloPlanoSucessoControle.empresasSelecionadas}" var="empresa" >
                <p:column>
                    <f:facet name="header">
                        <h:outputText value="Empresa"/>
                    </f:facet>
                    <h:outputText value="#{empresa.nomeFantasia}" />
                </p:column>
            </p:dataTable>
        </h:panelGroup>
        <div style="display: flex; justify-content: center; margin-top: 30px;">
            <p:commandButton value="Disponibilizar"
                             id="disponibilizarPlano"
                             style="background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#62c462), to(#51a351)) !important; color: #ffffff;"
                             styleClass="btn btn-success"
                             action="#{ModeloPlanoSucessoControle.atualizarPlano()}"
                             oncomplete="PF('modalCadastroEmpresas').hide();"/>
        </div>
    </h:panelGroup>
</p:dialog>
</html>
