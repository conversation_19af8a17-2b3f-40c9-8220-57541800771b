<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
<f:metadata>
    <f:event type="preRenderView" listener="#{SuperControl.validaAdmin}"/>
    <f:event type="preRenderView" listener="#{MoviDeskControle.verificaAlertaZW}"/>
</f:metadata>
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="conteudo">
        <script src="#{request.contextPath}/resources/js/amcharts/amcharts.js"></script>
        <script src="#{request.contextPath}/resources/js/amcharts/pie.js"></script>
        <script src="#{request.contextPath}/fontawesome/js/all.min.js"></script>
        <link type="text/css" href="#{request.contextPath}/resources/js/amcharts/plugins/export/export.css"
              rel="stylesheet"/>
        <link type="text/css" href="#{request.contextPath}/fontawesome/css/all.min.css"
              rel="stylesheet"/>
        <h:panelGroup styleClass="span12 offset3 caixaPrincipal" id="movideskPanel2"
                      style="padding: 5px; width: 100%; margin-left: 0px;">

            <div style="width: 99%; border: solid 1px #DDD; padding: 5px; border-radius: 5px; font-family: Arial;"
                 class="floatRight">
                <h4 style="margin-left: auto; float: left;">ENVIO DE NOTIFICAÇÕES MOVIDESK</h4>
                <p:commandLink style="float: right; color: #{MoviDeskControle.corStatus}"
                               title="#{MoviDeskControle.celularConectado}" onclick="PF('qrcode').show();">
                    <i class="fas fa-mobile-alt fa-2x"></i>
                </p:commandLink>
            </div>

            <h:panelGroup id="movideskPanel"
                          style="width: 100%; margin-left: 0px;">

                <div style="width: 99%; border: solid 1px #DDD; border-radius: 5px; padding: 5px; font-family: Arial;"
                     class="floatRight font9">

                    <p:selectBooleanCheckbox value="#{MoviDeskControle.marcarAlertaZW}"
                                             itemLabel="Ativar aviso na tela de login ZW" styleClass="floatLeft font10"
                                             style="font-family: Arial;">
                        <p:ajax update="listaTickets" listener="#{MoviDeskControle.ativarAlertaZW}"/>
                    </p:selectBooleanCheckbox>


                    <p:commandButton action="#{MoviDeskControle.atualizarLista}" value="Atualizar Lista" ajax="true"
                                     update="listaTickets"
                                     styleClass="btn btn-primary btn-small floatRight"/>
                    <p:calendar id="fim" value="#{MoviDeskControle.fim}" pattern="dd/MM/yyyy HH:mm" mask="true"
                                showButtonPanel="true" placeholder="Data Final" navigator="true"
                                styleClass="floatRight"/>
                    <p:calendar id="inicio" value="#{MoviDeskControle.inicio}" pattern="dd/MM/yyyy HH:mm" mask="true"
                                showButtonPanel="true" placeholder="Data Inicial" navigator="true"
                                styleClass="floatRight"/>
                    <br/><br/>
                    <p:commandButton action="#{MoviDeskControle.enviarMensagens}" value="Enviar Mensagens" ajax="true"
                                     update="listaTickets"
                                     styleClass="btn btn-primary btn-small floatRight"/>
                    <p:selectBooleanCheckbox value="#{MoviDeskControle.enviarSms}" itemLabel="SMS"
                                             styleClass="floatRight"
                                             style="font-family: Arial; margin-left: 5px;"/>
                    <p:selectBooleanCheckbox value="#{MoviDeskControle.enviarChat}" itemLabel="WhatsApp"
                                             styleClass="floatRight" style="font-family: Arial; margin-left: 10px;"/>
                    <p:selectBooleanCheckbox value="#{MoviDeskControle.fecharTickets}" itemLabel="Fechar Ticksts"
                                             rendered="false"
                                             styleClass="floatRight"
                                             style="font-family: Arial; margin-left: 10px; margin-right: 10px;"/>
                    <p:inputTextarea value="#{MoviDeskControle.mensagem}" rows="1" autoResize="true"
                                     style="min-width: 300px; max-width: 500px; margin-left: 10px;" id="mensagemEnviar"
                                     styleClass="floatRight"/>
                    <p:selectOneMenu id="console" value="#{MoviDeskControle.mensagemPronta}"
                                     style="max-width:300px; min-width:200px;" styleClass="floatRight">
                        <f:selectItem itemLabel="Escolha uma mensagem" itemValue=""/>
                        <f:selectItem itemLabel="Informar Problema" itemValue="1"/>
                        <f:selectItem itemLabel="Informar Solução" itemValue="2"/>
                        <p:ajax update="mensagemEnviar" listener="#{MoviDeskControle.setaMensagem}"/>
                    </p:selectOneMenu>
                </div>
                <p:dataTable id="listaTickets" value="#{MoviDeskControle.lista}" var="ticket" rows="10"
                             emptyMessage="Nenhum ticket encontrado."
                             styleClass="font8" style="font-family: Arial;"
                             paginator="true"
                             paginatorPosition="bottom"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             currentPageReportTemplate="Mostrando de {startRecord} a {endRecord} de {totalRecords} tickets"
                             rowsPerPageTemplate="10,20,30,50,100,500,1000">
                    <p:column headerText="Nº" sortBy="#{ticket.id}">
                        <h:outputText value="#{ticket.id}"/>
                    </p:column>
                    <p:column headerText="Status" sortBy="#{ticket.status}">
                        <h:outputText value="#{ticket.status}"/>
                    </p:column>
                    <p:column headerText="Assunto" sortBy="#{ticket.subject}">
                        <h:outputText value="#{ticket.subject}"/>
                    </p:column>
                    <p:column headerText="Cliente" sortBy="#{ticket.createdBy.businessName}">
                        <h:outputText value="#{ticket.createdBy.businessName}"/>
                    </p:column>
                    <p:column headerText="Telefone" sortBy="#{ticket.createdBy.phone}">
                        <h:outputText value="#{ticket.createdBy.phone}"/>
                    </p:column>
                    <p:column headerText="Problema" sortBy="#{ticket.serviceFirstLevel}">
                        <h:outputText value="#{ticket.serviceFirstLevel} (#{ticket.serviceSecondLevel})"/>
                    </p:column>
                    <p:column headerText="Atend." sortBy="#{ticket.owner.businessName}">
                        <h:outputText value="#{ticket.owner.businessNameFmt}"/>
                    </p:column>
                    <p:column headerText="Início" sortBy="#{ticket.createdDate}">
                        <h:outputText value="#{ticket.createdDate}"/>
                    </p:column>
                    <p:column headerText="Fim" sortBy="#{ticket.resolvedIn}">
                        <h:outputText value="#{ticket.resolvedIn}"/>
                    </p:column>
                    <p:column>
                        <f:facet name="header">
                            <p:selectBooleanCheckbox value="#{MoviDeskControle.marcarTodos}">
                                <p:ajax update="listaTickets" listener="#{MoviDeskControle.marcarTodostickets}"/>
                            </p:selectBooleanCheckbox>
                        </f:facet>
                        <p:selectBooleanCheckbox value="#{ticket.enviarMensagem}"
                                                 rendered="#{ticket.permiteEnviar}"></p:selectBooleanCheckbox>
                    </p:column>
                    <p:column>
                        <p:commandLink rendered="#{ticket.foiEnviadomensagens}"
                                       title="#{ticket.todasMensagensEnviadas}">
                            <i class="fas fa-envelope fa-2x"></i>
                        </p:commandLink>

                    </p:column>
                </p:dataTable>
                <p:dialog header="QR CODE WHATSAPP" widgetVar="qrcode" modal="true" closable="true"
                          height="500" width="500" style="text-align: center;">
                    <p:ajax event="close" update="movideskPanel2"/>

                    <p:outputLabel value="Faça a leitura do QR CODE com o WhatsApp do seu celular!"
                                   rendered="#{!MoviDeskControle.celularConectadoAPI and empty MoviDeskControle.statusLoading}"/>
                    <p:outputLabel value="Já existe um celular conectado nessa API!"
                                   rendered="#{MoviDeskControle.celularConectadoAPI}"/>

                    <p:outputLabel value="#{MoviDeskControle.statusLoading}" />

                    <p:commandLink rendered="#{!MoviDeskControle.statusLoadingDesconectar}"
                                   action="#{MoviDeskControle.logoutChat}" value="Desconectar celular" ajax="false"
                                   update="movideskPanel2"
                                   styleClass="btn btn-danger btn-small"/>

                    <p:graphicImage value="#{MoviDeskControle.urlQrCode}"/>
                </p:dialog>
            </h:panelGroup>
        </h:panelGroup>
    </ui:define>
</ui:decorate>
</html>
