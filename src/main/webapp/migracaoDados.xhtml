<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui">
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="CSS">
        <style>
            @media (max-width: 1400px){
                .container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
                    width: 100% !important;
                }
            }

            .ui-selectonemenu input {
                margin-bottom: 0px !important;
            }

            .ui-panelgrid td {
                border-width: 0px !important;
            }
            .ui-widget-content {
                border: 0px !important;
            }
            .width-90-perc {
                width: 90%;
            }

            .width-25-perc {
                width: 25%;
            }

            .ui-selectonemenu{
                width: 95% !important;
            }
            .ui-selectonemenu-label{
                width: 95% !important;
            }

            .ui-datatable-odd {
                background: none repeat scroll 0 0 #F2F5F9;
            }

        </style>
    </ui:define>
    <ui:define name="conteudo">
        <h:panelGroup layout="block" id="dados" style="width: 100%; margin-left: auto; margin-right: auto;"
                      styleClass="span12 caixaPrincipal">
            <h4>Migração de Dados</h4>
            <hr />
            <h:panelGroup layout="block" style="margin-left: 20px;">
                <p:commandLink styleClass="btn btn-primary"
                               oncomplete="modalConfiguracaoMigracao.show();"
                               update="detalhesConfiguracaoImportacao"
                               ajax="true">
                    <i class="fa-icon-cog"/> Configuração
                </p:commandLink>
                <p:commandLink styleClass="btn btn-primary"
                               action="#{MigracaoControle.abrirModalMigracao('IMPORTACAO')}"
                               oncomplete="modalMigracaoRegistro.show();"
                               update="migracaoRegistroDialog"
                               ajax="true" >
                    <i class="fa-icon-cloud-download"/> Importar OAMD
                </p:commandLink>
                <p:commandLink styleClass="btn btn-primary"
                               action="#{MigracaoControle.abrirModalMigracao('EXPORTACAO')}"
                               oncomplete="modalMigracaoRegistro.show();"
                               update="migracaoRegistroDialog"
                               ajax="true" >
                    <i class="fa-icon-upload"/> Exportar ZW
                </p:commandLink>
            </h:panelGroup>
            <hr/>
            <h:panelGroup id="painelRegistros" layout="block" style="margin: 1em;">
                <h:panelGroup id="painelFiltros" layout="block" style="width: 100%; margin-left: auto; margin-right: auto; padding-bottom: 1em;" styleClass="span12">
                    <p:panelGrid columns="5" layout="grid" style="width: 100%" columnClasses="width-20-perc">
                        <h:outputText value="MD5:" />
                        <h:outputText value="Status:" />
                        <h:outputText value="Tipo:" />
                        <h:outputText value="Codigos Externos:" />
                        <h:outputText value=""/>

                        <h:inputText value="#{MigracaoControle.filtroRegistroMigracao.md5}" style="width: 88%; margin-bottom: 2px;"/>
                        <p:selectOneMenu value="#{MigracaoControle.filtroRegistroMigracao.statusRegistroMigracao}">
                            <f:selectItem itemLabel="Selecione " itemValue=""/>
                            <f:selectItems value="#{MigracaoControle.statusRegistroMigracao}" />
                        </p:selectOneMenu>
                        <p:selectOneMenu value="#{MigracaoControle.filtroRegistroMigracao.tipoRegistroMigracao}">
                            <f:selectItem itemLabel="Selecione " itemValue=""/>
                            <f:selectItems value="#{MigracaoControle.tiposRegistroMigracao}" />
                        </p:selectOneMenu>
                        <h:panelGroup>
                        <h:inputText value="#{MigracaoControle.filtroCodigosExternos}" style="width: 88%; margin-bottom: 2px;"/>
                        <p:commandLink style="width: 88%; margin-bottom: 2px;" partialSubmit="true" 
                                       title="Limpar filtro codigos externos"
                                       action="#{MigracaoControle.limparCodigosExternos()}" update="painelRegistros">
                            <i class="fa-icon-remove"/>
                        </p:commandLink>
                        </h:panelGroup>
                        <p:commandLink id="btnPesquisar" styleClass="btn btn-default"
                                       process="painelRegistros"
                                       update="painelRegistros"
                                       ajax="true">
                            <i class="fa-icon-search"/> Pesquisar
                        </p:commandLink>
                    </p:panelGrid>
                </h:panelGroup>
                <p:dataTable var="registro"
                             value="#{MigracaoControle.modeloRegistroMigracao}"
                             paginator="true"
                             rows="20"
                             rowKey="#{registro.codigo}"
                             emptyMessage="#{msg['tabela.semregistros']}"
                             lazy="true"
                             paginatorPosition="bottom"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             currentPageReportTemplate="{startRecord} - {endRecord} of {totalRecords}"
                             rowsPerPageTemplate="20,50,100"
                             selection="#{MigracaoControle.selectedRegistros}">
                    <f:facet name="header">
                        Registros de migração
                    </f:facet>
                    <p:column selectionMode="multiple" />
                    <p:column headerText="MD5">
                        <h:outputText value="#{registro.md5}"/>
                    </p:column>
                    <p:column headerText="Tipo">
                        <h:outputText value="#{registro.tipo.nome}"/>
                    </p:column>
                    <p:column headerText="Status">
                        <h:outputText value="#{registro.status.nome}"/>
                    </p:column>
                      <p:column headerText="Chave Estrangeira">
                          <h:outputText value="#{registro.chaveEstrangeira}"/>
                    </p:column>
                    <p:column headerText="Codigo Externo">
                        <h:outputText value="#{registro.codigoExterno}"/>
                    </p:column>
                    <p:column headerText="Codigo ZW">
                        <h:outputText value="#{registro.codigoInterno}"/>
                    </p:column>
                    <p:column headerText="Data importação">
                        <h:outputText value="#{registro.dataImportacaoApresentacao}"/>
                    </p:column>
                    <p:column headerText="Opções">
                        <p:commandLink oncomplete="modalDetalheRegistroMigracao.show();"
                                       partialSubmit="true"
                                       update=":fmLay:detalhesRegistroMigracao"
                                       action="#{MigracaoControle.detalharRegistroMigracao(registro)}"
                                       title="Visualizar registro">
                            <i class="fa-icon-search"/>
                        </p:commandLink>
                        <p:commandLink  rendered="#{registro.apresentarBotaoExportacao}"
                                       title="Exportar registro" update=":fmLay" 
                                       action="#{MigracaoControle.exportarRegistroMigracao(registro)}">
                            <i class="icon-circle-arrow-right"/>
                        </p:commandLink>
                         <p:commandLink  rendered="#{registro.apresentarBotaoExcluir}"
                                       title="Excluir registro ZW" update=":fmLay" 
                                       action="#{MigracaoControle.excluirRegistroMigracao(registroMigracao)}">
                            <i class="icon-ban-circle"/>
                        </p:commandLink>
                    </p:column>
                </p:dataTable>
                
                
            </h:panelGroup>
           
            <h:panelGroup layout="block" style="text-align: right;margin-right: 20px;">
                
                <p:commandLink styleClass="btn btn-primary"
                               action="#{MigracaoControle.abrirModalMigracao('IMPORTACAO')}"
                               oncomplete="modalMigracaoRegistro.show();"
                               update="migracaoRegistroDialog"
                               ajax="true" >
                    <i class="fa-icon-cloud-download"/> Deletar selecionados ZW
                </p:commandLink>
                <p:commandLink styleClass="btn btn-primary"
                               action="#{MigracaoControle.abrirModalMigracao('EXPORTACAO')}"
                               oncomplete="modalMigracaoRegistro.show();"
                               update="migracaoRegistroDialog"
                               ajax="true" >
                    <i class="fa-icon-upload"/> Exportar selecionados ZW
                </p:commandLink>
            </h:panelGroup>
            <hr/>
        </h:panelGroup>
        

        <ui:include src="./template/includes/configuracaoImportacao.xhtml"/>
        <ui:include src="./template/includes/migracaoRegistro.xhtml"/>
        <ui:include src="./template/includes/detalheRegistroMigracao.xhtml"/>
    </ui:define>
</ui:decorate>
</html>
