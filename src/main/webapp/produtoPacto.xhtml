<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="conteudo">
        <style>
            @media (max-width: 1400px) {
                .container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
                    width: 100% !important;
                }
            }

            @media (min-width: 1200px) {
                .container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
                    width: 80%;
                }
            }
        </style>

        <div style="padding: 0;">
            <div class="bread span12"></div>

            <h:panelGroup styleClass="span10 caixaPrincipal" id="dados"
                          layout="block"
                          style="width: 100%; padding: 0px; margin: 0px;">
                <h4>Produtos cadastrados</h4>
                <hr/>
                <p:outputPanel layout="block" styleClass="pesq pesq-tabelas buscaAuxiliar" id="filtrar">
                    <p:inputText id="globalFilter" value="#{ProdutoPactoControle.filtro}">
                        <f:ajax event="keyup"
                                listener="#{ProdutoPactoControle.filtrarProdutos}"
                                delay="#{ProdutoPactoControle.tempoDelay}"
                                render=":fmLay:lista"/>
                    </p:inputText>
                    <p:watermark for="globalFilter" value="#{title['cadastros.filtrar']}"/>
                </p:outputPanel>

                <p:dataTable var="cupom" value="#{ProdutoPactoControle.listaProdutosTela}" id="lista"
                             widgetVar="tabelaAtual"
                             emptyMessage="#{msg['tabela.semregistros']}"
                             style="margin: 0"
                             styleClass="tabelaPrincipal">

                    <f:facet name="header">
                        <h:panelGroup layout="block" id="totalTabela">
                            <h:outputText value="#{fn:length(ProdutoPactoControle.listaProdutosTela)} produtos"/>
                            <p:commandLink styleClass="btn btn-primary"
                                           style="float: right"
                                           action="#{ProdutoPactoControle.novoProduto}">
                                <i class="fa-icon-plus-sign-alt"/> Novo
                            </p:commandLink>
                        </h:panelGroup>
                    </f:facet>

                    <p:column headerText="ID" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoControle.editar(cupom)}"
                        >#{cupom.id}</p:commandLink>
                    </p:column>

                    <p:column headerText="Nome" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoControle.editar(cupom)}"
                        >#{cupom.nome}</p:commandLink>
                    </p:column>

                    <p:column headerText="Valor / Adesão" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoControle.editar(cupom)}"
                        >#{cupom.adesaoApresentar}</p:commandLink>
                    </p:column>

                    <p:column headerText="Mensalidade" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoControle.editar(cupom)}"
                        >#{cupom.mensalidadeApresentar}</p:commandLink>
                    </p:column>

                    <p:column headerText="Max. Parcelas" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoControle.editar(cupom)}"
                        >#{cupom.maximoParcelas}</p:commandLink>
                    </p:column>

                    <p:column headerText="Qtd. Fixa" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoControle.editar(cupom)}"
                        >#{cupom.qtdFixa ? 'SIM' : 'NÃO'}</p:commandLink>
                    </p:column>

                    <p:column headerText="Qtd" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoControle.editar(cupom)}"
                        >#{cupom.qtd}</p:commandLink>
                    </p:column>

                    <p:column headerText="Tipo de Produto" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoControle.editar(cupom)}"
                        >#{cupom.tipo.descricao}</p:commandLink>
                    </p:column>

                    <p:column headerText="Proprietário" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoControle.editar(cupom)}"
                        >#{cupom.proprietario}</p:commandLink>
                    </p:column>

                    <p:column headerText="Ativo" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoControle.editar(cupom)}"
                        >#{cupom.ativoApresentar}</p:commandLink>
                    </p:column>
                </p:dataTable>
            </h:panelGroup>
        </div>
    </ui:define>
    <ui:define name="JS">
        <h:outputScript library="js" name="cadastrosV2.js"/>
    </ui:define>
</ui:decorate>
</html>
