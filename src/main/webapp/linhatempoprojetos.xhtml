<!DOCTYPE html>
<html lang="en"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:p="http://primefaces.org/ui" 
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">

    <h:head>
        <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/>

        <title>TimeLine Projetos Pacto</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
        <h:outputStylesheet library="css" name="tooltipster.css"/>
        <h:outputStylesheet library="css" name="tooltipster-light.css"/>
        <h:outputStylesheet library="css" name="font-awesome.min.css"/>

        <h:outputScript target="head" library="primefaces" name="jquery/jquery.js"/>
        <h:outputScript library="js" name="jquery.tooltipster.min.js"/>
        <h:outputScript library="js" name="background-min.js"/>

        <style>
            .fullscreen-bg {
                position: fixed;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                overflow-x: scroll;
                overflow-y: hidden;
                z-index: -100;

            }

            .fullscreen-bg__video {
                position: absolute;
                top: 0;
                left: 0;
                width: auto;

            }
            .cubo{
                height: 120px;
                width: 130px;
                cursor: pointer;
                -webkit-box-shadow: inset 0 0 5px #000000;
                display: inline-flex; 
                background: -moz-linear-gradient(top,  rgba(169,3,41,1) 0%, rgb(255, 0, 58) 44%, rgba(109,0,25,1) 100%); /* FF3.6-15 */
                background: -webkit-linear-gradient(top,  rgba(169,3,41,1) 0%,rgb(255, 0, 58) 44%,rgba(109,0,25,1) 100%); /* Chrome10-25,Safari5.1-6 */
                background: linear-gradient(to bottom, rgb(169, 7, 7) 0%,rgb(255, 0, 58) 44%,rgb(128, 8, 8) 100%);
                filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#73a90329', endColorstr='#6d0019',GradientType=0 ); /* IE6-9 */
                transition-duration: 0.8s;
                -webkit-transition-property: top, left,transform, width, height,margin-top;/* Safari */
                transition-property: top, left,transform, width, height, margin-top;
            }
            .containercubo{
                width: auto;
                float: left; 
                margin-top: 40vh; 
                display: inline-flex; 
                transition-duration: 0.8s;
                -webkit-transition-property: top, left,transform, width, height,margin-top;/* Safari */
                transition-property: top, left,transform, width, height, margin-top;
            }
            .containercubo:hover .c3{
                height: 230px;
                margin-top: -55px;
                width: 260px;
            }
            .containercubo:hover .c2{
                height: 179px;
                width: 180px;
                margin-top: -33px;
            }
            .containercubo:hover .c1{
                height: 143px;
                width: 136px;
                margin-top: -12px;
            }
            .cubo .textoAno{
                display: none;
            }
            .cubo.c3{
                text-align: center;
            }
            .cubo.c3 .textoAno{
                display: block;
                margin-top: 17px;
                width: 200px;
                text-align: center;
                margin-left: -35px;
            }
            .textoAno{
                color: white;
                font-family: Tahoma, Geneva, sans-serif;
                position: absolute;
                font-weight: bold;
                font-size: 61px;
                cursor: pointer;
                transition-duration: 0.8s;
                -webkit-transition-property: top, left,transform, width, height,margin-top, font-size, margin-left;/* Safari */
                transition-property: top, left,transform, width, height, margin-top, font-size, margin-left;
            }
            .containercubo:hover  .textoAno{
                font-size: 114px;
                margin-top: 35px;
                width: 325px;
            }

            .neon, .tituloAno{
                text-shadow:0 0 15px #ffffff;

            }
            .shadowRed{
                box-shadow:0 0 15px #ffffff;  
            }
            ::-webkit-scrollbar { 
                display: none; 
            }
            .rodape{
                width: 500vw;
                height: 100px;
                bottom: 0;

                position: absolute;
            }
            .itemrodape{
                height: 100px;
                width: 130px;
                display: inline-flex; 

            }
            .frente.c3{
                z-index: 9999999 !important;
            }

            @-webkit-keyframes play {
                from { box-shadow:0 0 60px #CE0909;}
            25% {box-shadow:0 0 80px #CE0909;}
            50% {box-shadow:0 0 110px #CE0909;}
            75% {box-shadow:0 0 80px #CE0909;}
            to { box-shadow:0 0 60px #CE0909;}
            }

            @-webkit-keyframes playazul {
                from { box-shadow:0 0 60px #0F15BF;}
            25% {box-shadow:0 0 80px #0F15BF;}
            50% {box-shadow:0 0 110px #0F15BF;}
            75% {box-shadow:0 0 80px #0F15BF;}
            to { box-shadow:0 0 60px #0F15BF;}
            }


            .sombraAnimada.azul{
                box-shadow:0 0 110px #0F15BF;
            }
            .sombraAnimada{
                
                box-shadow:0 0 110px #CE0909;
            }
            .painelano{
                font-family: Tahoma, Geneva, sans-serif !important;
                position: absolute;
                width: 80%;
                height: 80vh;
                left: 10%;
                background-color: rgba(0,0,0,0.8);
                display: none;
            }
            .painelProjeto{
                width: 100%;
            }
            .tituloAno{
                font-size: 4vh;
                text-align: center;
                color: white;
                font-weight: bold;
                width: 100%;
                padding-top: 10px;
                padding-bottom: 10px;
            }
            .tituloProjeto{
                width: 100%;
                /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#000000+0,490e0e+50,050001+100 */
                background: rgb(0,0,0); /* Old browsers */
                background: -moz-linear-gradient(top,  rgba(0,0,0,1) 0%, rgba(73,14,14,1) 50%, rgba(5,0,1,1) 100%); /* FF3.6-15 */
                background: -webkit-linear-gradient(top,  rgba(0,0,0,1) 0%,rgba(73,14,14,1) 50%,rgba(5,0,1,1) 100%); /* Chrome10-25,Safari5.1-6 */
                background: linear-gradient(to bottom,  rgba(0,0,0,1) 0%,rgba(73,14,14,1) 50%,rgba(5,0,1,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
                filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#000000', endColorstr='#050001',GradientType=0 ); /* IE6-9 */
                color: white;
            }

            .cubo.azul{
                background: linear-gradient(to bottom, rgb(13, 0, 117) 0%,rgb(16, 41, 152) 44%,rgb(24, 13, 187) 100%);
            }

            .btn {
                padding-left: 20px ;
                background: #000000;
                color: white !important;
                cursor: pointer;
                border: #1D1A1A solid 1px;
                border-radius: 4px;
                line-height: 4vh;
                font-weight: bold;
            }
            .ui-accordion .ui-accordion-header.ui-state-default a{
                padding: 0 !important;
                color: white;
                text-transform: none;
                text-decoration: none;
            }
            .ui-widget-content{
                background-color: black;
            }
            .ui-accordion .ui-accordion-header.ui-state-default {
                margin-bottom: 2px;
                border: 1px solid #171616;
            }
            button:focus, a:focus {outline:0;}
        </style>


    </h:head>
    <h:body>
        <h:form id="fmLay" prependId="false">
            <div class="fullscreen-bg" id="geral">
                <p:graphicImage style="position: fixed; margin: 20px; height: 40px;-webkit-filter: drop-shadow(1px 0px 4px #fff);filter:drop-shadow(1px 0px 4px #fff);" library="imagens" name="logo-pacto-branca.png"/>


                <div style="width: 500vw;">
                    <ui:repeat value="#{TimeLineControle.itens}" var="ano">
                        <div class="containercubo">
                            <ui:repeat value="#{ano.projetos}" var="projeto">
                                <div class="cubo #{projeto.css} sombraAnimada #{TimeLineControle.azul ? 'azul' : ''}" style="z-index:0; position: absolute;"></div>
                                <div class="cubo #{projeto.css} frente #{TimeLineControle.azul ? 'azul' : ''}" style="z-index: 99999;"
                                     onclick="showAno();">
                                    <h:outputText value="#{projeto.ano}" styleClass="textoAno neon"/>
                                </div>
                            </ui:repeat>

                        </div>
                    </ui:repeat>
                    <!--                    <div class="rodape">
                                            <div style="display: inline-flex;">
                                                <ui:repeat value="# {TimeLineControle.itens}" var="anor">
                                                    <div style="display: inline-flex;  ">
                                                        <ui:repeat value="# { anor.projetos}" var="projetor">
                                                            <div class="itemrodape">
                                                                <div class="tip" title="Teste com tool" 
                                                                     style="width: 130px; height: 3px;cursor: pointer; background-color: #fff;box-shadow:0 0 6px #ffffff; margin-top: 69px; ">
                                                                </div>
                                                            </div>
                                                        </ui:repeat>
                    
                                                    </div>
                                                </ui:repeat>
                                            </div>
                    
                                        </div>-->
                </div>

            </div>

            <div class="painelano">
                <div class="tituloAno">
                    2010
                    <div style="float: right; font-size: 3vh; cursor: pointer;" onclick="hideAno();">
                    <i class="fa-icon-remove"></i>
                </div>
                </div>
                
                
                <p:accordionPanel activeIndex="0">
                    <p:tab titleStyleClass="btn neon" title="Zillyon 2006">
                        <div style="width: 100%; text-align: center;">
                            <iframe style="width: 50%; height: 53vh;"  src="https://www.youtube.com/embed/pX-NpOBB3c0" frameborder="0" allowfullscreen="true"></iframe>

                        </div>
                        <div style="width: 100%; text-align: justify; color: white;">
                            Bacon ipsum dolor amet shoulder chicken beef ribs jerky beef, tri-tip fatback. Pig beef shank fatback alcatra flank jowl. Meatball kielbasa turkey drumstick porchetta shoulder tail sausage filet mignon ham hock shank prosciutto tongue fatback. T-bone prosciutto doner, ribeye cow short loin porchetta venison cupim.

                            Pig chuck cow ribeye. Tongue pig ham swine. Kevin chuck strip steak pork belly salami pancetta chicken bresaola brisket boudin rump meatball ground round cow. Porchetta picanha tongue ball tip. Meatloaf t-bone spare ribs kevin short loin turducken brisket filet mignon. Brisket pork chop biltong, pancetta bresaola chicken shoulder jerky.
                        </div>
                    </p:tab>
                    <p:tab titleStyleClass="btn neon" title="ZillyonWeb">
                        <div style="width: 100%; text-align: center;">
                            <img style="width: 50%; height: 40vh; margin-bottom: 30px;" src="http://www.pacto.vc/wp-content/uploads/2014/06/nova-versao-template_imagem-destacada-blog.jpg"/>
                        </div>
                        
                        <div style="width: 100%; text-align: justify; color: white;">
                            Bacon ipsum dolor amet shoulder chicken beef ribs jerky beef, tri-tip fatback. Pig beef shank fatback alcatra flank jowl. Meatball kielbasa turkey drumstick porchetta shoulder tail sausage filet mignon ham hock shank prosciutto tongue fatback. T-bone prosciutto doner, ribeye cow short loin porchetta venison cupim.

                            Pig chuck cow ribeye. Tongue pig ham swine. Kevin chuck strip steak pork belly salami pancetta chicken bresaola brisket boudin rump meatball ground round cow. Porchetta picanha tongue ball tip. Meatloaf t-bone spare ribs kevin short loin turducken brisket filet mignon. Brisket pork chop biltong, pancetta bresaola chicken shoulder jerky.
                        </div>
                    </p:tab>
                </p:accordionPanel>

            </div>

            <script>
                (function() {
                    function scrollHorizontally(e) {
                        e = window.event || e;
                        var delta = Math.max(-1, Math.min(1, (e.wheelDelta || -e.detail)));
                        document.getElementById('geral').scrollLeft -= (delta * 40); // Multiplied by 40
                        e.preventDefault();
                    }
                    if (document.getElementById('geral').addEventListener) {
                        // IE9, Chrome, Safari, Opera
                        document.getElementById('geral').addEventListener("mousewheel", scrollHorizontally, false);
                        // Firefox
                        document.getElementById('geral').addEventListener("DOMMouseScroll", scrollHorizontally, false);
                    } else {
                        // IE 6/7/8
                        document.getElementById('geral').attachEvent("onmousewheel", scrollHorizontally);
                    }
                })();


                function montarTips() {
                    $('.tip').tooltipster({
                        theme: 'tooltipster-light',
                        contentAsHTML: true,
                        animation: 'grow'
                    });
                }
                montarTips();
                
                function showAno(){
                    $('.painelano').slideDown();
                    
                }
                
                function hideAno(){
                    $('.painelano').slideUp();
                    
                }
            </script>
            <ui:fragment rendered="#{TimeLineControle.backgroundVivo}">
                <script>
                    $(document).ready(function() {
                        $('body').easyBackground({
                            shape: 'square',
                            colors: ['#ff0000', '#ff0000', '#ff0000', '#ff0000'],
                            border: false,
                            maxSpeedX: 3,
                            maxSpeedY: 3,
                            numParticles: 25,
                            minRotateSpeed: 0,
                            maxRotateSpeed: 0,
                            wrapNeighbours: true,
                            baseColor: '#000000'
                        });
                    });


                </script>
            </ui:fragment>
            <ui:fragment rendered="#{TimeLineControle.azul}">
                <script>
                    $(document).ready(function() {
                        $('body').easyBackground({
                            effect: 'gradient',
                            duration: 5000,
                            gradientType: 'diagonal',
                            gradientColors: ['#6D97FF', '#0C0E4D'],
                            gradientAnimateColors: ['#0C0E4D', '#6D97FF'],
                            overlay: 'grid',
                            wrapNeighbours: true
                        });
                    });

                    
                </script>
            </ui:fragment>
            <ui:fragment rendered="#{!TimeLineControle.azul and !TimeLineControle.backgroundVivo}">
                <style>
                    .fullscreen-bg{
                        background-color: black;
                    } 
                </style>
            </ui:fragment>
        </h:form>        
    </h:body>
</html>