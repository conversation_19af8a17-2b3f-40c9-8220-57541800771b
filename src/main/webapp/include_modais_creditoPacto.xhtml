<ui:composition
        xmlns="http://www.w3.org/1999/xhtml"
        xmlns:h="http://java.sun.com/jsf/html"
        xmlns:ui="http://java.sun.com/jsf/facelets"
        xmlns:f="http://java.sun.com/jsf/core"
        xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
        xmlns:fn="http://java.sun.com/jsp/jstl/functions"
        xmlns:p="http://primefaces.org/ui"
        xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">

    <p:dialog widgetVar="modalDialogCreditoPacto" modal="true" id="modalCreditoPacto" showEffect="fade"
              hideEffect="fade"
              resizable="true" width="400">

        <f:facet name="header">
            <h:outputText value="Crédito Pacto"/>
        </f:facet>

        <h:panelGrid columns="2" id="panelGridCreditoPacto">
            <p:outputLabel value="Descrição" style="font-weight: bold"/>
            <h:inputText value="#{CreditoPactoControle.creditoPacto.descricao}"/>

            <p:outputLabel value="Qtd Mínima para gerar cobrança" style="font-weight: bold"/>
            <p:inputText value="#{CreditoPactoControle.creditoPacto.qtdMinima}"
                         onkeypress="mascara(this, somenteNumeros)"/>

            <p:outputLabel value="Qtd Máxima" style="font-weight: bold"/>
            <p:inputText value="#{CreditoPactoControle.creditoPacto.qtdMaxima}"
                         onkeypress="mascara(this, somenteNumeros)"/>

            <p:outputLabel value="Valor Mensal" style="font-weight: bold"/>
            <p:inputText value="#{CreditoPactoControle.creditoPacto.valorMensal}"
                         onkeypress="mascara(this, moeda)"/>

            <p:outputLabel value="Valor Unitário Excedente" style="font-weight: bold"/>
            <p:inputText value="#{CreditoPactoControle.creditoPacto.valorUnitarioExcedente}"
                         onkeypress="mascara(this, moeda)"/>
        </h:panelGrid>

        <h:panelGroup layout="block"
                      rendered="false"
                      style="justify-content: center">
            <p:outputLabel value="Adicionar Empresa" style="font-weight: bold"/>

            <p:autoComplete id="empresasSelecionadas"
                            styleClass="full-width"
                            panelStyleClass="full-width"
                            multiple="true"
                            scrollHeight="300"
                            value="#{CreditoPactoControle.empresasSelecionadas}"
                            completeMethod="#{CreditoPactoControle.consultarEmpresas}"
                            var="empresa"
                            converter="empresaFinanceiroConverter"
                            minQueryLength="3"
                            itemLabel="#{empresa.nomeResumo}"
                            itemValue="#{empresa}"
                            forceSelection="true">
                <p:column>
                    <h:outputText value="#{empresa.nomeResumo}"/>
                </p:column>
                <p:ajax event="itemSelect" listener="#{CreditoPactoControle.adicionarEmpresa(empresa)}"
                        process="@form"/>
            </p:autoComplete>
        </h:panelGroup>

        <h:panelGroup layout="block"
                      style="display: flex; justify-content: center; padding: 20px;">
            <p:commandLink styleClass="btn btn-primary"
                           value="Gravar"
                           update=":fmLay:lista"
                           action="#{CreditoPactoControle.gravar}"/>
            <p:commandLink rendered="#{not empty CreditoPactoControle.creditoPacto.codigo}"
                           styleClass="btn btn-secundary"
                           style="margin-left: 20px"
                           value="Excluir"
                           update=":fmLay:lista"
                           action="#{CreditoPactoControle.excluir}"/>
        </h:panelGroup>
        <p:messages id="messagesModalCreditoPacto" showSummary="true" showDetail="false" autoUpdate="true"
                    closable="true"/>
    </p:dialog>
</ui:composition>
