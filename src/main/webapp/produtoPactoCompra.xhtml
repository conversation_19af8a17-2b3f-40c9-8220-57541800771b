<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="conteudo">
        <style>
            @media (max-width: 1400px) {
                .container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
                    width: 100% !important;
                }
            }
        </style>

        <div class="container-fluid noSlider" style="padding: 0px;">
            <div class="bread span12"></div>

            <h:panelGroup styleClass="span10 caixaPrincipal" id="dados"
                          style="width: 100%; padding: 0px; margin: 0px;">
                <h4>Vendas Realizadas</h4>
                <hr/>
                <p:outputPanel layout="block" styleClass="pesq pesq-tabelas buscaAuxiliar" id="filtrar">
                    <p:inputText id="globalFilter" value="#{ProdutoPactoCompraControle.filtro}">
                        <f:ajax event="keyup"
                                listener="#{ProdutoPactoCompraControle.filtrar(ProdutoPactoCompraControle.listaCompras)}"
                                delay="#{ProdutoPactoCompraControle.tempoDelay}"
                                render=":fmLay:lista"
                        />
                    </p:inputText>

                    <p:watermark for="globalFilter" value="#{title['cadastros.filtrar']}"/>
                </p:outputPanel>

                <p:dataTable var="compra" value="#{ProdutoPactoCompraControle.listaCompras}" id="lista"
                             widgetVar="tabelaAtual"
                             emptyMessage="#{msg['tabela.semregistros']}"
                             style="margin: 0"
                             styleClass="tabelaPrincipal">

                    <f:facet name="header">
                        <h:panelGroup layout="block" id="totalTabela" styleClass="totalizaTabela">
                            <h:outputText value="#{fn:length(ProdutoPactoCompraControle.listaTela)} produtos"/>
                        </h:panelGroup>
                    </f:facet>

                    <p:column headerText="ID" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoCompraControle.selecionar(compra)}">#{compra.id}</p:commandLink>
                    </p:column>

                    <p:column headerText="Produto" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoCompraControle.selecionar(compra)}">#{compra.produtoPacto.nome}</p:commandLink>
                    </p:column>

                    <p:column headerText="Sucesso" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoCompraControle.selecionar(compra)}">#{compra.sucesso_Apresentar}</p:commandLink>
                    </p:column>

                    <p:column headerText="Email Financeiro" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoCompraControle.selecionar(compra)}">#{compra.resultadoEmailFinanceiro_Apresentar}</p:commandLink>
                    </p:column>

                    <p:column headerText="Email Cliente" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoCompraControle.selecionar(compra)}">#{compra.resultadoEmailCliente_Apresentar}</p:commandLink>
                    </p:column>

                    <p:column headerText="Data" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true"
                                       action="#{ProdutoPactoCompraControle.selecionar(compra)}">#{compra.dataRegistroApresentar}</p:commandLink>
                    </p:column>

                    <p:column headerText="Origem" style="text-align: center">
                        <p:commandLink ajax="true" partialSubmit="true" style="text-transform: capitalize"
                                       action="#{ProdutoPactoCompraControle.selecionar(compra)}">#{compra.sistemaOrigem}</p:commandLink>
                    </p:column>
                </p:dataTable>
            </h:panelGroup>
        </div>

    </ui:define>
    <ui:define name="JS">
        <h:outputScript library="js" name="cadastrosV2.js"/>
    </ui:define>
</ui:decorate>
</html>
