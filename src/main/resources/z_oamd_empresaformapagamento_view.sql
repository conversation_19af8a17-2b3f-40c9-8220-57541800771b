-- public.z_oamd_empresaformapagamento_view source

CREATE OR REPLACE VIEW public.z_oamd_empresaformapagamento_view
AS SELECT z_oamd_empresaformapagamento.codigo,
          z_oamd_empresaformapagamento.empresazw,
          z_oamd_empresaformapagamento.anoexpiracao,
          z_oamd_empresaformapagamento.ativa,
          z_oamd_empresaformapagamento.chave,
          z_oamd_empresaformapagamento.nomebanco,
          z_oamd_empresaformapagamento.cidade,
          z_oamd_empresaformapagamento.clientesativos,
          z_oamd_empresaformapagamento.cnpj,
          z_oamd_empresaformapagamento.codigoempresa,
          z_oamd_empresaformapagamento.creditodcc,
          z_oamd_empresaformapagamento.dataexpiracao,
          z_oamd_empresaformapagamento.dataexpiracaocreditodcc,
          z_oamd_empresaformapagamento.diaex<PERSON><PERSON><PERSON>,
          z_oamd_empresaformapagamento.estado,
          z_oamd_empresaformapagamento.faturadoavista,
          z_oamd_empresaformapagamento.faturadoboleto,
          z_oamd_empresaformapagamento.faturadocartocredito,
          z_oamd_empresaformapagamento.faturadocartaocreditodcc,
          z_oamd_empresaformapagamento.faturadocartaocreditonormal,
          z_oamd_empresaformapagamento.faturadocheque,
          z_oamd_empresaformapagamento.faturadocontacorrente,
          z_oamd_empresaformapagamento.faturadopagamentodigital,
          z_oamd_empresaformapagamento.mesexpiracao,
          z_oamd_empresaformapagamento.nome,
          z_oamd_empresaformapagamento.periodofim,
          z_oamd_empresaformapagamento.periodoinicio,
          z_oamd_empresaformapagamento.qtdrecibos,
          z_oamd_empresaformapagamento.razaosocial,
          z_oamd_empresaformapagamento.sequencia,
          z_oamd_empresaformapagamento.situacao,
          z_oamd_empresaformapagamento.tokensms,
          z_oamd_empresaformapagamento.uf,
          z_oamd_empresaformapagamento.valorfaturado,
          z_oamd_empresaformapagamento.qtdacessomes,
          z_oamd_empresaformapagamento.dataultimoacesso,
          date_part('year'::text, z_oamd_empresaformapagamento.dataultimoacesso) AS ua_ano,
          date_part('month'::text, z_oamd_empresaformapagamento.dataultimoacesso) AS ua_mes,
          z_oamd_empresaformapagamento.dataconsulta::date - z_oamd_empresaformapagamento.dataultimoacesso::date AS ua_quantosdias_ultimoacesso_catraca,
          CASE
              WHEN z_oamd_empresaformapagamento.dataultimoacesso::date > z_oamd_empresaformapagamento.dataconsulta::date THEN '01: Acessos Futuros'::text
              WHEN z_oamd_empresaformapagamento.dataultimoacesso::date = z_oamd_empresaformapagamento.dataconsulta::date THEN '02: Acessos no dia '::text
              WHEN (z_oamd_empresaformapagamento.dataconsulta - z_oamd_empresaformapagamento.dataultimoacesso) < '06:00:00'::interval THEN '03: 0-6 horas sem acesso '::text
              WHEN (z_oamd_empresaformapagamento.dataconsulta - z_oamd_empresaformapagamento.dataultimoacesso) < '12:00:00'::interval THEN '04: 7-12 horas sem acesso '::text
              WHEN (z_oamd_empresaformapagamento.dataconsulta - z_oamd_empresaformapagamento.dataultimoacesso) < '24:00:00'::interval THEN '05: 13-24 horas sem acesso '::text
              WHEN (z_oamd_empresaformapagamento.dataconsulta - z_oamd_empresaformapagamento.dataultimoacesso) < '48:00:00'::interval THEN '06: 25-48 horas sem acesso '::text
              ELSE '07: 49 horas acima'::text
              END AS agrup_ua_faixa_acesso_horas,
          CASE
              WHEN z_oamd_empresaformapagamento.clientesativos <= 200 THEN '01 - FAIXA1_350'::text
              WHEN z_oamd_empresaformapagamento.clientesativos <= 500 THEN '02 - FAIXA2_500'::text
              WHEN z_oamd_empresaformapagamento.clientesativos <= 800 THEN '03 - FAIXA3_750'::text
              WHEN z_oamd_empresaformapagamento.clientesativos > 800 THEN '04 - FAIXA4_1100'::text
              ELSE NULL::text
              END AS agrup_faixa_clientes_ativos,
          CASE
              WHEN z_oamd_empresaformapagamento.dataexpiracao < z_oamd_empresaformapagamento.dataconsulta::date THEN 'BLOQUEADO'::text
              WHEN z_oamd_empresaformapagamento.dataexpiracao >= z_oamd_empresaformapagamento.dataconsulta::date THEN 'VAI BLOQUEAR'::text
              ELSE '_OK'::text
              END AS expirado,
          date_part('month'::text, z_oamd_empresaformapagamento.dataexpiracaocreditodcc) AS dcc_dt_exp_mes,
          date_part('year'::text, z_oamd_empresaformapagamento.dataexpiracaocreditodcc) AS dcc_dt_exp_ano,
          z_oamd_empresaformapagamento.ultimolancamento AS data_ultimolancamento_contrato,
          CASE
              WHEN z_oamd_empresaformapagamento.ultimolancamento::date > z_oamd_empresaformapagamento.dataconsulta::date THEN '01: Lançamento Futuros'::text
              WHEN z_oamd_empresaformapagamento.ultimolancamento::date = z_oamd_empresaformapagamento.dataconsulta::date THEN '02: Lançamento no dia'::text
              WHEN (z_oamd_empresaformapagamento.dataconsulta - z_oamd_empresaformapagamento.ultimolancamento) < '3 days'::interval THEN '03: 1-3 dias'::text
              WHEN (z_oamd_empresaformapagamento.dataconsulta - z_oamd_empresaformapagamento.ultimolancamento) < '10 days'::interval THEN '04: 4-10 dias'::text
              WHEN (z_oamd_empresaformapagamento.dataconsulta - z_oamd_empresaformapagamento.ultimolancamento) < '20 days'::interval THEN '05: 11-20 dias'::text
              WHEN z_oamd_empresaformapagamento.ultimolancamento IS NULL THEN '06: Sem lançamento'::text
              ELSE '07: 21 dias ou mais'::text
              END AS agrup_ultimolancamento_contrato,
          z_oamd_empresaformapagamento.dataultimologin,
          z_oamd_empresaformapagamento.dataconsulta::date - z_oamd_empresaformapagamento.dataultimologin::date AS ua_quantosdias_ultimologin,
          CASE
              WHEN z_oamd_empresaformapagamento.dataultimologin::date > z_oamd_empresaformapagamento.dataconsulta::date THEN '01: Login Futuros'::text
              WHEN z_oamd_empresaformapagamento.dataultimologin::date = z_oamd_empresaformapagamento.dataconsulta::date THEN '02: Login no dia'::text
              WHEN (z_oamd_empresaformapagamento.dataconsulta - z_oamd_empresaformapagamento.dataultimologin) < '3 days'::interval THEN '03: 1-3 dias sem login'::text
              WHEN (z_oamd_empresaformapagamento.dataconsulta - z_oamd_empresaformapagamento.dataultimologin) < '10 days'::interval THEN '04: 4-10 dias sem login'::text
              WHEN (z_oamd_empresaformapagamento.dataconsulta - z_oamd_empresaformapagamento.dataultimologin) < '20 days'::interval THEN '05: 11-20 dias sem login'::text
              ELSE '07: 21 dias ou mais sem login'::text
              END AS agrup_ultimo_login_horas,
          CASE
              WHEN z_oamd_empresaformapagamento.valorfaturado < 0::numeric THEN '01: Negativo'::text
              WHEN z_oamd_empresaformapagamento.valorfaturado <= 50000::numeric THEN '02: até 50 K'::text
              WHEN z_oamd_empresaformapagamento.valorfaturado <= 80000::numeric THEN '03: até 80 K'::text
              WHEN z_oamd_empresaformapagamento.valorfaturado <= 100000::numeric THEN '04: até 100 K'::text
              WHEN z_oamd_empresaformapagamento.valorfaturado <= 150000::numeric THEN '05: até 150 K'::text
              WHEN z_oamd_empresaformapagamento.valorfaturado > 150000::numeric THEN '06: acima de 150K'::text
              ELSE '07: SEM FAIXA'::text
              END AS agrup_fatur_faixa,
          z_oamd_empresaformapagamento.dataconsulta,
          date_part('year'::text, z_oamd_empresaformapagamento.dataconsulta) AS consulta_ano,
          date_part('month'::text, z_oamd_empresaformapagamento.dataconsulta) AS consulta_mes,
          z_oamd_empresaformapagamento.redeempresa,
          z_oamd_empresaformapagamento.tipoempresa,
          CASE
              WHEN z_oamd_empresaformapagamento.inicioimplantacao IS NULL THEN 'Não implantada'::text
              WHEN z_oamd_empresaformapagamento.inicioimplantacao IS NOT NULL AND z_oamd_empresaformapagamento.finalimplantacao IS NULL THEN 'Em implantação'::text
              WHEN z_oamd_empresaformapagamento.inicioimplantacao IS NOT NULL AND z_oamd_empresaformapagamento.finalimplantacao IS NOT NULL THEN 'Em produção'::text
              ELSE 'Problemas com as datas de implantação'::text
              END AS agrup_implantacao,
          CASE
              WHEN z_oamd_empresaformapagamento.tipocobrancadcc = 0 THEN 'Pré-Pago'::text
              WHEN z_oamd_empresaformapagamento.tipocobrancadcc = 1 THEN 'Pós-Pago Efetivado'::text
              WHEN z_oamd_empresaformapagamento.tipocobrancadcc = 2 THEN 'Pós-Pago Tentativa'::text
              ELSE ''::text
              END AS tipocobranca,
          z_oamd_empresaformapagamento.nivelreceitamensal,
          z_oamd_empresaformapagamento.renegociadoate,
          z_oamd_empresaformapagamento.condicaoespecial,
          z_oamd_empresaformapagamento.ativaoamd,
          CASE
              WHEN COALESCE(z_oamd_empresaformapagamento.motivoindisponivel, ''::text) = ''::text THEN 'Disponível'::text
              ELSE 'Indisponível'::text
              END AS disponibilidade,
          z_oamd_empresaformapagamento.motivoindisponivel,
          CASE
              WHEN z_oamd_empresaformapagamento.servidorweb IS NULL THEN ' - '::text
              WHEN z_oamd_empresaformapagamento.servidorweb IS TRUE THEN 'Locaweb'::text
              ELSE 'Servidor Local'::text
              END AS servidor,
          z_oamd_empresaformapagamento.temnfse,
          z_oamd_empresaformapagamento.primeiranfseemitida,
          z_oamd_empresaformapagamento.qtenfseemitidas,
          z_oamd_empresaformapagamento.qtenfseemitidasultimomes,
          z_oamd_empresaformapagamento.codigofinanceiro,
          e.datacadastro,
          e.datadesativacao,
          ((z_oamd_empresaformapagamento.chave::text || '-'::text) || z_oamd_empresaformapagamento.codigoempresa IN ( SELECT (v1.chave::text || '-'::text) || v1.codigoempresa
                                                                                                                      FROM z_oamd_empresaformapagamento v1
                                                                                                                               LEFT JOIN empresafinanceiro ef ON v1.codigofinanceiro = ef.codigofinanceiro
                                                                                                                      WHERE v1.ativa = true AND v1.ativaoamd = true AND v1.empresazw::text = 'PRODUCAO'::text AND ef.datacadastro < '2019-10-01 00:00:00'::timestamp without time zone AND ef.datadesativacao IS NULL AND v1.periodoinicio > '2019-12-31'::date AND v1.clientesativos > 10
                                                                                                                      GROUP BY (v1.chave::text || '-'::text) || v1.codigoempresa
                                                                                                                      HAVING count(v1.dataconsulta) >= 21)) AS desdeinicio
   FROM z_oamd_empresaformapagamento
            LEFT JOIN empresafinanceiro e ON z_oamd_empresaformapagamento.codigofinanceiro = e.codigofinanceiro;
