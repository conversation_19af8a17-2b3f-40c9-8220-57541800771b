select
  em.codigo                                       AS codigoEmpresa,
  em.ativa,
  em.nome,
  em.razaosocial,
  em.cnpj,
  em.tokensms,
  date_part('day', em.dataexpiracao) :: INTEGER   AS diaExpiracao,
  date_part('month', em.dataexpiracao) :: INTEGER AS mesExpiracao,
  date_part('year', em.dataexpiracao) :: INTEGER  AS anoExpiracao,
  em.dataexpiracao,
  em.dataexpiracaocreditodcc,
  es.sigla                                        AS UF,
  es.descricao                                    AS estado,
  c.nome                                          AS cidade,
  em.creditodcc                                   AS creditodcc,
  em.tipoCobrancaPacto                            AS tipoCobrancaDcc,
  (select count(*) from situacaoclientesinteticodw c where c.empresacliente = em.codigo and (c.situacao IN ('AT') OR c.situacaocontrato IN ('VE')) ) as clientesAtivos,
  (select max(contr.datalancamento) from contrato contr where contr.empresa = em.codigo) as ultimoLancamento,
  (select max(dataultimoacesso) from situacaoclientesinteticodw c where c.empresacliente = em.codigo) as dataUltimoAcesso,
  foo.ultdata as dataUltimoLogin,
  CASE WHEN (em.dataexpiracao > CURRENT_DATE) :: TEXT = 'true'
    THEN 'VAI EXPIRAR'
  WHEN (em.dataexpiracao <= CURRENT_DATE) :: TEXT = 'true'
    THEN 'EXPIRADO'
  ELSE 'SEM DATA' END AS situacao,
  _datainicio :: DATE  AS periodoInicio,
  _datafim :: DATE AS periodoFim,
  (select count(codigo) from recibopagamento  where empresa = em.codigo and data::date between _datainicio and  _datafim) as qteRecibos,
  (select coalesce(sum(valortotal::numeric),0) from movpagamento where empresa = em.codigo and datalancamento::date between _datainicio and _datafim) as valorfaturado,
  (select coalesce(sum(valortotal::numeric),0) from movpagamento mp inner join formapagamento fp on fp.codigo = mp.formapagamento where empresa = em.codigo and fp.tipoformapagamento = 'CH' and datalancamento::date between _datainicio and _datafim) as faturadoCheque,
  (select coalesce(sum(valortotal::numeric),0) from movpagamento mp inner join formapagamento fp on fp.codigo = mp.formapagamento where empresa = em.codigo and fp.tipoformapagamento = 'AV' and datalancamento::date between _datainicio and _datafim) as faturadoAVista,
  (select coalesce(sum(valortotal::numeric),0) from movpagamento mp inner join formapagamento fp on fp.codigo = mp.formapagamento where empresa = em.codigo and fp.tipoformapagamento = 'CA' and datalancamento::date between _datainicio and _datafim) as faturadoCartoCredito,
  (select coalesce(sum(valortotal::numeric),0) from movpagamento mp inner join formapagamento fp on fp.codigo = mp.formapagamento where empresa = em.codigo and fp.tipoformapagamento = 'CA' and (fp.defaultrecorrencia = FALSE AND fp.conveniocobranca IS NULL)  and  datalancamento::date between _datainicio and _datafim) as faturadoCartaoCreditoNormal,
  (select coalesce(sum(valortotal::numeric),0) from movpagamento mp inner join formapagamento fp on fp.codigo = mp.formapagamento where empresa = em.codigo and fp.tipoformapagamento = 'CA' and (fp.defaultrecorrencia = TRUE  OR fp.conveniocobranca > 0) and  datalancamento::date between _datainicio and _datafim) as faturadoCartaoCreditoDCC,
  (select coalesce(sum(valortotal::numeric),0) from movpagamento mp inner join formapagamento fp on fp.codigo = mp.formapagamento where empresa = em.codigo and fp.tipoformapagamento = 'CD' and datalancamento::date between _datainicio and _datafim) as faturadoCartaoDebito,
  (select coalesce(sum(valortotal::numeric),0) from movpagamento mp inner join formapagamento fp on fp.codigo = mp.formapagamento where empresa = em.codigo and fp.tipoformapagamento = 'BB' and datalancamento::date between _datainicio and _datafim) as faturadoBoleto,
  (select coalesce(sum(valortotal::numeric),0) from movpagamento mp inner join formapagamento fp on fp.codigo = mp.formapagamento where empresa = em.codigo and fp.tipoformapagamento = 'CC' and datalancamento::date between _datainicio and _datafim) as faturadoContaCorrente,
  (select coalesce(sum(valortotal::numeric),0) from movpagamento mp inner join formapagamento fp on fp.codigo = mp.formapagamento where empresa = em.codigo and fp.tipoformapagamento = 'PD' and datalancamento::date between _datainicio and _datafim) as faturadoPagamentoDigital,
  coalesce(em.chavenfse, '') <> '' temNFSe,
  (select min(dataemissao) from nfseemitida where empresa = em.codigo) ::DATE primeiraNFSeEmitida,
  (select count(*) from nfseemitida where empresa = em.codigo) qteNFSeEmitidas,
  (select count(*) from nfseemitida where empresa = em.codigo and dataemissao between _datainicio and _datafim) qteNFSeEmitidasUltimoMes,
  (select count(ac.codigo) from acessocliente ac left join localacesso la ON ac.localacesso = la.codigo WHERE  la.empresa = 1 AND TO_CHAR(dthrentrada,'MM/YYYY') =  TO_CHAR(CURRENT_DATE, 'MM/YYYY')) as qtdAcessoMes
FROM empresa em
  LEFT JOIN estado es ON es.codigo = em.estado
  LEFT JOIN cidade c ON c.codigo = em.cidade
  INNER JOIN (SELECT
                e.codigo            AS empresa,
                max(l.dataregistro) AS ultdata
              FROM usuario u
                INNER JOIN logcontroleusabilidade l
                  ON l.usuario = u.codigo AND l.acao = 'LOGADO' AND u.username NOT IN ('admin', 'PACTOBR', 'RECOR')
                INNER JOIN empresa e ON e.codigo = l.empresa
              GROUP BY e.codigo
              ORDER BY ultdata) AS foo ON foo.empresa = em.codigo
