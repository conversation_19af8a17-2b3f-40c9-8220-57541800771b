<?xml version="1.0" encoding="UTF-8"?>
<persistence xmlns="http://java.sun.com/xml/ns/persistence"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="1.0"
             xsi:schemaLocation="http://java.sun.com/xml/ns/persistence
		http://java.sun.com/xml/ns/persistence/persistence_1_0.xsd">

    <persistence-unit name="OamdPU" transaction-type="RESOURCE_LOCAL">
        <description>
            OamdPU
        </description>

        <provider>org.hibernate.ejb.HibernatePersistence</provider>
        
        <properties>
            <property name="hibernate.archive.autodetetion" value="class"/>
            <property name="hibernate.dialect" value="org.hibernate.dialect.PostgreSQLDialect"/>
            <property name="hibernate.connection.driver_class" value="org.postgresql.Driver"/>            
            <property name="hibernate.hbm2ddl.auto" value="update"/>

            <property name="hibernate.connection.url" value="**********************************************@"/>
            <property name="hibernate.connection.username" value="@USER_BD@" />
            <property name="hibernate.connection.password" value="@PWD_BD@" />
            
            <property name="hibernate.show_sql" value="false"/>
            <property name="hibernate.format_sql" value="true"/>
            <property name="hibernate.generate_statistics" value="false"/>
            <property name="hibernate.use_sql_comments" value="false"/>
            <property name="hibernate.pool_size" value="8"/>
            
            <property name="transaction.factory_class" value="org.hibernate.transaction.JDBCTransactionFactory"/>
            <property name="hibernate.current_session_context_class" value="thread"/>
            <property name="hibernate.enable_lazy_load_no_trans" value="true"/>
            
            <!-- AUDITORIA -->
            <property name="hibernate.ejb.event.post-insert" value="br.com.pacto.base.jpa.audit.CustomAuditEventListener" />
            <property name="hibernate.ejb.event.post-update" value="br.com.pacto.base.jpa.audit.CustomAuditEventListener" />
            <property name="hibernate.ejb.event.post-delete" value="br.com.pacto.base.jpa.audit.CustomAuditEventListener" />            
            <property name="hibernate.ejb.event.pre-collection-update" value="br.com.pacto.base.jpa.audit.CustomAuditEventListener" />
            <property name="hibernate.ejb.event.pre-collection-remove" value="br.com.pacto.base.jpa.audit.CustomAuditEventListener" />
            <property name="hibernate.ejb.event.post-collection-recreate" value="br.com.pacto.base.jpa.audit.CustomAuditEventListener" />
            <!-- -->
            <property name="org.hibernate.envers.revision_listener" value="br.com.pacto.base.jpa.audit.CustomEnversListener"/>
            
        </properties>
    </persistence-unit>
</persistence>