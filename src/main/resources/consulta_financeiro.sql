SELECT
  f.codigozw,
  ctm.ds_categoria Categoria,
  ct.id_categoria id_SubCategoria,
  ct.ds_categoria SubCategoria,
  subcla.ds_classificacao Classificacao,
  cla.id_classificacao id_SubClassificacao,
  cla.ds_classificacao SubClassificacao,
  grupo.descricao ds_grupofavorecido,
  grupo.tipo ds_grupotipo,
  -- ct.tipomovimento as D1R2,
  CASE
    WHEN ct.tipomovimento = 1 THEN 'Saidas (-)'
    ELSE 'Entradas (+)'
  END as D1R2,
  api.ID_AGENDPARCELA Id_MesmaParcela,
  CASE
    WHEN ct.tipomovimento = 1 THEN (api.vl_item*-1)
    ELSE api.vl_item
  END as ValorItem,
  api.DESCRICAO as DescricaoDoItem,
  ap.descricao AS DescricaoDaParcela,
  ap.dthr_lancamento,
  EXTRACT(Year from dthr_lancamento) as <PERSON><PERSON><PERSON><PERSON><PERSON>, EXTRACT(Month from dthr_lancamento) as <PERSON><PERSON>_<PERSON><PERSON>, EXTRACT(Day from dthr_lancamento) as <PERSON><PERSON>_<PERSON><PERSON>,
  ap.dthr_quitacao,
  EXTRACT(Year from dthr_quitacao) as Ano_Quit, EXTRACT(Month from dthr_quitacao) as Mes_Quit, EXTRACT(Day from dthr_quitacao) as Dia_Quit,
  ap.dthr_vencimento,
  EXTRACT(Year from dthr_vencimento) as Ano_Venc, EXTRACT(Month from dthr_vencimento) as Mes_Venc, EXTRACT(Day from dthr_vencimento) as Dia_Venc,
  ap.DATACOMPETENCIA,
  EXTRACT(Year from DATACOMPETENCIA) as Ano_Comp, EXTRACT(Month from DATACOMPETENCIA) as Mes_Comp, EXTRACT(Day from DATACOMPETENCIA) as Dia_Comp,
  ap.nr_cheque,
  ap.nr_documento,
  ap.nr_parcela,
  ap.ID_AGENDPARCELA as ID_PARCELA,
  a.PERIODICO AS AGD_PERIODICO,
  co.ABREVIACAO as ContaOrigem,
  cd.ABREVIACAO as ContaDestino,
  f.NO_FAVORECIDO,
  f.ENDERECO,
  f.CIDADE,
  f.ESTADO,
  substring(F.TELEFONE FROM 1 FOR 220) AS TELEFONE,
  f.ABREVIACAO as AbreviacaoFavorecido,
  f.EMPRESA as NO_FAVOREC_Empresa,
  f.CONTATO as PessoaDeContato,
  f.FAX,
  substring(F.EMAIL FROM 1 FOR 250) AS EMAIL,
  F.DATAATUALIZACAO as DataAtualizacaoCadastro,
  F.DATACADASTRO,
  EXTRACT(Year from F.DATACADASTRO) as Ano_Cad, EXTRACT(Month from F.DATACADASTRO) as Mes_Cad, EXTRACT(Day from F.DATACADASTRO) as Dia_Cad,
  F.Data_Expiracao,
  F.CHAVEZW,
  F.CGC,
  (select MIN(SISTEMASFAVORECIDO.DATAVENCCERT) FROM SISTEMASFAVORECIDO WHERE
    SISTEMASFAVORECIDO.CODFAVORECIDO = F.ID_FAVORECIDO) AS DATA_EXPNOVO_CERTIFICADO,
  CASE
    WHEN AP.DTHR_QUITACAO IS NULL THEN 'ABERTO'
    ELSE 'QUITADO'
  END AS QUITADO,
  CASE
    WHEN AP.DTHR_VENCIMENTO < (CURRENT_DATE-1)  THEN 'VENCIDO'
    ELSE 'NAO'
  END AS VENCIDO,
  CASE
    WHEN AP.DATACOMPETENCIA < (CURRENT_DATE-1)  THEN 'VENCIDA'
    ELSE 'FUTURA'
  END AS STATUSCOMPETENCIA,
  CASE
    WHEN ( ap.ID_CONTADESTINO > 0
        AND ap.ID_CONTAORIGEM > 0 ) THEN 'SIM'
    ELSE 'NAO'
  END AS TRANFERENCIA,
  CASE
    WHEN (CAST(ap.DTHR_QUITACAO AS DATE) - CAST(ap.DATACOMPETENCIA AS DATE)) <= -1 THEN 'ANTECIPADO'
    WHEN (CAST(ap.DTHR_QUITACAO AS DATE) - CAST(ap.DATACOMPETENCIA AS DATE)) <= 3 THEN  'ATE03-EM DIA'
    WHEN (CAST(ap.DTHR_QUITACAO AS DATE) - CAST(ap.DATACOMPETENCIA AS DATE)) <= 10 THEN 'ATE10-ATRASADO'
    WHEN (CAST(ap.DTHR_QUITACAO AS DATE) - CAST(ap.DATACOMPETENCIA AS DATE)) <= 30 THEN 'ATE30-MUITO ATRASADO '
    ELSE 'MAIS DE 30 ATRASADO'
  END AS PAGAEMDIA,
  CASE
    WHEN ap.DTHR_QUITACAO IS NOT NULL
      THEN (CAST(ap.DTHR_QUITACAO AS DATE) - CAST(ap.DATACOMPETENCIA AS DATE))
    ELSE ( current_date - CAST(ap.DATACOMPETENCIA AS DATE))
  END
   AS DIAS_ATRASO,
  CASE
     WHEN ct.EQUIVALENCIADRE = 1 THEN 'NAO ENTRAR NO DRE'
     WHEN ct.EQUIVALENCIADRE = 2 THEN '01 - RECEITA BRUTA'
     WHEN ct.EQUIVALENCIADRE = 3 THEN '02 - CUSTO ESPECIFICO'
     WHEN ct.EQUIVALENCIADRE = 4 THEN '03 - DESPESA OPERACIONAL'
     ELSE 'NAO ENTRAR NO DRE ---- VAZIO'
  END AS DRE,
  CASE
     WHEN AP.EFEITO = 4 THEN 'NOTA MATERIAL'
     WHEN AP.EFEITO = 3 THEN 'FISCAL'
     WHEN AP.EFEITO = 2 THEN 'GERENCIA'
     ELSE '---VERIFICAR'
  END AS EFEITO,
  evt.DS_EVENTO,
  CASE EXTRACT(Month from dthr_quitacao)
     WHEN 1 THEN 1
     WHEN 2 THEN 1
     WHEN 3 THEN 1
     WHEN 4 THEN 2
     WHEN 5 THEN 2
     WHEN 6 THEN 2
     WHEN 7 THEN 3
     WHEN 8 THEN 3
     WHEN 9 THEN 3
     WHEN 10 THEN 4
     WHEN 11 THEN 4
     WHEN 12 THEN 4
     ELSE 0
  END AS TRIMESTRE,
  CASE ap.TIPOCOBRANCA
      When 1 then 'Nenhum'
      When 2 then 'Boleto'
      When 3 then 'DCC'
      When 4 then 'DCO'
      When 5 then 'Carteira'
      When 6 then 'Depósito'
      When 7 then 'TEF'
      When 8 then 'Outros'
      When 9 then 'Serasa'
      When 10 then 'Jurídico'
      When 11 then 'Perdido'
      ELSE ''
  END AS TIPOCOBRANCA,
  ap.META,
  ap.NUMNFSE,
  EXTRACT(Year from ap.DATAPROCESSAMENTONFSE) as Ano_NotaEmitida,
     EXTRACT(Month from ap.DATAPROCESSAMENTONFSE) as Mes_NotaEmitida,
     EXTRACT(Day from ap.DATAPROCESSAMENTONFSE) as Dia_NotaEmitida

from
  fi_categoria ct
left outer join fi_categoria ctm
  on ct.id_catmae = ctm.id_categoria
inner join fi_agendparcitens api
  on ct.id_categoria = api.id_categoria
inner join fi_agendparcelas ap
  on ap.id_agendparcela = api.id_agendparcela
left outer join fi_conta co
  on ap.id_contaorigem = co.id_conta
left outer join fi_conta cd
  on ap.id_contadestino = cd.id_conta
inner join fi_agendamento a
  on ap.id_agendamento = a.id_agendamento
left outer join fi_favorecido f
  on a.id_favorecido = f.id_favorecido
left outer join fi_classificacao cla
  on api.id_classificacao = cla.id_classificacao
left outer join fi_classificacao subcla
  on cla.id_classmae = subcla.id_classificacao
left outer join fi_gruposfavorecido grupo
  on f.id_grupofavorecido = grupo.id_grupofavorecido
left outer join FI_EVENTO evt
  on ap.id_evento = evt.id_evento

where
-- AP.DTHR_QUITACAO > '2016-03-07 00:00'
  ap.DATACOMPETENCIA > '2017-10-01 00:00'
and ap.DATACOMPETENCIA < '2017-11-01 00:00'
-- and ap.DATACOMPETENCIA < '2016-03-02 23:00'
-- and AP.DTHR_QUITACAO < '2016-04-02 00:00'
 -- and FI_AGENDPARCELAS.DTHR_QUITACAO IS NOT NULL

--  and grupo.TIPO in ('C','T','Z')
--  and cla.DS_CLASSIFICACAO like '7-%'
-- AND FI_AGENDPARCELAS.DTHR_VENCIMENTO < '2011-01-01 00:00'
-- and ( co.ABREVIACAO is null or cd.ABREVIACAO is null )
