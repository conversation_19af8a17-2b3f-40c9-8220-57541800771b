select em.codigo                                                      AS codigoEmpresa,
       em.nome,
       em.tokensms,
       true as ativa,
       date_part('day', em.dataexpiracao) :: INTEGER                  AS diaExpiracao,
       date_part('month', em.dataexpiracao) :: INTEGER                AS mesExpiracao,
       date_part('year', em.dataexpiracao) :: INTEGER                 AS anoExpiracao,
       em.dataexpiracao,
       (select count(*)
        from clientesintetico c
        where c.empresa = em.codigo
          and (c.situacao IN ('AT') OR c.situacaocontrato IN ('VE'))) as clientesAtivos,
       CASE
           WHEN (em.dataexpiracao > CURRENT_DATE) :: TEXT = 'true'
               THEN 'VAI EXPIRAR'
           WHEN (em.dataexpiracao <= CURRENT_DATE) :: TEXT = 'true'
               THEN 'EXPIRADO'
           ELSE 'SEM DATA' END                                        AS situacao
FROM empresa em
