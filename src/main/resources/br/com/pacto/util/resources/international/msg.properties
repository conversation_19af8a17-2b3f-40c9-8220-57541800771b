# To change this template, choose Too<PERSON> | Templates
# and open the template in the editor.
comum.dadosIncorretos=Dados incorretos!
comum.dadosGravados=Dados gravados com Sucesso!

login.usuarioNaoPermitido=Usu\u00e1rio n\u00e3o permitido aqui!
login.usuarioInvalido=Usu\u00e1rio inv\u00e1lido!
login.senhaInvalida=Senha inv\u00e1lida!
login.usuariosenhaInvalida=Usu\u00e1rio ou senha inv\u00e1lidos!
login.sucesso=Logado com sucesso!
login.aguarde=Aguarde...
login.entrar=Entrar
login.facaLogin=Fa\u00e7a seu login
login.meLembrar=Lembrar-se de mim
login.esqueciSenha=Esqueceu sua senha?
mobile.token.invalido=Token Inv\u00e1lido!
validacao.nome=O 'nome' \u00e9 obrigat\u00f3rio!
validacao.empresa.naoencontrada=Empresa n\u00e3o encontrada
mobile.programanaoencontrado=Nenhum treino. Contacte seu Professor!
mobile.programaFichaNaoEncontrado=Nenhuma ficha encontrada para este treino. Efetue login novamente!

ficha.salva=Ficha salva com sucesso!
programa.salvo=Programa de treino salvo com sucesso!
atividade.adicionada.ficha=Atividade adicionada \u00e0 ficha com sucesso!
serie.salva=S\u00e9rie salva com sucesso!
serie.alterada=S\u00e9rie alterada com sucesso
serie.removida=S\u00e9rie removida com sucesso
atividade.ficha.salva=Atividade salva com sucesso!
atividade.removida=Atividade removida com sucesso!
ficha.predefinida.salva=Ficha pr\u00e9-definida salva!
validacao.ficha.nomeunico=Essa ficha n\u00e3o pode ser salva pois tem o mesmo nome de outra ficha deste programa!
mobile.serienaoencontrada=S\u00e9rie n\u00e3o encontrada!
cadastros.serie.adicionada=S\u00e9rie adicionada com sucesso!
cadastros.confirmar.exclusao=Confirma exclus\u00e3o?
validacao.nome.tipo=Os campos 'nome' e 'tipo' s\u00e3o obrigat\u00f3rios!
validacao.tipo=O 'tipo' \u00e9 obrigat\u00f3rio!
tabela.semregistros=Nenhum registro
validacao.fichaPredefinida.nomeunico=Essa ficha n\u00e3o pode ser salva pois tem o mesmo nome de outra ficha pr\u00e9-definida!
obrigatorio.nome=O 'nome' \u00e9 obrigat\u00f3rio!
obrigatorio.dataInicio='Data de in\u00edcio' \u00e9 obrigat\u00f3ria!
obrigatorio.diasPorSemana='Dias por semana' \u00e9 obrigat\u00f3rio!
obrigatorio.diasSemanaMaior7='Dias por semana' n\u00e3o pode ser maior do que sete!
obrigatorio.dataProximaRevisao='Data da pr\u00f3xima revis\u00e3o' \u00e9 obrigat\u00f3ria!
obrigatorio.dataTermino='Data de t\u00e9rmino' \u00e9 obrigat\u00f3ria!
concomitante.programa=Este aluno j\u00e1 possui um programa no per\u00edodo informado!
validacao.numeroFichas=O n\u00famero de fichas n\u00e3o pode ser maior do que o n\u00famero de dias por semana!
validacao.fichaDiaSemana=J\u00e1 existe uma ficha para este dia da semana!
nivel.alterado=N\u00edvel do aluno alterado com sucesso!
serieGravada=S\u00e9rie realizada gravada com sucesso!
mobile.usuarioinvalido=Usu\u00e1rio inv\u00e1lido!
alunoCadastrado=Aluno cadastrado no Treino com sucesso!
validacao.professor=\u00c9 necess\u00e1rio informar um professor para o aluno!
valorBadge=Valor do badge
obrigatorio.metodo=O 'm\u00e9todo de treino' \u00e9 obrigat\u00f3rio!
eventoalterado=Agenda alterada com sucesso!
eventoalteradonovoseventos=Agenda alterada, novos eventos foram criados com sucesso!
repetirAteObg=Se voc\u00ea deseja repetir esta disponibilidade, informe a data limite.
validacao.agenda.concomitante=J\u00e1 existe um evento neste hor\u00e1rio.
validacao.agenda.disponibilidade=N\u00e3o existe disponibilidade do professor na data informada.
validacao.agenda.tipo=O 'tipo do agendamento' \u00e9 obrigat\u00f3rio!
validacao.agenda.inicio=Informe o in\u00edcio do agendamento!
validacao.agenda.fim=Informe o fim do agendamento!
validacao.agenda.professor='Professor' \u00e9 obrigat\u00f3rio!
validacao.agenda.cliente=Informe o cliente do agendamento!
mobile.semtreinos=Nenhum treino realizado ainda!
validacao.tipoexecucao=O 'tipo de execu\u00e7\u00e3o' da ficha \u00e9 obrigat\u00f3rio!
consultornaodisponibilidade=Consultor n\u00e3o tem permiss\u00e3o para marcar disponibilidades!
mobile.nenhumagendamento=Nenhum agendamento!
validacao.perfil.existeUsuario=Este perfil n\u00e3o pode ser excluido pois j\u00e1 existe um usu\u00e1rio associado.
msg.comportamento=O 'comportamento' \u00e9 obrigat\u00f3rio!
msg.cor=A 'cor' \u00e9 obrigat\u00f3ria!
naoExisteDisponibilidade=N\u00e3o existe disponibilidade cadastrada para o hor\u00e1rio selecionado!
naoTemPermissaoIncluirTipos=Voc\u00ea n\u00e3o tem permiss\u00e3o para adicionar um agendamento com os tipos dispon\u00edveis.
naoTemPermissaoIncluirDisponibilidade=Voc\u00ea n\u00e3o tem permiss\u00e3o para adicionar uma disponibilidade.
naoTemPermissaoEditarDisponibilidade=Voc\u00ea n\u00e3o tem permiss\u00e3o para editar uma disponibilidade.
naoTemPermissaoEditarEvento=Voc\u00ea n\u00e3o tem permiss\u00e3o para editar o tipo de evento selecionado!
editarDisponibilidadeSemAgrupamento=Para editar uma disponibilidade retire o agrupamento.
obrigatorio.professorMontou=O 'professor' que montou o programa de treino \u00e9 obrigat\u00f3rio!
mobile.solicitacaoReagenda=Solicita\u00e7\u00e3o realizada com sucesso!
ajusteVazio=O 'ajuste' n\u00e3o pode ser vazio!
serieAlterada=S\u00e9rie alterada com sucesso!
validacaoalunoagendamento=O aluno j\u00e1 realizou o n\u00famero limite de agendamentos dentro do per\u00edodo de dias informado no Tipo de Evento.
validacao.perfilusuario=Perfil do usu\u00e1rio \u00e9 obrigat\u00f3rio!
disponibilidadeRemovida=Disponibilidade removida com sucesso!
validacao.agendamento.horaFimMenorHoraInicio=Hora final do agendamento n\u00e3o pode ser antes da hora inicial.
entidadePossuiRelacionamento=Este registro n\u00e3o pode ser exclu\u00eddo pois possui relacionamento com 
lembrete.agendamento=Lembrar: %s em %s 
lembrete.agendamento.novo=Novo compromisso: %s em %s 
lembrete.agendamento.alterado=Compromisso Alterado: %s para %s 
agendamento.confirmado=Aluno confirmou agendamento %s em %s
mobile.agendamentoConfirmado=Agendamento confirmado!
mobile.agendamentoCancelado=Agendamento cancelado!
agendamento.cancelado=Aluno cancelou agendamento %s em %s
programa.agendarRenovacao=Agende a renova\u00e7\u00e3o do seu Treino com o Professor
filaImpressao.nadaEnfileirado=Nada foi enfileirado
filaImpressao.fichaEnfileirada=Ficha enviada para impressora!
cliente.naoencontrado=Cliente n\u00e3o encontrado. Houve algum problema no carregamento do aluno pelo ZillyonWeb
permissaoIncluirPrograma=Voc\u00ea n\u00e3o tem permiss\u00e3o para incluir um programa de treino.
permissaoEditarPrograma=Voc\u00ea n\u00e3o tem permiss\u00e3o para editar um programa de treino.
habilitar=Habilitar
desabilitar=Desabilitar
modoFullScreen=Modo Full Screen
addAluno=Adicionar Aluno ao Treino (Ctrl+Shift+A)
wiki=Treino no Wiki Pacto
configuracoes=Configura\u00e7\u00f5es
agendarAluno=Agendar Aluno
acompanharAluno=Acompanhar Aluno
historicoExecAluno=Execu\u00e7\u00f5es
entrarModoAtendimento=Entrar Modo Atendimento
alternarModulos=Clique para Navegar a outro M\u00f3dulo
permissao=Voc\u00ea n\u00e3o tem permiss\u00e3o para essa a\u00e7\u00e3o.
programa.lembrarCompromisso=Lembre-se: malhar %s vezes por semana. Seu \u00faltimo Treino: %s
programa.comecarCompromisso=Lembre-se: voc\u00ea fez um compromisso de malhar %s vezes por semana


mensagem.validacao.duracaoPreDefinida=Este tipo de evento tem a dura\u00e7\u00e3o pr\u00e9-definida e n\u00e3o aceita agendamentos com dura\u00e7\u00e3o diferente.
mensagem.validacao.duracaoIntervalo=Este tipo de evento tem um intervalo de dura\u00e7\u00e3o e n\u00e3o aceita agendamentos com dura\u00e7\u00e3o fora deste intervalo.
programa.renovado=Programa renovado com sucesso!
programa.revisado=O programa foi marcado como revisado!
obrigatorio.professorCarteira=O 'professor' da carteira do aluno \u00e9 obrigat\u00f3rio!
obrigatorio.justificativa.revisao=\u00c9 obrigat\u00f3rio especificar a justificativa dessa Revis\u00e3o
obrigatorio.alterarProximaRevisao=\u00c9 obrigat\u00f3rio informar a data da pr\u00f3xima revis\u00e3o ou a data \u00e9 inv\u00e1lida!
obrigatorio.proximaRevisaoJaExiste=J\u00e1 existe uma previs\u00e3o de Revis\u00e3o nesta data
premium.usuarioapp.notfound= Usu\u00e1rio n\u00e3o encontrado
premium.usuarioapp.sucesso= Usu\u00e1rio salvo com sucesso
