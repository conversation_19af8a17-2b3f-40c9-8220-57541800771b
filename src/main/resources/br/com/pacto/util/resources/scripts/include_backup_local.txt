export PGPASSWORD=PWD_SGBD
FILE=$NOME_BANCO_DADOS`date +-%Y-%m-%d`.backup
echo -e "Gerando o Backup do banco... $NOME_CLIENTE `date +%Y-%m-%d` `date +"%T"`"
ssh -p $PSSH $ssh_user@$SERVIDOR -t "export PGPASSWORD=pactodb && mkdir -p '"$DIR_BACKUP"' && $PGDUMP --compress=9 --username USER_SGBD --host HOST_SGBD --port '"$PORTA"' --format custom --blobs '"$NOME_BANCO_DADOS"' --file '"$FILE"'"

echo -e "Backup concluido. Enviando o arquivo para a Pacto..."
scp -P $PSSH $ssh_user@$SERVIDOR:$FILE $DIRETORIO_LINUX
echo -e "Envio concluido. Movendo backup para o diretorio local: $DIR_BACKUP..."
ssh -p $PSSH $ssh_user@$SERVIDOR -t "mv '"$FILE"' '"$DIR_BACKUP"' && find '"$DIR_BACKUP"' -name '"*"'.backup -type f -mtime +6 -exec rm -f {} \;" 2> /dev/null

unset PGPASSWORD

FILE=$DIRETORIO_LINUX/$FILE