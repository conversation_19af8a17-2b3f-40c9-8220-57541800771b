(function () {
    window.onload = function () {
        var blipClient = new BlipChat();
        blipClient.withAppKey('BOT_APP_KEY')
            .withButton({"color": "#1a73e8", "icon": "https://wiki.pactosolucoes.com.br/midias/email_app/chat-bot.png"})
            .withCustomCommonUrl('BOT_URL')
            .withAccount({
                fullName: 'USER_NAME',
                email: 'USER_EMAIL',
                phoneNumber: 'USER_PHONE',
                city: 'USER_CITY',
                extras: {
                    token1: 'TOKEN1',
                    token2: 'TOKEN2',
                    urlValidateUser: 'URLVALIDATEUSER'
                }
            })
            .withEventHandler(BlipChat.LOAD_EVENT, function () {
                blipClient.sendMessage({
                    "type": "application/vnd.lime.chatstate+json",
                    "content": {
                        "state": "starting"
                    }
                });
            })
            .build();
    }
})();

