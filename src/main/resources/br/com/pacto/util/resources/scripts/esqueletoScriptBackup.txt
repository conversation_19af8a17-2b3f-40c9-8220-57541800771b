#!/bin/bash
# Script com a finalidade de gerar backup do banco de dados da academia em questao.
# Autor: Damas Neto / Waller Maciel
export LANG=pt_BR.UTF-8
export LC_ALL=pt_BR.UTF-8
NOME_CLIENTE="NCLIENTE_MINUSCULO"
SERVIDOR="HOST_NAME"
NOME_BANCO_DADOS="NBANCO"
DIRETORIO_LINUX="DIRETORIO_MIDIAS_BACKUP/"$NOME_CLIENTE

DIA=`date +%d | cut -c 2`
PORTA=PORTA_PG
DIR_FOTOS=/opt/zw-photos
DIR_BACKUP=/opt/backup
ssh_user="USER_SSH"
PSSH=PORTA_SSH

PGDUMP="CAMINHO_PGDUMP"

# Criando a pasta do cliente no dir de backup, caso nao exista
if [ ! -d $DIRETORIO_LINUX ]; then
	mkdir -p $DIRETORIO_LINUX
	echo "Diretorio $NOME_CLIENTE criado"
fi


INCLUDE_BACKUP_BANCO

INCLUDE_BACKUP_FOTOS


# Removendo o backups antigos
# So remove backups antigos se o backup de hoje for realizado com sucesso.
if [ -s $FILE ]; then	
    find $DIRETORIO_LINUX -maxdepth 1 -type f -mmin +1440 -delete
    echo -e "Realizado o Backup... $NOME_BANCO_DADOS `date +%Y-%m-%d` `date +"%T"`"
else
    echo "Backup nao realizado hoje. Arquivos antigos nao foram deletados."
fi

