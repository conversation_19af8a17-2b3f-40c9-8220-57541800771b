-- SCRIPT DE CRIA��O DAS TABELAS NECESS�RIAS PARA O SISTEMA "ZILLYON-CE". � NECESS�RIO J� HAVER RODADO O SCRIPT DE GERA��O DAS TABELAS DO SISTEMA "ZILLYON-WEB".
-- AUTOR : VICTOR HUGO TELES COSTA.
-- VERS�O 1.0

-- #### Table: formacontato ####

--DROP TABLE formacontato;

CREATE TABLE formacontato
(
	codigo serial NOT NULL,
	descricao character varying(50) NOT NULL,
	CONSTRAINT formacontato_pkey PRIMARY KEY (codigo)
);
-- Index: ch_formacontato_descricao

-- DROP INDEX ch_formacontato_descricao;

CREATE INDEX ch_formacontato_descricao
  ON formacontato
  USING btree
  (descricao);


-- #### Table: interessado ####

--DROP TABLE interessado;

CREATE TABLE interessado
(
  codigo serial NOT NULL,
  formacontato integer,
  comoconheceu character varying(50),
  quemindicou character varying(100),
  nomecliente character varying(100) NOT NULL,
  telefone character varying(15),
  telefonecomercial character varying(15),
  celular character varying(15),
  email character varying(50),
  observacao text,
  datacadastro timestamp without time zone NOT NULL,
  usuarioresponsavel integer NOT NULL,
  pessoa integer,
  queme character varying(20),
  CONSTRAINT interessado_pkey PRIMARY KEY (codigo),
  CONSTRAINT fk_interessado_formacontato FOREIGN KEY (formacontato)
      REFERENCES formacontato (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_interessado_usuario FOREIGN KEY (usuarioresponsavel)
      REFERENCES usuario (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_interessado_pessoa FOREIGN KEY (pessoa)
      REFERENCES pessoa (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_interessado_formacontato

-- DROP INDEX ch_interessado_formacontato;

CREATE INDEX ch_interessado_formacontato
  ON interessado
  USING btree
  (formacontato);

-- Index: ch_interessado_nomecliente

-- DROP INDEX ch_interessado_nomecliente;

CREATE INDEX ch_interessado_nomecliente
  ON interessado
  USING btree
  (nomecliente);

-- Index: ch_interessado_telefone

-- DROP INDEX ch_interessado_telefone;

CREATE INDEX ch_interessado_telefone
  ON interessado
  USING btree
  (telefone);

-- Index: ch_interessado_telefonecomercial

-- DROP INDEX ch_interessado_telefonecomercial;

CREATE INDEX ch_interessado_telefonecomercial
  ON interessado
  USING btree
  (telefonecomercial);

-- Index: ch_interessado_celular

-- DROP INDEX ch_interessado_celular;

CREATE INDEX ch_interessado_celular
  ON interessado
  USING btree
  (celular);

-- Index: ch_interessado_email

-- DROP INDEX ch_interessado_email;

CREATE INDEX ch_interessado_email
  ON interessado
  USING btree
  (email);

-- Index: ch_interessado_usuarioresponsavel

-- DROP INDEX ch_interessado_usuarioresponsavel;

CREATE INDEX ch_interessado_usuarioresponsavel
  ON interessado
  USING btree
  (usuarioresponsavel);

-- Index: ch_interessado_pessoa

-- DROP INDEX ch_interessado_pessoa;

CREATE INDEX ch_interessado_pessoa
  ON interessado
  USING btree
  (pessoa);

-- Index: ch_interessado_queme

-- DROP INDEX ch_interessado_queme;

CREATE INDEX ch_interessado_queme
  ON interessado
  USING btree
  (queme);


-- #### Table: tipovisita ####

--DROP TABLE tipovisita;

CREATE TABLE tipovisita
(
  codigo serial NOT NULL,
  nome character varying(100) NOT NULL,
  duracaomin integer NOT NULL,
  CONSTRAINT tipovisita_pkey PRIMARY KEY (codigo)
);


-- #### Table: agendavisita ####

--DROP TABLE agendavisita;

CREATE TABLE agendavisita
(
	codigo serial NOT NULL,
	tipovisita integer NOT NULL,
	datavisita timestamp without time zone NOT NULL,
	horariomarcado time without time zone NOT NULL,
	interessado integer NOT NULL,
	ambiente integer NOT NULL,
	duracaomin integer,
	observacao text,
	CONSTRAINT agendavisita_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_agendavisita_tipovisita FOREIGN KEY (tipovisita)
      REFERENCES tipovisita (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_agendavisita_interessado FOREIGN KEY (interessado)
      REFERENCES interessado (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_agendavisita_ambiente FOREIGN KEY (ambiente)
      REFERENCES ambiente (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);


-- Index: ch_agendavisita_tipovisita

-- DROP INDEX ch_agendavisita_tipovisita;

CREATE INDEX ch_agendavisita_tipovisita
  ON agendavisita
  USING btree
  (tipovisita);

-- Index: ch_agendavisita_datavisita

-- DROP INDEX ch_agendavisita_datavisita;

CREATE INDEX ch_agendavisita_datavisita
  ON agendavisita
  USING btree
  (datavisita);

-- Index: ch_agendavisita_interessado

-- DROP INDEX ch_agendavisita_interessado;

CREATE INDEX ch_agendavisita_interessado
  ON agendavisita
  USING btree
  (interessado);

-- Index: ch_agendavisita_ambiente

-- DROP INDEX ch_agendavisita_ambiente;

CREATE INDEX ch_agendavisita_ambiente
  ON agendavisita
  USING btree
  (ambiente);


ALTER TABLE agendavisita ADD COLUMN usuariocadastro INTEGER;
ALTER TABLE agendavisita ADD CONSTRAINT fk_agendavisita_usuariocadastro FOREIGN KEY (usuariocadastro)
      REFERENCES usuario (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT;
      
-- #### Table: eventointeresse ####

--DROP TABLE eventointeresse;

CREATE TABLE eventointeresse
(
	codigo serial NOT NULL,
	interessado integer NOT NULL,
	datainteresse timestamp without time zone,
	ambienteinteresse integer,
	observacao text,
	numeroconvidados integer,
	nomeevento character varying(50),
	horariomarcadohr character varying(2),
	horariomarcadomin character varying(2),
	situacao integer NOT NULL,
	dataprereserva timestamp without time zone,
	CONSTRAINT eventointeresse_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_eventointeresse_interessado FOREIGN KEY (interessado)
      REFERENCES interessado (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_eventointeresse_ambiente FOREIGN KEY (ambienteinteresse)
      REFERENCES ambiente (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_eventointeresse_interessado

-- DROP INDEX ch_eventointeresse_interessado;

CREATE INDEX ch_eventointeresse_interessado
  ON eventointeresse
  USING btree
  (interessado);

-- Index: ch_eventointeresse_datainteresse

-- DROP INDEX ch_eventointeresse_datainteresse;

CREATE INDEX ch_eventointeresse_datainteresse
  ON eventointeresse
  USING btree
  (datainteresse);

-- Index: ch_eventointeresse_ambienteinteresse

-- DROP INDEX ch_eventointeresse_ambienteinteresse;

CREATE INDEX ch_eventointeresse_ambienteinteresse
  ON eventointeresse
  USING btree
  (ambienteinteresse);

-- Index: ch_eventointeresse_nomeevento

-- DROP INDEX ch_eventointeresse_nomeevento;

CREATE INDEX ch_eventointeresse_nomeevento
  ON eventointeresse
  USING btree
  (nomeevento);

-- Index: ch_eventointeresse_situacao

-- DROP INDEX ch_eventointeresse_situacao;

CREATE INDEX ch_eventointeresse_situacao
  ON eventointeresse
  USING btree
  (situacao);


-- #### Table: conversa ####

--DROP TABLE conversa;

CREATE TABLE conversa
(
	codigo serial NOT NULL,
	descricao text NOT NULL,
	dataconversa timestamp without time zone,
	dataproximocontato timestamp without time zone,
	interessado integer NOT NULL,
	formacontato integer NOT NULL,
	eventointeresse integer,
	atendente integer NOT NULL,
	tipocontato integer,
	ultimocontato BOOLEAN,
	CONSTRAINT conversa_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_conversa_interessado FOREIGN KEY (interessado)
      REFERENCES interessado (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_conversa_formacontato FOREIGN KEY (formacontato)
      REFERENCES formacontato (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_conversa_eventointeresse FOREIGN KEY (eventointeresse)
      REFERENCES eventointeresse (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_conversa_usuario FOREIGN KEY (atendente)
	  REFERENCES usuario (codigo) MATCH SIMPLE
	  ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_conversa_interessado

-- DROP INDEX ch_conversa_interessado;

CREATE INDEX ch_conversa_interessado
  ON conversa
  USING btree
  (interessado);

-- Index: ch_conversa_formacontato

-- DROP INDEX ch_conversa_formacontato;

CREATE INDEX ch_conversa_formacontato
  ON conversa
  USING btree
  (formacontato);

-- Index: ch_conversa_usuario

-- DROP INDEX ch_conversa_usuario;

CREATE INDEX ch_conversa_usuario
  ON conversa
  USING btree
  (atendente);

-- Index: ch_conversa_eventointeresse

-- DROP INDEX ch_conversa_eventointeresse;

CREATE INDEX ch_conversa_eventointeresse
  ON conversa
  USING btree
  (eventointeresse);


-- #### Table: perfilevento ####

--DROP TABLE perfilevento;

CREATE TABLE perfilevento
(
	codigo serial NOT NULL,
	descricao character varying(100),
	datainicio timestamp without time zone,
	datatermino timestamp without time zone,
	textopadrao text NOT NULL,
	permiteoutrosbensconsumo boolean NOT NULL,
	permiteoutrosutensilios boolean NOT NULL,
	permiteoutrosbrinquedos boolean NOT NULL,
	permiteoutrosservicos boolean NOT NULL DEFAULT false,
	exigecontratoassinado boolean NOT NULL DEFAULT false,
	pagamentoprevio real,
	produto integer,
	CONSTRAINT perfilevento_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_perfilevento_produto FOREIGN KEY (produto)
	  REFERENCES produto (codigo) MATCH SIMPLE
	  ON UPDATE RESTRICT ON DELETE RESTRICT
);


-- Index: ch_perfilevento_descricao

-- DROP INDEX ch_perfilevento_descricao;

CREATE INDEX ch_perfilevento_descricao
  ON perfilevento
  USING btree
  (descricao);

-- Index: ch_perfilevento_datainicio

-- DROP INDEX ch_perfilevento_datainicio;

CREATE INDEX ch_perfilevento_datainicio
  ON perfilevento
  USING btree
  (datainicio);

-- Index: ch_perfilevento_datatermino

-- DROP INDEX ch_perfilevento_datatermino;

CREATE INDEX ch_perfilevento_datatermino
  ON perfilevento
  USING btree
  (datatermino);

-- Index: ch_perfilevento_produto

-- DROP INDEX ch_perfilevento_produto;

CREATE INDEX ch_perfilevento_produto
  ON perfilevento
  USING btree
  (produto);
  
-- #### Table: perfileventomodelocontrato ####

--DROP TABLE perfileventomodelocontrato;

CREATE TABLE perfileventomodelocontrato
(
	codigo serial NOT NULL,
	perfilevento integer NOT NULL,
	arquivo bytea NOT NULL,
	descricao text NOT NULL,
	nomearquivo character varying(50) NOT NULL,
	CONSTRAINT perfileventomodelocontrato_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_perfileventomodelocontrato_perfilevento FOREIGN KEY (perfilevento)
      REFERENCES perfilevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_perfileventomodelocontrato_perfilevento

-- DROP INDEX ch_perfileventomodelocontrato_perfilevento;

CREATE INDEX ch_perfileventomodelocontrato_perfilevento
  ON perfileventomodelocontrato
  USING btree
  (perfilevento);


-- #### Table: perfileventomodelocontratoimagem ####

--DROP TABLE perfileventomodelocontratoimagem;

CREATE TABLE perfileventomodelocontratoimagem
(
	codigo serial NOT NULL,
	perfileventomodelocontrato integer NOT NULL,
	arquivo bytea NOT NULL,
	nomearquivo character varying(50) NOT NULL,
	CONSTRAINT perfileventomodelocontratoimagem_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_perfileventomodelocontratoimagem_perfileventomodelocontrato FOREIGN KEY (perfileventomodelocontrato)
      REFERENCES perfileventomodelocontrato (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_perfileventomodelocontratoimagem_perfileventomodelocontrato

-- DROP INDEX ch_perfileventomodelocontratoimagem_perfileventomodelocontrato;

CREATE INDEX ch_perfileventomodelocontratoimagem_perfileventomodelocontrato
  ON perfileventomodelocontratoimagem
  USING btree
  (perfileventomodelocontrato);


-- #### Table: perfileventomodeloorcamento ####

--DROP TABLE perfileventomodeloorcamento;

CREATE TABLE perfileventomodeloorcamento
(
	codigo serial NOT NULL,
	perfilevento integer NOT NULL,
	arquivo bytea NOT NULL,
	descricao text NOT NULL,
	nomearquivo character varying(50) NOT NULL,
	CONSTRAINT perfileventomodeloorcamento_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_perfileventomodeloorcamento_perfilevento FOREIGN KEY (perfilevento)
      REFERENCES perfilevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_perfileventomodeloorcamento_perfilevento

-- DROP INDEX ch_perfileventomodeloorcamento_perfilevento;

CREATE INDEX ch_perfileventomodeloorcamento_perfilevento
  ON perfileventomodeloorcamento
  USING btree
  (perfilevento);


-- #### Table: perfileventomodeloorcamentoimagem ####

--DROP TABLE perfileventomodeloorcamentoimagem;

CREATE TABLE perfileventomodeloorcamentoimagem
(
	codigo serial NOT NULL,
	perfileventomodeloorcamento integer NOT NULL,
	arquivo bytea NOT NULL,
	nomearquivo character varying(50) NOT NULL,
	CONSTRAINT perfileventomodeloorcamentoimagem_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_perfileventomodeloorcamentoimagem_perfileventomodeloorcamento FOREIGN KEY (perfileventomodeloorcamento)
      REFERENCES perfileventomodeloorcamento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_perfileventomodeloorcamentoimagem_perfileventomodeloorcamento

-- DROP INDEX ch_perfileventomodeloorcamentoimagem_perfileventomodeloorcamento;

CREATE INDEX ch_perfileventomodeloorcamentoimagem_perfileventomodeloorcamento
  ON perfileventomodeloorcamentoimagem
  USING btree
  (perfileventomodeloorcamento);


-- #### Table: negociacaoevento ####

--DROP TABLE negociacaoevento;

CREATE TABLE negociacaoevento
(
	codigo serial NOT NULL,
	datacadastro timestamp without time zone NOT NULL,
	usuariocadastro integer NOT NULL,
	textopredefinido text NOT NULL,
	textolivre text,
	eventointeresse integer NOT NULL,
	situacao integer NOT NULL,
	dataevento timestamp without time zone NOT NULL,
	horarioinicial time without time zone,
	horariofinal time without time zone,
	horariofinalexibicao time without time zone,
	valortotal real,
	desconto real NOT NULL DEFAULT 0,
	tipodesconto integer,
	modelocontrato integer,
	CONSTRAINT negociacaoevento_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_negociacaoevento_usuario FOREIGN KEY (usuariocadastro)
      REFERENCES usuario (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_negociacaoevento_eventointeresse FOREIGN KEY (eventointeresse)
      REFERENCES eventointeresse (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_negociacaoevento_modelocontrato FOREIGN KEY (modelocontrato)
	  REFERENCES perfileventomodelocontrato (codigo) MATCH SIMPLE
	  ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_negociacaoevento_usuariocadastro

-- DROP INDEX ch_negociacaoevento_usuariocadastro;

CREATE INDEX ch_negociacaoevento_usuariocadastro
  ON negociacaoevento
  USING btree
  (usuariocadastro);

-- Index: ch_negociacaoevento_eventointeresse

-- DROP INDEX ch_negociacaoevento_eventointeresse;

CREATE INDEX ch_negociacaoevento_eventointeresse
  ON negociacaoevento
  USING btree
  (eventointeresse);

-- Index: ch_negociacaoevento_situacao

-- DROP INDEX ch_negociacaoevento_situacao;

CREATE INDEX ch_negociacaoevento_situacao
  ON negociacaoevento
  USING btree
  (situacao);

-- Index: ch_negociacaoevento_valortotal

-- DROP INDEX ch_negociacaoevento_valortotal;

CREATE INDEX ch_negociacaoevento_valortotal
  ON negociacaoevento
  USING btree
  (valortotal);

-- Index: ch_negociacaoevento_modelocontrato

-- DROP INDEX ch_negociacaoevento_modelocontrato;

CREATE INDEX ch_negociacaoevento_modelocontrato
  ON negociacaoevento
  USING btree
  (modelocontrato);


-- #### Table: negociacaoeventoimpressaocontrato ####

--DROP TABLE negociacaoeventoimpressaocontrato;

CREATE TABLE negociacaoeventoimpressaocontrato
(
	codigo serial NOT NULL,
	negociacaoevento integer NOT NULL,
	contrato integer NOT NULL,
	data timestamp without time zone NOT NULL,
	CONSTRAINT negociacaoeventoimpressaocontrato_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_negociacaoeventoimpressaocontrato_negociacaoevento FOREIGN KEY (negociacaoevento)
      REFERENCES negociacaoevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_negociacaoeventoimpressaocontrato_contrato FOREIGN KEY (contrato)
      REFERENCES perfileventomodelocontrato (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_negociacaoeventoimpressaocontrato_negociacaoevento

-- DROP INDEX ch_negociacaoeventoimpressaocontrato_negociacaoevento;

CREATE INDEX ch_negociacaoeventoimpressaocontrato_negociacaoevento
  ON negociacaoeventoimpressaocontrato
  USING btree
  (negociacaoevento);

-- Index: ch_negociacaoeventoimpressaocontrato_contrato

-- DROP INDEX ch_negociacaoeventoimpressaocontrato_contrato;

CREATE INDEX ch_negociacaoeventoimpressaocontrato_contrato
  ON negociacaoeventoimpressaocontrato
  USING btree
  (contrato);


-- #### Table: negociacaoeventoimpressaoorcamento ####

--DROP TABLE negociacaoeventoimpressaoorcamento;

CREATE TABLE negociacaoeventoimpressaoorcamento
(
	codigo serial NOT NULL,
	negociacaoevento integer NOT NULL,
	orcamento integer NOT NULL,
	data timestamp without time zone NOT NULL,
	CONSTRAINT negociacaoeventoimpressaoorcamento_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_negociacaoeventoimpressaoorcamento_negociacaoevento FOREIGN KEY (negociacaoevento)
      REFERENCES negociacaoevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_negociacaoeventoimpressaoorcamento_orcamento FOREIGN KEY (orcamento)
      REFERENCES perfileventomodeloorcamento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_negociacaoeventoimpressaoorcamento_negociacaoevento

-- DROP INDEX ch_negociacaoeventoimpressaoorcamento_negociacaoevento;

CREATE INDEX ch_negociacaoeventoimpressaoorcamento_negociacaoevento
  ON negociacaoeventoimpressaoorcamento
  USING btree
  (negociacaoevento);

-- Index: ch_negociacaoeventoimpressaoorcamento_orcamento

-- DROP INDEX ch_negociacaoeventoimpressaoorcamento_orcamento;

CREATE INDEX ch_negociacaoeventoimpressaoorcamento_orcamento
  ON negociacaoeventoimpressaoorcamento
  USING btree
  (orcamento);


-- #### Table: checklistevento ####

--DROP TABLE checklistevento;

CREATE TABLE checklistevento
(
	codigo serial NOT NULL,
	negociacaoevento integer,
	datachecklistevento timestamp without time zone,
	datacadastro timestamp without time zone NOT NULL,
	usuariocadastro integer NOT NULL,
	usuarioresponsavelabertura integer,
	usuarioresponsavelencerramento integer,
	dataabertura timestamp without time zone,
	dataencerramento timestamp without time zone,
	CONSTRAINT checklistevento_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_checklistevento_negociacaoevento FOREIGN KEY (negociacaoevento)
      REFERENCES negociacaoevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_checklistevento_usuario FOREIGN KEY (usuariocadastro)
      REFERENCES usuario (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_checklistevento_usuario02 FOREIGN KEY (usuarioresponsavelabertura)
      REFERENCES usuario (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_checklistevento_usuario03 FOREIGN KEY (usuarioresponsavelencerramento)
      REFERENCES usuario (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_checklistevento_negociacaoevento

-- DROP INDEX ch_checklistevento_negociacaoevento;

CREATE INDEX ch_checklistevento_negociacaoevento
  ON checklistevento
  USING btree
  (negociacaoevento);

-- Index: ch_checklistevento_usuariocadastro

-- DROP INDEX ch_checklistevento_usuariocadastro;

CREATE INDEX ch_checklistevento_usuariocadastro
  ON checklistevento
  USING btree
  (usuariocadastro);

-- Index: ch_checklistevento_usuarioresponsavelabertura

-- DROP INDEX ch_checklistevento_usuarioresponsavelabertura;

CREATE INDEX ch_checklistevento_usuarioresponsavelabertura
  ON checklistevento
  USING btree
  (usuarioresponsavelabertura);

-- Index: ch_checklistevento_usuarioresponsavelencerramento

-- DROP INDEX ch_checklistevento_usuarioresponsavelencerramento;

CREATE INDEX ch_checklistevento_usuarioresponsavelencerramento
  ON checklistevento
  USING btree
  (usuarioresponsavelencerramento);

-- Index: ch_checklistevento_dataabertura

-- DROP INDEX ch_checklistevento_dataabertura;

CREATE INDEX ch_checklistevento_dataabertura
  ON checklistevento
  USING btree
  (dataabertura);

-- Index: ch_checklistevento_dataencerramento

-- DROP INDEX ch_checklistevento_dataencerramento;

CREATE INDEX ch_checklistevento_dataencerramento
  ON checklistevento
  USING btree
  (dataencerramento);


-- #### Table: checklistproduto ####

--DROP TABLE checklistproduto;

CREATE TABLE checklistproduto
(
	codigo serial NOT NULL,
	checklistevento integer NOT NULL,
	produto integer NOT NULL,
	quantidadeorcamento integer NOT NULL,
	quantidadeabertura integer NOT NULL,
	quantidadeencerramento integer,
	total real,
	CONSTRAINT checklistproduto_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_checklistproduto_checklistevento FOREIGN KEY (checklistevento)
      REFERENCES checklistevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_checklistproduto_produto FOREIGN KEY (produto)
      REFERENCES produto (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_checklistproduto_checklistevento

-- DROP INDEX ch_checklistproduto_checklistevento;

CREATE INDEX ch_checklistproduto_checklistevento
  ON checklistproduto
  USING btree
  (checklistevento);

-- Index: ch_checklistproduto_produto

-- DROP INDEX ch_checklistproduto_produto;

CREATE INDEX ch_checklistproduto_produto
  ON checklistproduto
  USING btree
  (produto);


-- #### Table: tipolayout ####

--DROP TABLE tipolayout;

CREATE TABLE tipolayout
(
	codigo serial NOT NULL,
	descricao character varying(100) NOT NULL,
	ambiente integer NOT NULL,
	CONSTRAINT tipolayout_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_tipolayout_ambiente FOREIGN KEY (ambiente)
      REFERENCES ambiente (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_tipolayout_descricao

-- DROP INDEX ch_tipolayout_descricao;

CREATE INDEX ch_tipolayout_descricao
  ON tipolayout
  USING btree
  (descricao);

-- Index: ch_tipolayout_ambiente

-- DROP INDEX ch_tipolayout_ambiente;

CREATE INDEX ch_tipolayout_ambiente
  ON tipolayout
  USING btree
  (ambiente);


-- #### Table: produtolocacao ####

--DROP TABLE produtolocacao;

CREATE TABLE produtolocacao
(
	codigo serial NOT NULL,
	descricao character varying(50) NOT NULL,
	foto bytea,
	estoque integer,
	minimoestoque integer,
	tipo integer NOT NULL,
	rastreado boolean,
	valor real NOT NULL,
	CONSTRAINT produtolocacao_pkey PRIMARY KEY (codigo)
);

-- Index: ch_produtolocacao_descricao

-- DROP INDEX ch_produtolocacao_descricao;

CREATE INDEX ch_produtolocacao_descricao
  ON produtolocacao
  USING btree
  (descricao);

-- Index: ch_produtolocacao_tipo

-- DROP INDEX ch_produtolocacao_tipo;

CREATE INDEX ch_produtolocacao_tipo
  ON produtolocacao
  USING btree
  (tipo);

  
-- #### Table: produtolocacaopatrimonio ####

--DROP TABLE produtolocacaopatrimonio;

CREATE TABLE produtolocacaopatrimonio
(
  codigo character varying(30) NOT NULL,
  produtolocacao integer NOT NULL,
  descricao text,
  CONSTRAINT produtolocacaopatrimonio_pkey PRIMARY KEY (CODIGO),
  CONSTRAINT produtolocacaopatrimonio_produtolocacao_fkey FOREIGN KEY (produtolocacao)
      REFERENCES produtolocacao (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

-- Index: ch_produtolocacaopatrimonio_produtolocacao

-- DROP INDEX ch_produtolocacaopatrimonio_produtolocacao;

CREATE INDEX ch_produtolocacaopatrimonio_produtolocacao
  ON produtolocacaopatrimonio
  USING btree
  (produtolocacao);


-- #### Table: produtolocacaorastreamento ####

--DROP TABLE produtolocacaorastreamento;

CREATE TABLE produtolocacaorastreamento
(
  codigo serial NOT NULL,
  patrimonio VARCHAR(30) NOT NULL,
  eventointeresse integer NOT NULL,
  observacao VARCHAR(60), 
  CONSTRAINT produtolocacaorastreamento_pkey PRIMARY KEY (codigo),
  CONSTRAINT produtolocacaorastreamento_eventointeresse_fkey FOREIGN KEY (eventointeresse)
      REFERENCES eventointeresse (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT produtolocacaorastreamento_produtolocacaopatrimonio_fkey FOREIGN KEY (patrimonio)
      REFERENCES produtolocacaopatrimonio (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

-- Index: ch_produtolocacaorastreamento_patrimonio

-- DROP INDEX ch_produtolocacaorastreamento_patrimonio;

CREATE INDEX ch_produtolocacaorastreamento_patrimonio
  ON produtolocacaorastreamento
  USING btree
  (patrimonio);

-- Index: ch_produtolocacaorastreamento_eventointeresse

-- DROP INDEX ch_produtolocacaorastreamento_eventointeresse;

CREATE INDEX ch_produtolocacaorastreamento_eventointeresse
  ON produtolocacaorastreamento
  USING btree
  (eventointeresse);


-- #### Table: fornecedor ####

--DROP TABLE fornecedor;

CREATE TABLE fornecedor
(
	codigo serial NOT NULL,
	descricao character varying(100) NOT NULL,
	CONSTRAINT fornecedor_pkey PRIMARY KEY (codigo)
);

-- Index: ch_fornecedor_descricao

-- DROP INDEX ch_fornecedor_descricao;

CREATE INDEX ch_fornecedor_descricao
  ON fornecedor
  USING btree
  (descricao);


-- #### Table: servico ####

--DROP TABLE servico;

CREATE TABLE servico
(
	codigo serial NOT NULL,
	descricao character varying(100) NOT NULL,
	fornecedor integer NOT NULL,
	CONSTRAINT servico_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_servico_fornecedor FOREIGN KEY (fornecedor)
      REFERENCES fornecedor (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_servico_descricao

-- DROP INDEX ch_servico_descricao;

CREATE INDEX ch_servico_descricao
  ON servico
  USING btree
  (descricao);

-- Index: ch_servico_fornecedor

-- DROP INDEX ch_servico_fornecedor;

CREATE INDEX ch_servico_fornecedor
  ON servico
  USING btree
  (fornecedor);

-- #### Table: negociacaoeventoformapagamento ####

--DROP TABLE negociacaoeventoformapagamento;

CREATE TABLE negociacaoeventoformapagamento
(
	codigo serial NOT NULL,
	negociacaoevento integer,
	formapagamento integer,
	CONSTRAINT negociacaoeventoformapagamento_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_negevtformpa_negociacaoevento FOREIGN KEY (negociacaoevento)
      REFERENCES negociacaoevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_negevtformpag_formapagamento FOREIGN KEY (formapagamento)
      REFERENCES formapagamento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_negociacaoeventoformapagamento_negociacaoevento

-- DROP INDEX ch_negociacaoeventoformapagamento_negociacaoevento;

CREATE INDEX ch_negociacaoeventoformapagamento_negociacaoevento
  ON negociacaoeventoformapagamento
  USING btree
  (negociacaoevento);

-- Index: ch_negociacaoeventoformapagamento_formapagamento

-- DROP INDEX ch_negociacaoeventoformapagamento_formapagamento;

CREATE INDEX ch_negociacaoeventoformapagamento_formapagamento
  ON negociacaoeventoformapagamento
  USING btree
  (formapagamento);



-- #### Table: negociacaoeventoperfileventoproduto ####

--DROP TABLE negociacaoeventoperfileventoproduto;

CREATE TABLE negociacaoeventoperfileventoproduto
(
	codigo serial NOT NULL,
	negociacaoevento integer NOT NULL,
	produto integer NOT NULL,
	quantidade integer,
	valor real,
	CONSTRAINT negociacaoeventoperfileventoproduto_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_negevtprfevtprd_negociacaoevento FOREIGN KEY (negociacaoevento)
      REFERENCES negociacaoevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_negevtprfevtprd_produto FOREIGN KEY (produto)
      REFERENCES produto (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_negevtprfevtprd_negociacaoevento

-- DROP INDEX ch_negevtprfevtprd_negociacaoevento;

CREATE INDEX ch_negevtprfevtprd_negociacaoevento
  ON negociacaoeventoperfileventoproduto
  USING btree
  (negociacaoevento);

-- Index: ch_negevtprfevtprd_produto

-- DROP INDEX ch_negevtprfevtprd_produto;

CREATE INDEX ch_negevtprfevtprd_produto
  ON negociacaoeventoperfileventoproduto
  USING btree
  (produto);


-- #### Table: negociacaoeventoperfileventoprodutolocacao ####

--DROP TABLE negociacaoeventoperfileventoprodutolocacao;

CREATE TABLE negociacaoeventoperfileventoprodutolocacao
(
	codigo serial NOT NULL,
	negociacaoevento integer NOT NULL,
	produtolocacao integer NOT NULL,
	quantidade integer,
	valor real,
	textolivre text,
	extra boolean NOT NULL DEFAULT FALSE,
	desconto real DEFAULT 0,
	tipodesconto integer,
	CONSTRAINT negociacaoeventoperfileventoprodutolocacao_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_negevtprfevtprdloc_negociacaoevento FOREIGN KEY (negociacaoevento)
      REFERENCES negociacaoevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_negevtprfevtprdloc_produtolocacao FOREIGN KEY (produtolocacao)
      REFERENCES produtolocacao (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_negevtprfevtprdloc_negociacaoevento

-- DROP INDEX ch_negevtprfevtprdloc_negociacaoevento;

CREATE INDEX ch_negevtprfevtprdloc_negociacaoevento
  ON negociacaoeventoperfileventoprodutolocacao
  USING btree
  (negociacaoevento);

-- Index: ch_negevtprfevtprdloc_produtolocacao

-- DROP INDEX ch_negevtprfevtprdloc_produtolocacao;

CREATE INDEX ch_negevtprfevtprdloc_produtolocacao
  ON negociacaoeventoperfileventoprodutolocacao
  USING btree
  (produtolocacao);


-- #### Table: negociacaoeventoperfileventoservico ####

--DROP TABLE negociacaoeventoperfileventoservico;

CREATE TABLE negociacaoeventoperfileventoservico
(
	codigo serial NOT NULL,
	negociacaoevento integer NOT NULL,
	servico integer NOT NULL,
	quantidade integer,
	valor real,
	textolivre text,
	CONSTRAINT negociacaoeventoperfileventoservico_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_negevtprfevtserv_negociacaoevento FOREIGN KEY (negociacaoevento)
      REFERENCES negociacaoevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_negevtprfevtserv_servico FOREIGN KEY (servico)
      REFERENCES servico (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_negevtprfevtserv_negociacaoevento

-- DROP INDEX ch_negevtprfevtserv_negociacaoevento;

CREATE INDEX ch_negevtprfevtserv_negociacaoevento
  ON negociacaoeventoperfileventoservico
  USING btree
  (negociacaoevento);

-- Index: ch_negevtprfevtserv_servico

-- DROP INDEX ch_negevtprfevtserv_servico;

CREATE INDEX ch_negevtprfevtserv_servico
  ON negociacaoeventoperfileventoservico
  USING btree
  (servico);



-- #### Table: negociacaoperfilevento ####

--DROP TABLE negociacaoperfilevento;

CREATE TABLE negociacaoperfilevento
(
	codigo serial NOT NULL,
	negociacaoevento integer NOT NULL,
	perfilevento integer NOT NULL,
	CONSTRAINT negociacaoperfilevento_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_negprfevt_negociacaoevento FOREIGN KEY (negociacaoevento)
      REFERENCES negociacaoevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_negprfevt_perfilevento FOREIGN KEY (perfilevento)
      REFERENCES perfilevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_negociacaoperfilevento_negociacaoevento

-- DROP INDEX ch_negociacaoperfilevento_negociacaoevento;

CREATE INDEX ch_negociacaoperfilevento_negociacaoevento
  ON negociacaoperfilevento
  USING btree
  (negociacaoevento);

-- Index: ch_negociacaoperfilevento_perfilevento

-- DROP INDEX ch_negociacaoperfilevento_perfilevento;

CREATE INDEX ch_negociacaoperfilevento_perfilevento
  ON negociacaoperfilevento
  USING btree
  (perfilevento);


-- #### Table: perfileventocondicaopagamento ####

--DROP TABLE perfileventocondicaopagamento;

CREATE TABLE perfileventocondicaopagamento
(
	codigo serial NOT NULL,
	perfilevento integer NOT NULL,
	condicaopagamento integer NOT NULL,
	CONSTRAINT perfileventocondicaopagamento_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_prfevtcndpag_perfilevento FOREIGN KEY (perfilevento)
      REFERENCES perfilevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_prfevtcndpag_condicaopagamento FOREIGN KEY (condicaopagamento)
      REFERENCES condicaopagamento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_perfileventocondicaopagamento_perfilevento

-- DROP INDEX ch_perfileventocondicaopagamento_perfilevento;

CREATE INDEX ch_perfileventocondicaopagamento_perfilevento
  ON perfileventocondicaopagamento
  USING btree
  (perfilevento);

-- Index: ch_perfileventocondicaopagamento_condicaopagamento

-- DROP INDEX ch_perfileventocondicaopagamento_condicaopagamento;

CREATE INDEX ch_perfileventocondicaopagamento_condicaopagamento
  ON perfileventocondicaopagamento
  USING btree
  (condicaopagamento);


-- #### Table: perfileventoformapagamento ####

--DROP TABLE perfileventoformapagamento;

CREATE TABLE perfileventoformapagamento
(
	codigo serial NOT NULL,
	perfilevento integer NOT NULL,
	formapagamento integer NOT NULL,
	CONSTRAINT perfileventoformapagamento_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_prfevtformpag_perfilevento FOREIGN KEY (perfilevento)
      REFERENCES perfilevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_prfevtformpag_formapagamento FOREIGN KEY (formapagamento)
      REFERENCES formapagamento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_perfileventoformapagamento_perfilevento

-- DROP INDEX ch_perfileventoformapagamento_perfilevento;

CREATE INDEX ch_perfileventoformapagamento_perfilevento
  ON perfileventoformapagamento
  USING btree
  (perfilevento);

-- Index: ch_perfileventoformapagamento_formapagamento

-- DROP INDEX ch_perfileventoformapagamento_formapagamento;

CREATE INDEX ch_perfileventoformapagamento_formapagamento
  ON perfileventoformapagamento
  USING btree
  (formapagamento);


-- #### Table: perfileventoproduto ####

--DROP TABLE perfileventoproduto;

CREATE TABLE perfileventoproduto
(
	codigo serial NOT NULL,
	produto integer NOT NULL,
	quantidade integer,
	valor real,
	perfilevento integer NOT NULL,
	obrigatorio boolean,
	CONSTRAINT perfileventoproduto_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_perfileventoproduto_produto FOREIGN KEY (produto)
      REFERENCES produto (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_perfileventoproduto_perfilevento FOREIGN KEY (perfilevento)
      REFERENCES perfilevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_perfileventoproduto_produto

-- DROP INDEX ch_perfileventoproduto_produto;

CREATE INDEX ch_perfileventoproduto_produto
  ON perfileventoproduto
  USING btree
  (produto);

-- Index: ch_perfileventoproduto_valor

-- DROP INDEX ch_perfileventoproduto_valor;

CREATE INDEX ch_perfileventoproduto_valor
  ON perfileventoproduto
  USING btree
  (valor);

-- Index: ch_perfileventoproduto_perfilevento

-- DROP INDEX ch_perfileventoproduto_perfilevento;

CREATE INDEX ch_perfileventoproduto_perfilevento
  ON perfileventoproduto
  USING btree
  (perfilevento);


-- #### Table: arquivoambiente ####

--DROP TABLE arquivoambiente;

CREATE TABLE arquivoambiente
(
	codigo serial NOT NULL,
	fotoambiente bytea,
	tipoarquivo character varying(10),
	ambiente integer NOT NULL,
	CONSTRAINT arquivoambiente_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_arquivoambiente_ambiente FOREIGN KEY (ambiente)
      REFERENCES ambiente (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_arquivoambiente_ambiente

-- DROP INDEX ch_arquivoambiente_ambiente;

CREATE INDEX ch_arquivoambiente_ambiente
  ON arquivoambiente
  USING btree
  (ambiente);


-- #### Table: ambientehorariolocacao ####

--DROP TABLE ambientehorariolocacao;

CREATE TABLE ambientehorariolocacao
(
	codigo serial NOT NULL,
	ambiente integer NOT NULL,
	horarioturma integer NOT NULL,
	tipooperacao character varying(10),
	valordesconto real,
	percentualdesconto real,
	bloqueio boolean,
	CONSTRAINT ambientehorariolocacao_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_ambientehorariolocacao_ambiente FOREIGN KEY (ambiente)
      REFERENCES ambiente (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_ambientehorariolocacao_horarioturma FOREIGN KEY (horarioturma)
      REFERENCES horarioturma (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_ambientehorariolocacao_ambiente

-- DROP INDEX ch_ambientehorariolocacao_ambiente;

CREATE INDEX ch_ambientehorariolocacao_ambiente
  ON ambientehorariolocacao
  USING btree
  (ambiente);

-- Index: ch_ambientehorariolocacao_horarioturma

-- DROP INDEX ch_ambientehorariolocacao_horarioturma;

CREATE INDEX ch_ambientehorariolocacao_horarioturma
  ON ambientehorariolocacao
  USING btree
  (horarioturma);


-- #### Table: perfileventoambiente ####

--DROP TABLE perfileventoambiente;

CREATE TABLE perfileventoambiente
(
	codigo serial NOT NULL,
	perfilevento integer NOT NULL,
	ambiente integer NOT NULL,
	valor real,
	observacao text,
	nrmaximoconvidados integer,
	CONSTRAINT perfileventoambiente_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_perfileventoambiente_perfilevento FOREIGN KEY (perfilevento)
      REFERENCES perfilevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_perfileventoambiente_ambiente FOREIGN KEY (ambiente)
      REFERENCES ambiente (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_perfileventoambiente_perfilevento

-- DROP INDEX ch_perfileventoambiente_perfilevento;

CREATE INDEX ch_perfileventoambiente_perfilevento
  ON perfileventoambiente
  USING btree
  (perfilevento);

-- Index: ch_perfileventoambiente_ambiente

-- DROP INDEX ch_perfileventoambiente_ambiente;

CREATE INDEX ch_perfileventoambiente_ambiente
  ON perfileventoambiente
  USING btree
  (ambiente);

  
-- #### Table: perfileventoambientelayout ####

--DROP TABLE perfileventoambientelayout;

CREATE TABLE perfileventoambientelayout
(
	codigo serial NOT NULL,
	perfileventoambiente integer NOT NULL,
	arquivo bytea NOT NULL,
	descricao text NOT NULL,
	nomearquivo character varying(50) NOT NULL,
	CONSTRAINT perfileventoambientelayout_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_perfileventoambientelayout_perfileventoambiente FOREIGN KEY (perfileventoambiente)
      REFERENCES perfileventoambiente (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_perfileventoambientelayout_perfileventoambiente

-- DROP INDEX ch_perfileventoambientelayout_perfileventoambiente;

CREATE INDEX ch_perfileventoambientelayout_perfileventoambiente
  ON perfileventoambientelayout
  USING btree
  (perfileventoambiente);


-- #### Table: produtoinclusao ####

--DROP TABLE produtoinclusao;

CREATE TABLE produtoinclusao
(
	codigo serial NOT NULL,
	produto integer NOT NULL,
	quantidade integer,
	perfileventoambiente integer NOT NULL,
	CONSTRAINT produtoinclusao_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_produtoinclusao_produto FOREIGN KEY (produto)
      REFERENCES produto (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_produtoinclusao_perfileventoambiente FOREIGN KEY (perfileventoambiente)
      REFERENCES perfileventoambiente (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_produtoinclusao_produto

-- DROP INDEX ch_produtoinclusao_produto;

CREATE INDEX ch_produtoinclusao_produto
  ON produtoinclusao
  USING btree
  (produto);

-- Index: ch_produtoinclusao_perfileventoambiente

-- DROP INDEX ch_produtoinclusao_perfileventoambiente;

CREATE INDEX ch_produtoinclusao_perfileventoambiente
  ON produtoinclusao
  USING btree
  (perfileventoambiente);


-- #### Table: configuracaocentralevento ####

--DROP TABLE configuracaocentralevento;

CREATE TABLE configuracaocentralevento
(
	codigo serial NOT NULL,
	quantidadediasreservadointeressado integer,
	quantidadediasavisaragendavisita integer,
	CONSTRAINT configuracaocentralevento_pkey PRIMARY KEY (codigo)
);


-- #### Table: perfileventoprodutolocacao ####

--DROP TABLE perfileventoprodutolocacao;

CREATE TABLE perfileventoprodutolocacao
(
	codigo serial NOT NULL,
	valor real NOT NULL,
	perfilevento integer NOT NULL,
	produtolocacao integer NOT NULL,
	obrigatorio boolean NOT NULL,
	textolivre text,
	minimo integer NOT NULL,
	maximo integer NOT NULL,
	CONSTRAINT perfileventoprodutolocacao_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_perfileventoprodutolocacao_perfilevento FOREIGN KEY (perfilevento)
      REFERENCES perfilevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_perfileventoprodutolocacao_produtolocacao FOREIGN KEY (produtolocacao)
      REFERENCES produtolocacao (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_perfileventoprodutolocacao_perfilevento

-- DROP INDEX ch_perfileventoprodutolocacao_perfilevento;

CREATE INDEX ch_perfileventoprodutolocacao_perfilevento
  ON perfileventoprodutolocacao
  USING btree
  (perfilevento);

-- Index: ch_perfileventoprodutolocacao_produtolocacao

-- DROP INDEX ch_perfileventoprodutolocacao_produtolocacao;

CREATE INDEX ch_perfileventoprodutolocacao_produtolocacao
  ON perfileventoprodutolocacao
  USING btree
  (produtolocacao);


-- #### Table: perfileventosazonalidade ####

--DROP TABLE perfileventosazonalidade;

CREATE TABLE perfileventosazonalidade
(
	codigo serial NOT NULL,
	diasemana character varying(2),
	datainicio timestamp without time zone NOT NULL,
	datafim timestamp without time zone NOT NULL,
	tipooperacao integer,
	formacalculo integer,
	valor real,
	perfileventoambiente integer NOT NULL,
	CONSTRAINT perfileventosazonalidade_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_perfileventosazonalidade_perfileventoambiente FOREIGN KEY (perfileventoambiente)
      REFERENCES perfileventoambiente (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_perfileventosazonalidade_datainicio

-- DROP INDEX ch_perfileventosazonalidade_datainicio;

CREATE INDEX ch_perfileventosazonalidade_datainicio
  ON perfileventosazonalidade
  USING btree
  (datainicio);

-- Index: ch_perfileventosazonalidade_datafim

-- DROP INDEX ch_perfileventosazonalidade_datafim;

CREATE INDEX ch_perfileventosazonalidade_datafim
  ON perfileventosazonalidade
  USING btree
  (datafim);

-- Index: ch_perfileventosazonalidade_perfileventoambiente

-- DROP INDEX ch_perfileventosazonalidade_perfileventoambiente;

CREATE INDEX ch_perfileventosazonalidade_perfileventoambiente
  ON perfileventosazonalidade
  USING btree
  (perfileventoambiente);


-- #### Table: perfileventoservico ####

--DROP TABLE perfileventoservico;

CREATE TABLE perfileventoservico
(
	codigo serial NOT NULL,
	perfilevento integer NOT NULL,
	servico integer NOT NULL,
	valor real,
	textolivre text,
	minimo integer NOT NULL,
	maximo integer NOT NULL,
	CONSTRAINT perfileventoservico_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_perfileventoservico_perfilevento FOREIGN KEY (perfilevento)
      REFERENCES perfilevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_perfileventoservico_servico FOREIGN KEY (servico)
      REFERENCES servico (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_perfileventoservico_perfilevento

-- DROP INDEX ch_perfileventoservico_perfilevento;

CREATE INDEX ch_perfileventoservico_perfilevento
  ON perfileventoservico
  USING btree
  (perfilevento);

-- Index: ch_perfileventoservico_servico

-- DROP INDEX ch_perfileventoservico_servico;

CREATE INDEX ch_perfileventoservico_servico
  ON perfileventoservico
  USING btree
  (servico);


-- #### Table: tipoambiente ####

--DROP TABLE tipoambiente;

CREATE TABLE tipoambiente
(
	codigo serial NOT NULL,
	descricao character varying(50) NOT NULL,
	duracaominimahrs integer NOT NULL DEFAULT 1,
	qtdmaximareservasdia integer NOT NULL DEFAULT 0,
	tempoadicionalposteriormin integer NOT NULL DEFAULT 0,
	horarioinicial time without time zone NOT NULL,
	horariofinal time without time zone NOT NULL,
	CONSTRAINT tipoambiente_pkey PRIMARY KEY (codigo)
);

-- Index: ch_tipoambiente_descricao

-- DROP INDEX ch_tipoambiente_descricao;

CREATE INDEX ch_tipoambiente_descricao
  ON tipoambiente
  USING btree
  (descricao);


-- #### Table: negociacaoeventocontratosituacao ####

--DROP TABLE negociacaoeventocontratosituacao;

CREATE TABLE negociacaoeventocontratosituacao
(
	codigo serial NOT NULL,
	abreviacao character(2),
	descricao character varying(20),
	CONSTRAINT negociacaoeventocontratosituacao_pkey PRIMARY KEY (codigo)
);


-- #### Table: negociacaoeventocontrato ####

--DROP TABLE negociacaoeventocontrato;

CREATE TABLE negociacaoeventocontrato
(
  codigo serial NOT NULL,
  valordesconto real,
  valordescontoespecifico real,
  valordescontopercentual real,
  nomeevento character varying(50),
  somaproduto real,
  datapagamento timestamp without time zone,
  dataevento timestamp without time zone,
  pagarcomboleto boolean,
  responsavelcontrato integer NOT NULL,
  observacao text,
  valorfinal real,
  valorbasecalculo real,
  vigenciade timestamp without time zone,
  vigenciaate timestamp without time zone,
  vigenciaateajustada timestamp without time zone,
  situacao integer NOT NULL,
  perfilevento integer NOT NULL,
  pessoa integer NOT NULL,
  empresa integer NOT NULL,
  dividirprodutosnasparcelas boolean,
  eventointeresse integer,
  CONSTRAINT negociacaoeventocontrato_pkey PRIMARY KEY (codigo),
  CONSTRAINT fk_negociacaoeventocontrato_responsavelcontrato FOREIGN KEY (responsavelcontrato)
      REFERENCES usuario (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_negociacaoeventocontrato_situacao FOREIGN KEY (situacao)
      REFERENCES negociacaoeventocontratosituacao (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_negociacaoeventocontrato_perfilevento FOREIGN KEY (perfilevento)
      REFERENCES perfilevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_negociacaoeventocontrato_pessoa FOREIGN KEY (pessoa)
      REFERENCES pessoa (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_negociacaoeventocontrato_empresa FOREIGN KEY (empresa)
      REFERENCES empresa (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_negociacaoeventocontrato_evento FOREIGN KEY (eventointeresse)
      REFERENCES eventointeresse (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT    
);

-- Index: ch_negociacaoeventocontrato_responsavelcontrato

-- DROP INDEX ch_negociacaoeventocontrato_responsavelcontrato;

CREATE INDEX ch_negociacaoeventocontrato_responsavelcontrato
  ON negociacaoeventocontrato
  USING btree
  (responsavelcontrato);

-- Index: ch_negociacaoeventocontrato_situacao

-- DROP INDEX ch_negociacaoeventocontrato_situacao;
CREATE INDEX ch_negociacaoeventocontrato_situacao
  ON negociacaoeventocontrato
  USING btree
  (situacao);

-- Index: ch_negociacaoeventocontrato_perfilevento

-- DROP INDEX ch_negociacaoeventocontrato_perfilevento;
CREATE INDEX ch_negociacaoeventocontrato_perfilevento
  ON negociacaoeventocontrato
  USING btree
  (perfilevento);

-- Index: ch_negociacaoeventocontrato_pessoa

-- DROP INDEX ch_negociacaoeventocontrato_pessoa;
CREATE INDEX ch_negociacaoeventocontrato_pessoa
  ON negociacaoeventocontrato
  USING btree
  (pessoa);

-- Index: ch_negociacaoeventocontrato_empresa

-- DROP INDEX ch_negociacaoeventocontrato_empresa;
CREATE INDEX ch_negociacaoeventocontrato_empresa
  ON negociacaoeventocontrato
  USING btree
  (empresa);


-- #### Table: negociacaoeventocontratoparcelas ####

--DROP TABLE negociacaoeventocontratoparcelas;

CREATE TABLE negociacaoeventocontratoparcelas
(
  contrato integer NOT NULL,
  parcela integer NOT NULL,
  CONSTRAINT negociacaoeventocontratoparcelas_contrato_fkey FOREIGN KEY (contrato)
      REFERENCES negociacaoeventocontrato (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT negociacaoeventocontratoparcelas_parcela_fkey FOREIGN KEY (parcela)
      REFERENCES movparcela (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);



-- Index: ch_negociacaoeventocontratoparcelas_contrato

-- DROP INDEX ch_negociacaoeventocontratoparcelas_contrato;
CREATE INDEX ch_negociacaoeventocontratoparcelas_contrato
  ON negociacaoeventocontratoparcelas
  USING btree
  (contrato);

-- Index: ch_negociacaoeventocontratoparcelas_parcela

-- DROP INDEX ch_negociacaoeventocontratoparcelas_parcela;
CREATE INDEX ch_negociacaoeventocontratoparcelas_parcela
  ON negociacaoeventocontratoparcelas
  USING btree
  (parcela);

-- #### Table: negociacaoeventocontratopagamento ####

-- DROP TABLE negociacaoeventocontratopagamento;

CREATE TABLE negociacaoeventocontratopagamento
(
  contrato integer NOT NULL,
  movpagamento integer NOT NULL,
  CONSTRAINT negociacaoeventocontratopagamento_contrato_fkey FOREIGN KEY (contrato)
      REFERENCES negociacaoeventocontrato (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT negociacaoeventocontratopagamento_movpagamento_fkey FOREIGN KEY (movpagamento)
      REFERENCES movpagamento (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

-- #### Table: ambiente ####

ALTER TABLE ambiente
	ADD COLUMN tipoambiente integer,
	ADD COLUMN capacidademaximaconvidados integer,
	ADD CONSTRAINT fk_ambiente_tipoambiente FOREIGN KEY (tipoambiente)
      REFERENCES tipoambiente (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT;


-- #### Table: pessoa ####

ALTER TABLE pessoa
	ADD COLUMN senhaacesso character varying(30);


-- Valores m�nimos para tipos de ambiente
SELECT SETVAL('tipoambiente_codigo_seq', 4);

-- Valores m�nimos para tipos de visita
INSERT INTO tipovisita (codigo, nome, duracaomin)
	VALUES	(1, 'Visita R�pida', 15),
			(2, 'Visita Detalhada', 60),
			(3, 'Visita em Aberto', 0);
SELECT SETVAL('tipovisita_codigo_seq', 3);

-- Valores m�nimos para negociacaoeventocontratosituacao
SELECT SETVAL('negociacaoeventocontratosituacao_codigo_seq', 2);


-- Table: contratopadraoce

-- DROP TABLE contratopadraoce;

CREATE TABLE contratopadraoce
(
  codigo serial NOT NULL,
  textopadrao text NOT NULL,
  datacadastro timestamp without time zone NOT NULL,
  usuarioresponsavel integer NOT NULL,
  CONSTRAINT contratopadraoce_pkey PRIMARY KEY (codigo),
  CONSTRAINT fk_contratopadraoce_usuario FOREIGN KEY (usuarioresponsavel)
      REFERENCES usuario (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
)
WITH (
  OIDS=FALSE
);

-- Index: ch_contratopadraoce_usuarioresponsavel

-- DROP INDEX ch_contratopadraoce_usuarioresponsavel;

CREATE INDEX ch_contratopadraoce_usuarioresponsavel
  ON contratopadraoce
  USING btree
  (usuarioresponsavel);
  
  -- Table: contratopadraocetag

-- DROP TABLE contratopadraocetag;

CREATE TABLE contratopadraocetag
(
  codigo serial NOT NULL,
  contratopadraoce integer NOT NULL,
  tags character varying(1000) NOT NULL,
  CONSTRAINT contratopadraocetag_pkey PRIMARY KEY (codigo),
  CONSTRAINT fk_contratopadraocetag_contratopadraoce FOREIGN KEY (contratopadraoce)
      REFERENCES contratopadraoce (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
)
WITH (
  OIDS=FALSE
);

-- Index: ch_contratopadraocetag_contratopadraoce

-- DROP INDEX ch_contratopadraocetag_contratopadraoce;

CREATE INDEX ch_contratopadraocetag_contratopadraoce
  ON contratopadraocetag
  USING btree
  (contratopadraoce);





-- Table: negociacaoeventocontratopadraoce

-- DROP TABLE negociacaoeventocontratopadraoce;

CREATE TABLE negociacaoeventocontratopadraoce
(
  codigo serial NOT NULL,
  negociacaoevento integer,
  contratopadraoce integer,
  CONSTRAINT negociacaoeventocontratopadraoce_pkey PRIMARY KEY (codigo),
  CONSTRAINT fk_negevtocontpadce_contratopadraoce FOREIGN KEY (contratopadraoce)
      REFERENCES contratopadraoce (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_negevtocontpadce_negociacaoevento FOREIGN KEY (negociacaoevento)
      REFERENCES negociacaoevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
)
WITH (
  OIDS=FALSE
);

-- Index: ch_negociacaoeventocontratopadraoce_contratopadraoce

-- DROP INDEX ch_negociacaoeventocontratopadraoce_contratopadraoce;

CREATE INDEX ch_negociacaoeventocontratopadraoce_contratopadraoce
  ON negociacaoeventocontratopadraoce
  USING btree
  (contratopadraoce);

-- Index: ch_negociacaoeventocontratopadraoce_negociacaoevento

-- DROP INDEX ch_negociacaoeventocontratopadraoce_negociacaoevento;

CREATE INDEX ch_negociacaoeventocontratopadraoce_negociacaoevento
  ON negociacaoeventocontratopadraoce
  USING btree
  (negociacaoevento);


  -- Table: negociacaoeventotextopadraocetag

-- DROP TABLE negociacaoeventotextopadraocetag;

CREATE TABLE negociacaoeventotextopadraocetag
(
  codigo serial NOT NULL,
  negociacaoeventocontratopadraoce integer,
  tags character varying(1000),
  CONSTRAINT negociacaoeventotextopadraocetag_pkey PRIMARY KEY (codigo),
  CONSTRAINT fk_negevttextpadcetag_negevtcontrpadce FOREIGN KEY (negociacaoeventocontratopadraoce)
      REFERENCES negociacaoeventocontratopadraoce (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
)
WITH (
  OIDS=FALSE
);

-- Index: ch_negevttxtpadcetag_negociacaoeventocontratopadraoce

-- DROP INDEX ch_negevttxtpadcetag_negociacaoeventocontratopadraoce;

CREATE INDEX ch_negevttxtpadcetag_negociacaoeventocontratopadraoce
  ON negociacaoeventotextopadraocetag
  USING btree
  (negociacaoeventocontratopadraoce);
-- #### Table: negociacaoeventoperfileventoambiente ####

--DROP TABLE negociacaoeventoperfileventoambiente;

CREATE TABLE negociacaoeventoperfileventoambiente
(
	codigo serial NOT NULL,
	negociacaoevento integer NOT NULL,
	ambiente integer NOT NULL,
	tipolayout integer,
	valor real,
	nrmaximoconvidado integer,
	CONSTRAINT negociacaoeventoperfileventoambiente_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_negevtperfevtamb_negociacaoevento FOREIGN KEY (negociacaoevento)
      REFERENCES negociacaoevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_negevtperfevtamb_ambiente FOREIGN KEY (ambiente)
      REFERENCES ambiente (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_negevtperfevtamb_tipolayout FOREIGN KEY (tipolayout)
      REFERENCES perfileventoambientelayout (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_negociacaoeventoperfileventoambiente_negociacaoevento

-- DROP INDEX ch_negociacaoeventoperfileventoambiente_negociacaoevento;

CREATE INDEX ch_negociacaoeventoperfileventoambiente_negociacaoevento
  ON negociacaoeventoperfileventoambiente
  USING btree
  (negociacaoevento);

-- Index: ch_negociacaoeventoperfileventoambiente_ambiente

-- DROP INDEX ch_negociacaoeventoperfileventoambiente_ambiente;

CREATE INDEX ch_negociacaoeventoperfileventoambiente_ambiente
  ON negociacaoeventoperfileventoambiente
  USING btree
  (ambiente);

-- Index: ch_negociacaoeventoperfileventoambiente_tipolayout

-- DROP INDEX ch_negociacaoeventoperfileventoambiente_tipolayout;

CREATE INDEX ch_negociacaoeventoperfileventoambiente_tipolayout
  ON negociacaoeventoperfileventoambiente
  USING btree
  (tipolayout);

-- Index: ch_negociacaoeventoperfileventoambiente_valor

-- DROP INDEX ch_negociacaoeventoperfileventoambiente_valor;

CREATE INDEX ch_negociacaoeventoperfileventoambiente_valor
  ON negociacaoeventoperfileventoambiente
  USING btree
  (valor);


-- #### Table: negociacaoeventocondicaopagamento ####

--DROP TABLE negociacaoeventocondicaopagamento;

CREATE TABLE negociacaoeventocondicaopagamento
(
	codigo serial NOT NULL,
	negociacaoevento integer NOT NULL,
	condicaopagamento integer NOT NULL,
	CONSTRAINT negociacaoeventocondicaopagamento_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_negevtcondpag_negociacaoevento FOREIGN KEY (negociacaoevento)
      REFERENCES negociacaoevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_negevtcondpag_condicaopagamento FOREIGN KEY (condicaopagamento)
      REFERENCES condicaopagamento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_negociacaoeventocondicaopagamento_negociacaoevento

-- DROP INDEX ch_negociacaoeventocondicaopagamento_negociacaoevento;

CREATE INDEX ch_negociacaoeventocondicaopagamento_negociacaoevento
  ON negociacaoeventocondicaopagamento
  USING btree
  (negociacaoevento);

-- Index: ch_negociacaoeventocondicaopagamento_condicaopagamento

-- DROP INDEX ch_negociacaoeventocondicaopagamento_condicaopagamento;

CREATE INDEX ch_negociacaoeventocondicaopagamento_condicaopagamento
  ON negociacaoeventocondicaopagamento
  USING btree
  (condicaopagamento);

-- #### Table: negociacaoeventoperfilambienteprodutoincluso ####

--DROP TABLE negociacaoeventoperfilambienteprodutoincluso;

CREATE TABLE negociacaoeventoperfilambienteprodutoincluso
(
	codigo serial NOT NULL,
	produto integer NOT NULL,
	quantidade integer,
	negociacaoeventoperfileventoambiente integer NOT NULL,
	CONSTRAINT negevtprfambprdincl_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_negevtprfambprdtincl_produto FOREIGN KEY (produto)
      REFERENCES produto (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_negevtprfambprdtincl_negevtprfevtamb FOREIGN KEY (negociacaoeventoperfileventoambiente)
      REFERENCES negociacaoeventoperfileventoambiente (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Index: ch_negevtprfambprdincl_produto

-- DROP INDEX ch_negevtprfambprdincl_produto;

CREATE INDEX ch_negevtprfambprdincl_produto
  ON negociacaoeventoperfilambienteprodutoincluso
  USING btree
  (produto);

-- Index: ch_negevtprfambprdincl_quantidade

-- DROP INDEX ch_negevtprfambprdincl_quantidade;

CREATE INDEX ch_negevtprfambprdincl_quantidade
  ON negociacaoeventoperfilambienteprodutoincluso
  USING btree
  (quantidade);

-- Index: ch_negevtprfambprdincl_negevtprfevtamb

-- DROP INDEX ch_negevtprfambprdincl_negevtprfevtamb;

CREATE INDEX ch_negevtprfambprdincl_negevtprfevtamb 
  ON negociacaoeventoperfilambienteprodutoincluso
  USING btree
  (negociacaoeventoperfileventoambiente);

  -- Essa tabela guarda o documento modelo de tags
  -- � necess�rio que se rode o arquivo aux-00022.sql localizado na pasta de 
  -- scripts de atualiza��o, para um correto funcionamento do sistema.
CREATE TABLE documentomodelo
(
  id serial NOT NULL,
  descricao character varying(30),
  arquivo text,
  CONSTRAINT documentomodelo_pkey PRIMARY KEY (id)
)
WITH (
  OIDS=FALSE
);


insert into documentoModelo(descricao, arquivo) values ('Modelo das Tags', '<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head>
  
  <meta content="text/html; charset=ISO-8859-1" http-equiv="content-type">
  <title>Tags_Modelo</title>
<style type="text/css"> 
table, td{ 
border-style: solid; 
border-collapse: collapse;
border-width: 1px; 
font-family: Calibri;"
} 
</style> 
</head><body>
<big style="font-weight: bold; font-family: Calibri;"><big>Tags do
Contrato</big></big><br>

<span style="font-style: italic; font-family: Calibri;">Documento de exemplo que visa demonstrar como cada tag 
vai se comportar assim que o contrato for gerado.</span><br>

<br>

<table style="font-family: Calibri;">

  <tbody>
    <tr>
      <td width="35%">Tag: #<span>nomeevento </span></td>
      <td width="65%"><br>
#nomeevento</td>
    </tr>
    <tr>
      <td>Tag: #<span>ambiente </span></td>
      <td><br>
#ambiente</td>
    </tr>
    <tr>
      <td>Tag: #<span>layout </span></td>
      <td><br>
#layout</td>
    </tr>
    <tr>
      <td>Tag: #<span>servicoslistavertical </span></td>
      <td><br>
#servicoslistavertical</td>
    </tr>
    <tr>
      <td>Tag: #<span>bensdeconsumolistavertical</span></td>
      <td><br>
#bensdeconsumolistavertical</td>
    </tr>
    <tr>
      <td>Tag: #<span>utensilioslistavertical </span></td>
      <td><br>
#utensilioslistavertical</td>
    </tr>
    <tr>
      <td>Tag: #<span>brinquedoslistavertical </span></td>
      <td><br>
#brinquedoslistavertical</td>
    </tr>
    <tr>
      <td>Tag: #<span>condicaopagamento </span></td>
      <td><br>
#condicaopagamento</td>
    </tr>
    <tr>
      <td>Tag: #<span>data </span></td>
      <td><br>
#data</td>
    </tr>
    <tr>
      <td>Tag: #<span>horario </span></td>
      <td><br>
#horario</td>
    </tr>
    <tr>
      <td>Tag: #<span>texto </span></td>
      <td><br>
#texto</td>
    </tr>
    <tr>
      <td>Tag: #<span>observacao </span></td>
      <td><br>
#observacao</td>
    </tr>
    <tr>
      <td>Tag: #<span>total </span></td>
      <td><br>
#total</td>
    </tr>
    <tr>
      <td>Tag: #<span>valorfinal </span></td>
      <td><br>
#valorfinal</td>
    </tr>
    <tr>
      <td>Tag: #<span>valorparcela </span></td>
      <td><br>
#valorparcela</td>
    </tr>
    <tr>
      <td>Tag: #<span>nomecliente </span></td>
      <td><br>
#nomecliente</td>
    </tr>
    <tr>
      <td>Tag: #<span>rg </span></td>
      <td><br>
#rg</td>
    </tr>
    <tr>
      <td>Tag: #<span>cpf </span></td>
      <td><br>
#cpf</td>
    </tr>
    <tr>
      <td>Tag: #<span>endereco </span></td>
      <td><br>
#endereco</td>
    </tr>
    <tr>
      <td>Tag: #<span>bairro </span></td>
      <td><br>
#bairro</td>
    </tr>
    <tr>
      <td>Tag: #<span>cidade </span></td>
      <td><br>
#cidade</td>
    </tr>
    <tr>
      <td>Tag: #<span>telefones </span></td>
      <td><br>
#telefones</td>
    </tr>
    <tr>
      <td>Tag: #<span>tempoduracaoevento </span></td>
      <td><br>
#tempoduracaoevento</td>
    </tr>
    <tr>
      <td>Tag: #<span>qtdconvidados </span></td>
      <td><br>
#qtdconvidados</td>
    </tr>
    <tr>
      <td>Tag: #<span>parcelas </span></td>
      <td><br>
#parcelas</td>
    </tr>
    <tr>
      <td>Tag: #<span>vencimentoparcelas </span></td>
      <td><br>
#vencimentoparcelas</td>
    </tr>
    <tr>
      <td>Tag: #<span>formapagamento </span></td>
      <td><br>
#formapagamento</td>
    </tr>
    <tr>
      <td>Tag: #<span>cheque </span></td>
      <td><br>
#cheque</td>
    </tr>
    <tr>
      <td>Tag: #<span>compensacaodata </span></td>
      <td><br>
#compensacaodata</td>
    </tr>
    <tr>
      <td>Tag: #<span>eventohorainicial </span></td>
      <td><br>
#eventohorainicial</td>
    </tr>
    <tr>
      <td>Tag: #<span>eventohorafinal </span></td>
      <td><br>
#eventohorafinal</td>
    </tr>
    <tr>
      <td>Tag: #<span>servicoslistahorizontal </span></td>
      <td><br>
#servicoslistahorizontal</td>
    </tr>
    <tr>
      <td>Tag: #<span>bensdeconsumolistahorizontal </span></td>
      <td><br>
#bensdeconsumolistahorizontal</td>
    </tr>
    <tr>
      <td>Tag: #<span>utensilioslistahorizontal </span></td>
      <td><br>
#utensilioslistahorizontal</td>
    </tr>
    <tr>
      <td>Tag: #<span>brinquedoslistahorizontal </span></td>
      <td><br>
#brinquedoslistahorizontal</td>
    </tr>
  </tbody>
</table>

</body></html>');
