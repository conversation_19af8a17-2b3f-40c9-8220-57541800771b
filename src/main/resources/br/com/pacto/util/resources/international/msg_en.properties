# To change this template, choose Tools | Templates
# and open the template in the editor.
comum.dadosIncorretos=Incorrect data!
comum.dadosGravados=Data recorded successfully!

login.usuarioNaoPermitido=Access denied!
login.usuarioInvalido=Invalid user!
login.senhaInvalida=Invalid password!
login.sucesso=Login successfully!
login.aguarde=Please wait...
login.entrar=Enter
login.facaLogin=Login
login.meLembrar=Remember me
login.esqueciSenha=Forgot your password?
mobile.token.invalido=Invalid Token!
validacao.nome=The 'name' is mandatory!
validacao.empresa.naoencontrada=Company not found
mobile.programanaoencontrado=Training Program not found. Feedback teacher!

ficha.salva=Sheet saved successfully!
programa.salvo=Training Program saved successfully!
atividade.adicionada.ficha=Activity added to the sheet successfully!
serie.salva=Series saved successfully!
serie.alterada=Series changed successfully!
serie.removida=Series successfully removed!
atividade.ficha.salva=Activity saved successfully!
atividade.removida=Activity successfully removed!
ficha.predefinida.salva=Predefined sheet saved!
mobile.serienaoencontrada=Serie nof found. Try again!
cadastros.serie.adicionada=Series added successfully!
validacao.nome.tipo=The fields 'name' and 'type' are mandatory!
validacao.tipo=The 'type' is mandatory!
tabela.semregistros=No records
validacao.fichaPredefinida.nomeunico=This sheet can not be saved because it has the same name as another preset sheet!
validacao.ficha.nomeunico=This sheet can not be saved because it has the same name as another sheet this program!
obrigatorio.nome=The 'name' is mandatory!
obrigatorio.dataInicio='Start Date' is mandatory!
obrigatorio.diasPorSemana='Days of Week' is mandatory!
obrigatorio.diasSemanaMaior7='Days of Week' may not be greater than seven!
obrigatorio.dataProximaRevisao='Date of next revision' is mandatory!
obrigatorio.dataTermino='Completion Date' is mandatory!
concomitante.programa=This student already has a program in the period informed!
validacao.numeroFichas=The number of records can not be greater than the number of days a week!
validacao.fichaDiaSemana=There is already a record to this day of the week!
nivel.alterado=Level the student successfully changed!
serieGravada=Series held successfully saved!
mobile.usuarioinvalido=Invalid user!
alunoCadastrado=Student registered in Treino successfully!
validacao.professor=It is necessary to inform a teacher to the student!
valorBadge=Badge value
eventoalterado=Schedule changed successfully!
eventoalteradonovoseventos=Schedule changes, new events have been created successfully!
repetirAteObg=If you want to repeat this availability, enter the date.
validacao.agenda.concomitante=Already there is an event at this time.
validacao.agenda.disponibilidade=There is no availability of the teacher on the date notified.
validacao.agenda.tipo=O tipo do agendamento \u00e9 obrigat\u00f3rio!
validacao.agenda.inicio=Informe o in\u00edcio do agendamento!
validacao.agenda.fim=Informe o fim do agendamento!
validacao.agenda.professor=Professor \u00e9 obrigat\u00f3rio!
validacao.agenda.cliente=Informe o cliente do agendamento!
mobile.semtreinos=No workout done yet!
validacao.tipoexecucao=O tipo de execu\u00e7\u00e3o da ficha \u00e9 obrigat\u00f3rio!
consultornaodisponibilidade=Consultant does not have permission to flag availabilities!
mobile.nenhumagendamento=No scheduling!
validacao.perfil.existeUsuario=This profile can not be deleted because there is already an associated user.
msg.comportamento=The behavior is mandatory!
msg.cor=The color is mandatory!
naoExisteDisponibilidade=There is no availability for the selected registered time!
naoTemPermissaoIncluirTipos=You do not have permission to add a schedule to the types available.
naoTemPermissaoIncluirDisponibilidade=You do not have permission to add an availability.
naoTemPermissaoEditarDisponibilidade=You do not have permission to edit an availability.
naoTemPermissaoEditarEvento=You do not have permission to edit the selected event type!
editarDisponibilidadeSemAgrupamento=To edit a disponibility remove the grouping.
obrigatorio.professorMontou=The teacher who set up the training program is mandatory!
mobile.solicitacaoReagenda=Request was successful!
ajusteVazio=The 'fit' can not be empty!
serieAlterada=Series changed successfully!
validacaoalunoagendamento=The student has performed the number limit of schedules within the period of days entered the Event Type.
validacao.perfilusuario=User profile is mandatory!
disponibilidadeRemovida=Availability successfully removed!
validacao.agendamento.horaFimMenorHoraInicio=End time of the schedule can not be before the start time
entidadePossuiRelacionamento=This record can not be deleted because it has relationship with 
lembrete.agendamento=Remenber: %s on %s
agendamento.confirmado=Student confirmed schedule %s on %s
mobile.agendamentoConfirmado=Scheduling confirmed!
mobile.agendamentoCancelado=Scheduling canceled!
agendamento.cancelado=Student canceled schedule %s on %s
programa.agendarRenovacao=Schedule the renovation of your workout with the teacher
filaImpressao.nadaEnfileirado=Nothing was queued
filaImpressao.fichaEnfileirada=Data sent to the printer!
cliente.naoencontrado=Customer not found. There was a problem loading the student by ZillyonWeb
permissaoIncluirPrograma=You are not allowed to include a training program.
permissaoEditarPrograma=You do not have permission to edit a training program.
habilitar=Enable
desabilitar=Disable
modoFullScreen=Full Screen Mode
addAluno=Add Student to Treino
wiki=Treino in Wiki Pacto
configuracoes=Settings
agendarAluno=Schedule Student
acompanharAluno=Monitor Student
historicoExecAluno=Plays
entrarModoAtendimento=Enter Service Mode
alternarModulos=Click to Navigate to other Module
permissao=You do not have permission for this action.
#Lembre do seu compromisso: %s vezes por semana. \u00daltimo Treino: %s
programa.lembrarCompromisso=Remember: workout %s twice a week. His last training: %s

programa.comecarCompromisso=Remember: you have made a commitment to workout %s times a week
mobile.programaFichaNaoEncontrado=No record found for this workout. Log back!
programa.renovado=Program successfully renewed!
obrigatorio.metodo=The Trainning Method is required!
lembrete.agendamento.novo=New scheduling: %s on %s
lembrete.agendamento.alterado=Scheduling changed: %s to %s
programa.revisado=The program was marked as reviewed!
cadastros.confirmar.exclusao=Delete record?
obrigatorio.professorCarteira=The 'teacher' of the student portfolio is required!
obrigatorio.justificativa.revisao=Say what's the jusfity
obrigatorio.alterarProximaRevisao=Say what's when is the next revision or date is invalid!
obrigatorio.proximaRevisaoJaExiste=Revision already exists for this date
login.usuariosenhaInvalida=Invalid user or password!
