-- Descricao: Alteracao na chave estrangeira da tabela historicocontato.
-- Autor: <PERSON><PERSON><PERSON>
-- Data: 2010-12-06
-- Motivacao: Possibilitar ao sistema a exclusao de cliente excluindo os registros dele nesta tabela.
ALTER TABLE historicocontato
  DROP CONSTRAINT fk_historicocontato_cliente RESTRICT;
ALTER TABLE historicocontato
  ADD CONSTRAINT fk_historicocontato_cliente FOREIGN KEY (cliente)
    REFERENCES cliente(codigo)
    ON DELETE CASCADE
    ON UPDATE RESTRICT
    NOT DEFERRABLE;

-- Descricao: Alteracao na chave estrangeira da tabela historicocontato.
-- Autor: <PERSON><PERSON><PERSON>
-- Data: 2010-12-14
-- Motivacao: Possibilitar ao sistema a exclusao de cliente excluindo os registros dele nesta tabela.
ALTER TABLE vendaavulsa
  DROP CONSTRAINT fk_vendaavulsa_cliente RESTRICT;
ALTER TABLE vendaavulsa
  ADD CONSTRAINT fk_vendaavulsa_cliente FOREIGN KEY (cliente)
    REFERENCES cliente(codigo)
    ON DELETE CASCADE
    ON UPDATE RESTRICT
    NOT DEFERRABLE;

-- Descricao: Alteracao na chave estrangeira da tabela historicocontato.
-- Autor: Mauricio Lhoji Shiozawa
-- Data: 2010-12-14
-- Motivacao: correcao na tabela de cliente mensagem onde apresenta 2 chaves estrangeiras para a mesma tabela.
ALTER TABLE clientemensagem
  DROP CONSTRAINT fk_clientemensagem_parcela RESTRICT;
ALTER TABLE clientemensagem
  DROP CONSTRAINT fk_clientemensagem_movparcela RESTRICT;
ALTER TABLE clientemensagem
  ADD CONSTRAINT fk_clientemensagem_movparcela FOREIGN KEY (movparcela)
    REFERENCES movparcela(codigo)
    ON DELETE CASCADE
    ON UPDATE RESTRICT
    NOT DEFERRABLE;

-- Descricao: Criacao tabela ValidacaoLocalAcesso
-- Autor: Waller Maciel
-- Data: 2010-12-09
-- Motivacao: Controle de permissoes por local de acesso
CREATE TABLE validacaolocalacesso
(
   coletor integer NOT NULL,
   tipovalidacao smallint,
   chave integer,
   tipohorario smallint,
   horario integer,
   usuario integer NOT NULL,
   dataregistro timestamp without time zone NOT NULL,
   codigo serial NOT NULL,
   empresa integer NOT NULL,
   CONSTRAINT validacaolocalacesso_pkey PRIMARY KEY (codigo),
   CONSTRAINT fk_coletor FOREIGN KEY (coletor) REFERENCES coletor (codigo) ON UPDATE NO ACTION ON DELETE RESTRICT,
   CONSTRAINT fk_empresa FOREIGN KEY (empresa) REFERENCES empresa (codigo) ON UPDATE NO ACTION ON DELETE RESTRICT,
   CONSTRAINT fk_horario FOREIGN KEY (horario) REFERENCES horario (codigo) ON UPDATE NO ACTION ON DELETE RESTRICT,
   CONSTRAINT fk_usuario FOREIGN KEY (usuario) REFERENCES usuario (codigo) ON UPDATE NO ACTION ON DELETE RESTRICT
)
WITH (
  OIDS = FALSE
);

-- Descricao: Criacao de campo de tolerancia para entrada no horario da turma
-- Autor: Waller Maciel
-- Data: 2010-12-21
-- Motivacao: Criacao de tolerancia para acesso antes da hora inicio em HorarioTurma
alter table horarioTurma add toleranciaEntradaMinutos smallint default 0;

-- Descricao: Adicionar relacionamento entre as tabelas visita e usuario 
-- Autor: Joao Alcides
-- Data: 2010-12-23
-- Motivacao: Persistir o usuario que efetuou o cadastro da visita
ALTER TABLE agendavisita ADD COLUMN usuariocadastro INTEGER;
ALTER TABLE agendavisita ADD CONSTRAINT fk_agendavisita_usuariocadastro FOREIGN KEY (usuariocadastro)
      REFERENCES usuario (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT;
-- Descricao: Adicionar campos referentes a cheque caucao nas tabelas relacionadas a evento e negociacao.
-- Autor: Joao Alcides
-- Data: 2010-12-23
-- Motivacao: Adicionar a funcionalidade de pagar o cheque caucao no fechamento da negociacao que estiver relacionada
-- a um perfil de evento que exija o cheque caucao, alem de identificar como sera o calculo do valor do mesmo.
-- Tambem possibilitar que o sistema reconheca quando um evento ja teve o cheque caucao pago.
ALTER TABLE perfilevento ADD COLUMN exigechequecaucao boolean;
ALTER TABLE perfilevento ADD COLUMN tipovalorcaucao integer;
ALTER TABLE negociacaoevento ADD COLUMN caucaopago boolean;
-- Descricao: Adicionar campo que identifica se o evento ira reservar horario na agenda.
-- Autor: Joao Alcides
-- Data: 2010-12-23
-- Motivacao: Possibilitar que o sistema salve uma negociacao sem reservar um horario na agenda de disponibilidades.
ALTER TABLE negociacaoevento ADD COLUMN eventoreserva boolean not null default true;

-- Descricao: Gravar a foto que sera utilizada no modelo de mensagem de e-mail 
-- Autor: Pedro Y. Saito
-- Data: 2011-01-06
-- Motivacao: Disponibilizar a funcionalidade no cadastro da empresa de
--			  se adicionar a foto que sera anexada no modelo de mensagem de e-mail
ALTER TABLE empresa ADD COLUMN fotoemail bytea;
ALTER TABLE empresa ADD COLUMN alturafotoemail character varying(50);
ALTER TABLE empresa ADD COLUMN largurafotoemail character varying(50);

-- Descricao: Adicionar campo de data/hora para manipular tabela de rotatividade, no novo conceito baseado em historico de contrato
-- Autor: Waller Maciel
-- Data: 2010-12-27
-- Motivacao: Gravar a data/hora em que o registro de rotatividade foi inserido
ALTER TABLE rotatividadeanaliticodw
   ADD COLUMN dataalteracaoregistro timestamp without time zone;

-- Descricao: Grava um Fornecedor para varios servicos
-- Autor: Simonides
-- Data: 2011-01-10
-- Motivacao: Cadastrar Servicos tercerizados no cadastro de fornecedor
CREATE TABLE fornecedorservico
(
  codigo serial NOT NULL,
  fornecedor integer,
  servico integer,
  CONSTRAINT ch_codigo PRIMARY KEY (codigo),
  CONSTRAINT fk_fornecedor FOREIGN KEY (fornecedor)
      REFERENCES fornecedor (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_servico FOREIGN KEY (servico)
      REFERENCES servico (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Descricao: Adicionar colunas na tabela Fornecedor
-- Autor: Simonides
-- Data: 2011-01-11
-- Motivacao: Possuir mais informacoes sobre o fornecedor
ALTER TABLE fornecedor ADD COLUMN telefone character varying(20);
ALTER TABLE fornecedor ADD COLUMN endereco character varying(150);
ALTER TABLE fornecedor ADD COLUMN email character varying(30);
ALTER TABLE fornecedor ADD COLUMN cnpj character varying(15);
ALTER TABLE fornecedor ADD COLUMN contato character varying(20);
-- Descricao: Criacao da negociacao do evento para cadastrar fornecedores tercerizados.
-- Autor: Simonides
-- Data: 2011-01-10
-- Motivacao: Cadastra servicos tercerizado no orcamento detalhado.
CREATE TABLE negociacaoeventoservicotercerizado
(
  codigo serial NOT NULL,
  fornecedorservico integer,
  valor real,
  descricao character varying(50),
  CONSTRAINT cp_codigo PRIMARY KEY (codigo),
  CONSTRAINT fk_codfornecedorservico FOREIGN KEY (codigo)
      REFERENCES fornecedorservico (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

-- Descricao: Adicionar colunas na tabela negociacaoeventoservicotercerizado
-- Autor: Simonides
-- Data: 2011-01-13
-- Motivacao: relacionar a tabela negociacaoeventoservicotercerizado com negociacaoevento e adiconar o texto livre
ALTER TABLE negociacaoeventoservicotercerizado ADD COLUMN negociacaoevento integer;
ALTER TABLE negociacaoeventoservicotercerizado ADD CONSTRAINT fk_codnegeveterce FOREIGN KEY (negociacaoevento)
      REFERENCES negociacaoevento (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE negociacaoeventoservicotercerizado ADD COLUMN textolivre integer;

-- Descricao: Adicionar colunas na tabela negociacaoeventoimpressaocontrato e negociacaoeventoimpressaoorcamento
-- Autor: Simonides
-- Data: 2011-01-13
-- Motivacao: usar na pagina de emissao de documentos para registrar a data do ultimo envio
ALTER TABLE negociacaoeventoimpressaocontrato ADD COLUMN dataultimoenvio timestamp without time zone;
ALTER TABLE negociacaoeventoimpressaoorcamento ADD COLUMN dataultimoenvio timestamp without time zone;

-- Descricao: Adicionar campo ultimocontato na tabela conversa para identificar qual registro eh o ultimo contato valido
-- realizado com o cliente (updates necessarios para o caso de ja existirem conversas salvas em banco).
-- Autor: Joao Alcides
-- Data: 2011-01-18
-- Motivacao: Identificar qual contato eh o ultimo valido como conversa, para futura ordenacao na lista de prospects.
ALTER TABLE conversa ADD COLUMN ultimocontato BOOLEAN;
UPDATE conversa SET ultimocontato = FALSE;
UPDATE conversa SET ultimocontato = TRUE WHERE codigo IN (SELECT MAX(codigo) FROM conversa GROUP BY interessado, eventointeresse);

-- Descricao: Adicionar campos tipodesconto e desconto para controle de desconto de cada produto especifico da negociacao.
-- Autor: Joao Alcides
-- Data: 2011-01-18
-- Motivacao: Permitir ao usuario informar descontos particulares a cada produto do evento
ALTER TABLE negociacaoeventoperfileventoprodutolocacao ADD COLUMN desconto real DEFAULT 0;
ALTER TABLE negociacaoeventoperfileventoprodutolocacao ADD COLUMN  tipodesconto integer;

-- Descricao: Criacao de campos dataprereserva, tipocontato nas tabelas eventointeresse, conversa respectivamente. Insercao 
-- de forma de contato 'Operacao do sistema' e atualizacao dos dados na tabela conversa.  
-- Autor: Joao Alcides
-- Data: 2011-01-19
-- Motivacao: (dataprereserva) Adicionar ao sistema a Pre-reserva. (tipocontato)Possibilitar registrar na tabela 
-- conversa todo o clico de vida do evento. (Update na tabela conversa) setar as conversas ja cadastradas em banco
-- com o codigo do tipo de contato Conversa. (insert) Inserir um tipo novo de forma de contato
ALTER TABLE eventointeresse ADD COLUMN dataprereserva date;
ALTER TABLE conversa ADD COLUMN tipocontato integer;
UPDATE conversa SET tipocontato = 1;
INSERT INTO formacontato(codigo, descricao) VALUES (50,'Operacao do Sistema');

-- Descricao: Adiciona campo para configuracao da URL da Agenda no Google Agenda
-- Autor: Waller Maciel
-- Data: 2011-01-20
-- Motivacao: Personalizar a agenda do Google para cada Sistema
ALTER TABLE configuracaosistema
   ADD COLUMN urlGoogleAgenda character varying(2048);
--Desricao: Faltava coluna pessoa em Log (Pedro Yoda)
-- Autor: Waller Maciel
-- Data: 2011-01-20
--Motivacao correcao em coluna que faltava para o Log funcionar
alter table log add column pessoa integer;

-- Descricao: Os Campos da tabela de endereco devem ter o mesmo tamanho do BD CEP
-- Autor: Mauricio Shiozawa
-- Data: 2011-01-21
-- Motivacao: Correcao de erro ao gravar endereco no cadastro do cliente
ALTER TABLE endereco ALTER COLUMN bairro TYPE character varying(100);
ALTER TABLE endereco ALTER COLUMN endereco TYPE character varying(100);
ALTER TABLE endereco ALTER COLUMN complemento TYPE character varying(100);
ALTER TABLE cidade ALTER COLUMN nome TYPE character varying(50);
ALTER TABLE cidade ALTER COLUMN nomesemacento TYPE character varying(50);
ALTER TABLE estado ALTER COLUMN descricao TYPE character varying(50);

-- Descricao: Criacao de tabela para relacionar um recibo de pagamento ao evento pago (ou parcela de evento paga).  
-- Autor: Joao Alcides
-- Data: 2011-01-26
-- Motivacao: relacionar os recibos de pagamento gerados ao evento que foi pago.
CREATE TABLE negociacaoeventocontratorecibo
(
  contrato integer NOT NULL,
  recibo integer NOT NULL,
  CONSTRAINT negociacaoeventocontratorecibo_contrato_fkey FOREIGN KEY (contrato)
      REFERENCES negociacaoeventocontrato (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT negociacaoeventocontratorecibo_recibo_fkey FOREIGN KEY (recibo)
      REFERENCES recibopagamento (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

-- Descricao: Criacao do campo "diasparabloqueio" em configuracaosistema, que sera utilizado para verificacao
--			  no acesso ao sistema 
-- Autor: Pedro Yoda
-- Data: 2011-01-27
-- Motivacao: Bloquear o sistema
ALTER TABLE configuracaosistema ADD COLUMN diasparabloqueio integer;
ALTER TABLE configuracaosistema ALTER COLUMN diasparabloqueio SET STORAGE PLAIN;
ALTER TABLE configuracaosistema ALTER COLUMN diasparabloqueio SET DEFAULT (-1);
UPDATE configuracaosistema SET diasparabloqueio=-1 WHERE codigo=1;

-- Descricao: Remocao dos campos e tabela relacionada ao cheque caucao do sistema. 
-- Autor: Joao Alcides
-- Data: 2011-02-01
-- Motivacao: Retirar do Central de Eventos o cheque caucao, da forma com que esta implementado hoje.
ALTER TABLE perfilevento DROP COLUMN exigechequecaucao;
ALTER TABLE perfilevento DROP COLUMN chequecaucao;
ALTER TABLE negociacaoevento DROP COLUMN caucaopago;
DROP TABLE negociacaoeventochequecaucao;

-- Descricao: reestruturar a parte de matricula aluno horario turma
-- Autor: Mauricio Lhoji Shiozawa
-- Data: 2011-01-21
-- Motivacao: Corrigir o controle de matriculas de alunos nas turma
ALTER TABLE horarioturma DROP COLUMN nrAlunoMatriculado;
DROP TABLE matriculaalunohorarioturma;
CREATE TABLE matriculaalunohorarioturma (
    codigo SERIAL,
    empresa INTEGER NOT NULL,
    pessoa INTEGER NOT NULL,
    contrato INTEGER NOT NULL,
    datainicio DATE NOT NULL,
    datafim DATE NOT NULL,
    horarioturma INTEGER NOT NULL,
    PRIMARY KEY (codigo),
    CONSTRAINT fk_matriculaalunohorarioturma_empresa FOREIGN KEY (empresa)
        REFERENCES empresa (codigo) MATCH SIMPLE
        ON UPDATE NO ACTION ON DELETE NO ACTION,
    CONSTRAINT fk_matriculaalunohorarioturma_pessoa FOREIGN KEY (pessoa)
        REFERENCES pessoa (codigo) MATCH SIMPLE
        ON UPDATE NO ACTION ON DELETE CASCADE,
    CONSTRAINT fk_matriculaalunohorarioturma_contrato FOREIGN KEY (contrato)
        REFERENCES contrato (codigo) MATCH SIMPLE
        ON UPDATE NO ACTION ON DELETE CASCADE,
    CONSTRAINT fk_matriculaalunohorarioturma_horarioturma FOREIGN KEY (horarioturma)
        REFERENCES horarioturma (codigo) MATCH SIMPLE
        ON UPDATE NO ACTION ON DELETE RESTRICT
);

-- Descricao: Melhoria na tela de negociacao de evento, onde sera possivel adicionar varios
-- ambientes. (Ref. a atividade 007 da Central de Eventos)
-- Autor: Pedro Y. Saito
-- Data: 2011-02-03
-- Motivacao: Permitir adicionar varios ambientes a negociacao
ALTER TABLE negociacaoeventoperfileventoambiente ADD COLUMN horarioinicial time without time zone;
ALTER TABLE negociacaoeventoperfileventoambiente ALTER COLUMN horarioinicial SET STORAGE PLAIN;
ALTER TABLE negociacaoeventoperfileventoambiente ADD COLUMN horariofinal time without time zone;
ALTER TABLE negociacaoeventoperfileventoambiente ALTER COLUMN horariofinal SET STORAGE PLAIN;
ALTER TABLE negociacaoeventoperfileventoambiente ADD COLUMN horariofinalexibicao time without time zone;
ALTER TABLE negociacaoeventoperfileventoambiente ALTER COLUMN horariofinalexibicao SET STORAGE PLAIN;

-- Descricao: Criar a tabela que guardara o documento modelo com todas as tags
-- Autor: Joao Alcides
-- Data: 2011-02-03
-- Motivacao: Permitir que o sistema possua um documento que exibe todas as tags e 
-- seu comportamento depois de substituidas
-- o insert desse documento esta em um script separado em aux-00022.sql nesta mesma pasta!
CREATE TABLE documentomodelo
(
  id serial NOT NULL,
  descricao character varying(30),
  arquivo text,
  CONSTRAINT documentomodelo_pkey PRIMARY KEY (id)
)
WITH (
  OIDS=FALSE
);

-- Descricao: Criar uma foreign key para eventointeresse na tabela agendavisita 
-- Autor: Joao Alcides
-- Data: 2011-02-04
-- Motivacao: Associar uma visita a um interesse de evento 
ALTER TABLE agendavisita ADD COLUMN evento INTEGER;
ALTER TABLE agendavisita ADD CONSTRAINT fk_agendavisita_evento FOREIGN KEY (evento)
      REFERENCES eventointeresse (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Descricao: Tabela que ira armazenar os dados do encerramento do evento 
-- Autor: Pedro Y. Saito
-- Data: 2011-02-10
-- Motivacao: Armazenar a justificativa do encerramento do evento 
CREATE TABLE negociacaoeventoencerramento
(
  negociacaoevento integer,
  usuario integer,
  data timestamp without time zone,
  observacoes text,
  CONSTRAINT codigonegociacaoevento_fk FOREIGN KEY (negociacaoevento)
      REFERENCES negociacaoevento (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT codigousuario_fk FOREIGN KEY (usuario)
      REFERENCES usuario (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
)
WITH (
  OIDS=FALSE
);

-- Descricao: Nova tabela para registrar as Liberacoes de Acesso
-- Autor: Ulisses
-- Data: 2010-12-29
-- Motivacao: Liberar o acesso rapido de(visitante/Tercerizados/Alunos que nao sabem o numero da matricula etc..)
CREATE TABLE LiberacaoAcesso (
  codigo SERIAL not null,
  pessoa INTEGER,
  tipoLiberacao INTEGER NOT NULL,
  sentido VARCHAR(1) NOT NULL,
  localacesso INTEGER NOT NULL,
  coletor INTEGER NOT NULL,
  usuario INTEGER NOT NULL,
  empresa INTEGER NOT NULL,
  datahora TIMESTAMP WITHOUT TIME ZONE NOT NULL,
  justificativa VARCHAR(500),
  dthrjustificativa TIMESTAMP WITHOUT TIME ZONE,
  CONSTRAINT "codigoLibAcesPK" PRIMARY KEY("codigo"),
  CONSTRAINT "clienteFK" FOREIGN KEY ("pessoa")
    REFERENCES "public"."pessoa"("codigo")
    ON DELETE NO ACTION
    ON UPDATE NO ACTION
    NOT DEFERRABLE,
  CONSTRAINT "coletorFK" FOREIGN KEY ("coletor")
    REFERENCES "public"."coletor"("codigo")
    ON DELETE NO ACTION
    ON UPDATE NO ACTION
    NOT DEFERRABLE,
  CONSTRAINT "localacessoFK" FOREIGN KEY ("localacesso")
    REFERENCES "public"."localacesso"("codigo")
    ON DELETE NO ACTION
    ON UPDATE NO ACTION
    NOT DEFERRABLE,
  CONSTRAINT "usuarioFK" FOREIGN KEY ("usuario")
    REFERENCES "public"."usuario"("codigo")
    ON DELETE NO ACTION
    ON UPDATE NO ACTION
    NOT DEFERRABLE,
  CONSTRAINT "fk_cliente_empresa" FOREIGN KEY ("empresa")
    REFERENCES "public"."empresa"("codigo")
    ON DELETE RESTRICT
    ON UPDATE RESTRICT
    NOT DEFERRABLE
 ) WITHOUT OIDS;
-- Setar a situacao de todos os acessos para "RV_LIBACESSOAUTORIZADO"
update ACESSOCLIENTE set situacao = 'RV_LIBACESSOAUTORIZADO';
update cliente set uaSituacao = 'RV_LIBACESSOAUTORIZADO';
-- Apagar as mensagem do tipo "Catraca", pois o novo ZillyonAcesso espera esta
-- mensagem como sendo um texto puro, e nao no formato HTML como estava.
update clienteMensagem set mensagem = 'Mensagem nao definida' where tipoMensagem = 'AC' and mensagem like '<!%';

-- Descricao: Adicionar campo de descricao para telefone de cliente
-- Autor: Carla Pereira
-- Data: 09-02-11
-- Motivacao: Permitir que seja acrescentado telefones de parentes, vizinho e seja descrito de quem eh.
alter table telefone add column descricao varchar(20);

-- Descricao: Alteracoes necessarias para o zillyonacessoweb
-- Autor: Ulisses
-- Data: 2010-12-29
-- Motivacao: Liberar o acesso rapido de(visitante/Tercerizados/Alunos que nao sabem o numero da matricula etc..)
alter table LocalAcesso add column PedirSenhaLibParaCadaAcesso BOOLEAN;
alter table acessocliente add column MeioIdentificacaoEntrada smallint;
alter table acessocliente add column MeioIdentificacaoSaida smallint;
alter table acessocolaborador add column MeioIdentificacaoEntrada smallint;
alter table acessocolaborador add column MeioIdentificacaoSaida smallint;
-- Descricao: Novos campos nas tabelas de Cliente e Colaborador para gravar o ultimo acesso.
-- Autor: Ulisses
-- Data: 2011-02-10
-- Motivacao: Melhor a performance para registrar o ultimo acesso da pessoa.
alter table cliente drop column uasituacao;
alter table cliente drop column uasentido;
alter table cliente add uaCodigo integer;
alter table cliente add constraint FK_Cliente_AcessoCli foreign key (uaCodigo) references AcessoCliente(Codigo);
alter table colaborador drop column uadata;
alter table colaborador drop column uasentido;
alter table colaborador add uaCodigo integer;
alter table colaborador add constraint FK_Colaborador_AcessoColab foreign key (uaCodigo) references AcessoColaborador(Codigo);
-- Descricao: Excluir os campos "permiteacessohorarioturma" e "permiteacessohorarioplano" da tabela de plano
-- Autor: Ulisses
-- Data: 2011-02-11
-- Motivacao: Estes campos nao serao mais necessarios, pois as validacoes de acesso por Coletor ira contemplar isto.
alter table plano drop column permiteacessohorarioturma;
alter table plano drop column permiteacessohorarioplano;

-- Descricao: Criando o campo realizado em agendavisita
-- Autor: Pedro Y. Saito
-- Data: 14/02/2011
-- Motivacao: Flag para se definir quando uma visita foi realizada, onde "false" visita nao realizada
--            "true" visita realizada
ALTER TABLE agendavisita ADD COLUMN realizada boolean;
ALTER TABLE agendavisita ALTER COLUMN realizada SET STORAGE PLAIN;
ALTER TABLE agendavisita ALTER COLUMN realizada SET DEFAULT false;

-- Descricao: Povoar o ultimo acesso do cliente e do colaborador
-- Autor: Ulisses
-- Data: 2011-02-10
-- Motivacao: Melhor a performance para registrar o ultimo acesso da pessoa.
update cliente
set uaCodigo = (select max(codigo) from acessoCliente ac
                where cliente.codigo = ac.cliente) where uaCodigo is null;
update colaborador
set uaCodigo = (select max(codigo) from acessocolaborador ac
                where colaborador.codigo = ac.colaborador) where uaCodigo is null;

-- Descricao: Criando o campo horariofinalexibicao em tipoambiente
-- Autor: Joao Alcides
-- Data: 15/02/2011
-- Motivacao: Permitir que o tipo de ambiente tenha registros de horario final em dia posterior (ate as 6 da manha) como
-- ja existe no ambiente. 
ALTER TABLE tipoambiente  ADD COLUMN horariofinalexibicao time without time zone NOT NULL DEFAULT '06:00:00';
ALTER TABLE tipoambiente  ALTER COLUMN horariofinalexibicao DROP DEFAULT;

-- Descricao: Criacao da tabela que ira gerenciar os cheques caucao e os creditos
-- Autor: Pedro Y. Saito
-- Data: 15/02/2011
-- Motivacao: Gerenciar os cheques caucao e os creditos de bebidas
CREATE TABLE negociacaoeventocaucaoecredito
(
  codigo serial NOT NULL,
  dataLancamento timestamp without time zone,
  tipo integer,
  observacao text,
  valor real,
  dataQuitacao timestamp without time zone,
  creditodocliente integer,
  tipoQuitacao integer,
  negociacaoevento integer NOT NULL
);

-- Descricao: Troca o tipo do campo texto livre da tabela negociacaoeventoservicotercerizado
-- Autor: Joao Alcides
-- Data: 18/02/2011
-- Motivacao: Corrigir erro de criacao da tabela. 
ALTER TABLE negociacaoeventoservicotercerizado DROP COLUMN textolivre;
ALTER TABLE negociacaoeventoservicotercerizado ADD COLUMN textolivre text;

-- Descricao: Troca o campo que referencia a tabela fornecedorservico na tabela negociacaoeventoservicotercerizado
-- Autor: Joao Alcides
-- Data: 23/02/2011
-- Motivacao: Corrigir erro de criacao da tabela. 
ALTER TABLE negociacaoeventoservicotercerizado DROP CONSTRAINT fk_codfornecedorservico;
ALTER TABLE negociacaoeventoservicotercerizado
  ADD CONSTRAINT fk_codfornecedorservico FOREIGN KEY (fornecedorservico)
    REFERENCES fornecedorservico(codigo) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION;

-- Descricao: Apaga a coluna queme da tabela interessado e cria a coluna queme na tabela eventointeresse. (Transfere os dados
-- ja persistidos para a nova coluna antes).
-- Autor: Joao Alcides
-- Data: 24/02/2011
-- Motivacao: Persistir este dado na tabela de evento e nao na tabela de interessado.
ALTER TABLE eventointeresse ADD COLUMN queme varchar(30);
UPDATE eventointeresse
SET queme = (SELECT interessado.queme FROM interessado WHERE eventointeresse.interessado = interessado.codigo);
ALTER TABLE interessado DROP COLUMN queme;

-- Descricao: Incluido constraint para validar exclusoes e alteracoes em usuario e colaborador
-- Autor: Carla Pereira
-- Data: 25/02/2011
-- Motivacao: Evitar erros ao buscar usuarios, pois mostrava mensagem de dados nao encontrados colaborador
update usuario set colaborador = null where colaborador = 0;
ALTER TABLE usuario
  ADD CONSTRAINT fk_usuario_colaborador FOREIGN KEY (colaborador)
    REFERENCES colaborador(codigo)
    ON DELETE CASCADE
    ON UPDATE RESTRICT
    NOT DEFERRABLE;

-- Descricao: Estorno de clientes deve excluirPlano os registros relacionados destas tabelas tbm
-- Autor: Mauricio Lhoji Shiozawa
-- Data: 2011-03-01
-- Motivacao: Estorno de cliente
ALTER TABLE acessocliente
  DROP CONSTRAINT clienteFK RESTRICT;
ALTER TABLE acessocliente
  ADD CONSTRAINT clienteFK FOREIGN KEY (cliente)
    REFERENCES cliente(codigo)
    ON DELETE CASCADE
    ON UPDATE RESTRICT
    NOT DEFERRABLE;
ALTER TABLE historicovinculo
  DROP CONSTRAINT fk_historicovinculo_cliente RESTRICT;
ALTER TABLE historicovinculo
  ADD CONSTRAINT fk_historicovinculo_cliente FOREIGN KEY (cliente)
    REFERENCES cliente(codigo)
    ON DELETE CASCADE
    ON UPDATE RESTRICT
    NOT DEFERRABLE;
ALTER TABLE vinculo
  DROP CONSTRAINT fk_vinculo_cliente RESTRICT;
ALTER TABLE vinculo
  ADD CONSTRAINT fk_vinculo_cliente FOREIGN KEY (cliente)
    REFERENCES cliente(codigo)
    ON DELETE CASCADE
    ON UPDATE RESTRICT
    NOT DEFERRABLE;
ALTER TABLE questionariocliente
  DROP CONSTRAINT fk_questionariocliente_cliente RESTRICT;
ALTER TABLE questionariocliente
  ADD CONSTRAINT fk_questionariocliente_cliente FOREIGN KEY (cliente)
    REFERENCES cliente(codigo)
    ON DELETE CASCADE
    ON UPDATE RESTRICT
    NOT DEFERRABLE;

-- Descricao: Excluir campos "permiteacessoalunosoutraempresa" da tabela de Empresa e "uadata" da tabela Cliente
-- Autor: Ulisses
-- Data: 18/02/2011
-- Motivacao: O campo deixou de ser necessario com a implementacao de novos recursos
alter table empresa drop column  permiteacessoalunosoutraempresa;
alter table cliente drop column uadata;

-- Descricao: Novo campo "desativado" na tabela "Coletor"
-- Autor: Ulisses
-- Data: 09/03/2011
-- Motivacao: Desativar um coletor, para que o mesmo nao seja exibido no ZillyonAcesso.
alter table coletor add COLUMN desativado boolean;

-- Descricao: Indice para otimizacao de consulta em AcessoCliente
-- Autor: Waller Maciel
-- Data: 10/03/2011
-- Motivacao: Necessidade de otimizar as consultas nas tabelas de acesso
CREATE INDEX ind_acesso_cliente
  ON acessocliente
  USING btree
  (cliente, dthrentrada);
CREATE INDEX ind_acesso_colaborador
  ON acessocolaborador
  USING btree
  (colaborador, dthrentrada);
CREATE INDEX ind_acesso_cliente_codigo
   ON acessocliente USING btree (codigo DESC NULLS FIRST, cliente ASC NULLS LAST);

-- Descricao: Adicionar campos tipodesconto e desconto para controle de desconto de cada ambiente especifico da negociacao.
-- Autor: Joao Alcides
-- Data: 2011-03-10
-- Motivacao: Permitir ao usuario informar descontos particulares a cada ambiente do evento
ALTER TABLE negociacaoeventoperfileventoambiente ADD COLUMN desconto real DEFAULT 0;
ALTER TABLE negociacaoeventoperfileventoambiente ADD COLUMN  tipodesconto integer;

-- Descricao: Apaga definicao de layout existente, pois estas deverao ser refeitas
-- Autor: Waller Maciel
-- Data: 11/03/2011
-- Motivacao: Unifiicar as  panels do BI do CRM com os mesmos panels do ZillyonWeb
DELETE from definirlayout;

-- Descricao: Adicionar campo perfilinteresse na tabela eventointeresse, chave estrangeira com a tabela perfilevento.
-- Autor: Joao Alcides
-- Data: 2011-03-11
-- Motivacao: Permitir ao usuario informar o perfil de evento que lhe interessa ja no cadastro inicial.
ALTER TABLE eventointeresse ADD COLUMN perfilinteresse int;
ALTER TABLE eventointeresse ADD CONSTRAINT fk_agendavisita_perfilinteresse FOREIGN KEY (perfilinteresse)
      REFERENCES perfilevento (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Descricao: correcoes de casas decimais em tabelas relevantes
-- Autor: Mauricio Lhoji Shiozawa
-- Data: 2011-02-08
-- Motivacao: problemas no sistema relativos a casas decimais muito pequenas
UPDATE movproduto SET precounitario = to_number (to_char(coalesce(precounitario), '99999D99'), '999999D99'),
		      totalfinal = to_number (to_char(coalesce(totalfinal), '99999D99'), '999999D99'),
                      valordesconto = to_number (to_char(coalesce(valordesconto), '99999D99'), '999999D99');
UPDATE movimentocontacorrentecliente SET saldoatual = to_number (to_char(coalesce(saldoatual), '99999D99'), '999999D99'),
                                         valor = to_number (to_char(coalesce(valor), '99999D99'), '999999D99');

-- Descricao: Script de criacao da tabela de desconto em renovacao antecipada
-- Autor: Mauricio Lhoji Shiozawa
-- Data: 2011-02-08
-- Motivacao: Estruturacao do processo de desconto em renovacao antecipada
CREATE TABLE descontorenovacao (
    codigo SERIAL,
    desconto INTEGER NOT NULL,
    tipointervalo CHARACTER VARYING(2) NOT NULL,
    intervalode INTEGER NOT NULL,
    intervaloate INTEGER NOT NULL,
    tipodesconto CHARACTER VARYING(2) NOT NULL,
    valor DOUBLE PRECISION NOT NULL,
    tipojustificativa INTEGER NOT NULL,
    PRIMARY KEY (codigo),
    CONSTRAINT fk_descontorenovacao_desconto FOREIGN KEY (desconto)
        REFERENCES desconto (codigo) MATCH SIMPLE
        ON UPDATE NO ACTION ON DELETE CASCADE
);

-- Descricao: Script de atualizaqcao das tabelas referentes a Mensagem notificacao
-- Autor: Pedro Y. Saito
-- Data: 2011-03-15
-- Motivacao: Script de refatoracao das tabelas referentes a Mensagem notificacao
-- que agora e Mala Direta [Mailing]
UPDATE permissao SET tituloapresentacao='7.10 - Mala Direta', nomeentidade='MalaDireta' WHERE nomeentidade='MensagemNotificacao';
ALTER TABLE mensagemnotificacao RENAME TO maladireta;
ALTER TABLE mensagemnotificacao_codigo_seq RENAME TO maladireta_codigo_seq;
ALTER TABLE notificacaoenviada RENAME TO maladiretaenviada;
ALTER TABLE maladiretaenviada RENAME mensagemnotificacao  TO maladireta;
ALTER TABLE historicocontato RENAME mensagemnotificacao  TO maladireta;

-- Descricao: Apaga definicao de layout existente, pois estas deverao ser refeitas
-- Autor: Waller Maciel
-- Data: 11/03/2011
-- Motivacao: Unifiicar as  panels do BI do CRM com os mesmos panels do ZillyonWeb
DELETE from definirlayout;

-- Descricao: Cria a tabela de regras de autorizacao
-- Autor: Joao Felipe
-- Data: 22/03/2011
-- Motivacao: Refazer o modelo de autorizacao do sistema para permitir autorizacao para operacoes de tela e nao manipulacoes de entidades
CREATE TABLE autorizacaoregras
(
   codigo integer NOT NULL, 
   perfilacesso integer NOT NULL, 
   entidade integer DEFAULT 0, 
   operacao character(10) DEFAULT '', 
   funcionalidade integer DEFAULT 0, 
   descricao text DEFAULT '', 
   CONSTRAINT pkey PRIMARY KEY (codigo) USING INDEX TABLESPACE pg_default, 
   CONSTRAINT pkey_perfilacesso FOREIGN KEY (perfilacesso) REFERENCES perfilacesso (codigo) ON UPDATE NO ACTION ON DELETE NO ACTION
) 
WITH (
  OIDS = FALSE
)
;

-- Descricao: alterando a tabela de plano para uso do desconto antecipado.
-- Autor: Mauricio Lhoji Shiozawa
-- Data: 2011-03-16
-- Motivacao: Estruturacao do processo de desconto em renovacao antecipada
ALTER TABLE plano ADD COLUMN descontoAntecipado int;

-- Descricao: Muda o tipo da chave prim�ria da tabela autorizacaoregras de int para serial
-- Autor: Jo�o Alcides
-- Data: 23/03/2011
-- Motivacao: Determinar que a chave prim�ria desta tabela seja gerada pelo BD.
ALTER TABLE autorizacaoregras DROP CONSTRAINT pkey;
ALTER TABLE autorizacaoregras DROP COLUMN codigo;
ALTER TABLE autorizacaoregras ADD COLUMN codigo SERIAL;
ALTER TABLE autorizacaoregras ADD CONSTRAINT pkey PRIMARY KEY (codigo) USING INDEX TABLESPACE pg_default;

-- Descricao: Adicionando o campo situacao na tabela ambiente, onde seu valor padrao
-- sera 1 que referente a situacao ativa descrita no enumerador "SituacaoAmbiente.java"
-- Autor: Pedro Y. Saito
-- Data: 31/03/2011
-- Motivacao: Possibilitar que um determinado ambiente possa ser inativo, para que nao se
-- possa ser utilizado na tela inicial, cadastro inicial, negociacao e visita. So aparecera
-- na tela de Cadastro de Perfil. 
ALTER TABLE ambiente ADD COLUMN situacao integer DEFAULT 1;
update ambiente set situacao=1;

-- Descricao: Campo que ira armazenar uma imagem para ser enviada por e-mail
-- Autor: Pedro Y. Saito
-- Data: 11/04/2011
-- Motivacao: Permitir que o cliente possa enviar imagem de mala direta 
ALTER TABLE modelomensagem ADD COLUMN imagemModelo bytea;

-- Descricao: alterando a tabela de configuracao para adicionar a coluna de verificacao de banco inicial
-- Autor: Carla Pereira de Moraes
-- Data: 2011-03-28
-- Motivacao: Melhora no processo de execucao do script de banco inicial
ALTER TABLE configuracaosistema ADD COLUMN rodarSqlsBancoInicial boolean DEFAULT false;

-- Descricao: Removendo a tabela negociacaoeventocontratosituacao
-- Autor: JFelipe
-- Data: 2011-04-12
-- Motivacao: Durante refactoring da populacao inicial dos dados foi verificado que a mesma nao e utilizada
ALTER TABLE negociacaoeventocontrato DROP COLUMN situacao;
DROP TABLE negociacaoeventocontratosituacao;

-- Descricao: Campo que ira guardar o nome da imagem de upload
-- Autor: Pedro Y. Saito
-- Data: 13/04/2011
-- Motivacao: Permitir que o cliente possa enviar imagem de mala direta  
ALTER TABLE modelomensagem ADD COLUMN nomeImagem character varying(150);

-- Descricao: Criar campo para guardar obrigatoriedade de servico no perfil
-- Autor: Joao Alcides
-- Data: 13/04/2011
-- Motivacao: Determinar que o servico marcado como obrigatorio seja adicionado ao evento automaticamente 
-- sem que exista opcao de retira-lo, assim como ja ocorre com produtos.  
ALTER TABLE perfileventoservico ADD COLUMN obrigatorio BOOLEAN NOT NULL DEFAULT FALSE;

-- Descricao: novo campo retornoManual em HistoriContrato (boolean)
-- Autor: Waller Maciel
-- Data: 2011-12-04
-- Motivacao: Necessidade de sinalizacao de um retorno de situacao do contrato manualmente
ALTER TABLE historicocontrato ADD COLUMN retornoManual boolean DEFAULT false;

-- Descricao: 2 campos novos na tabela do colaborador. 1 campo novo na configuracaosistema. 2 tabelas novas. 1 campo novo no movparcela
-- Autor: Mauricio Lhoji Shiozawa
-- Data: 28/03/2011
-- Motivacao: Gestao de personal.
ALTER TABLE colaborador ADD COLUMN diavencimento SMALLINT;
ALTER TABLE colaborador ADD COLUMN produtoDefault INT;
ALTER TABLE configuracaosistema ADD COLUMN vencimentocolaborador SMALLINT DEFAULT 1;
CREATE TABLE controletaxapersonal
(
  codigo serial NOT NULL,
  empresa integer NOT NULL,
  personal integer NOT NULL,
  dataregistro date NOT NULL,
  responsavel integer NOT NULL,
  CONSTRAINT pk_controletaxapersonal PRIMARY KEY (codigo),
  CONSTRAINT fk_controletaxapersonal_colaborador FOREIGN KEY (personal)
      REFERENCES colaborador (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE RESTRICT,
  CONSTRAINT fk_controletaxapersonal_usuario FOREIGN KEY (responsavel)
      REFERENCES usuario (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE RESTRICT
);
CREATE TABLE itemtaxapersonal
(
  codigo serial NOT NULL,
  controle integer NOT NULL,
  aluno integer NOT NULL,
  produto integer NOT NULL,
  desconto integer,
  CONSTRAINT pk_itemtaxapersonal PRIMARY KEY (codigo),
  CONSTRAINT fk_itemtaxapersonal_controle FOREIGN KEY (controle)
      REFERENCES controletaxapersonal (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE CASCADE,
  CONSTRAINT fk_itemtaxapersonal_cliente FOREIGN KEY (aluno)
      REFERENCES cliente (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE RESTRICT,
  CONSTRAINT fk_itemtaxapersonal_produto FOREIGN KEY (produto)
      REFERENCES produto (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE RESTRICT,
  CONSTRAINT fk_itemtaxapersonal_desconto FOREIGN KEY (desconto)
      REFERENCES desconto (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE RESTRICT
);
ALTER TABLE movparcela ADD COLUMN personal integer;
ALTER TABLE movparcela ADD CONSTRAINT fk_movparcela_controle FOREIGN KEY (personal)
      REFERENCES controletaxapersonal (codigo) MATCH SIMPLE
      ON UPDATE RESTRICT ON DELETE CASCADE;

-- Descricao: Novo campo "DataInicioTemporal" na tabela "HistoricoContrato"
-- Autor: Ulisses Silva de Souza
-- Data: 20/04/2011
-- Motivacao: Identificar em qual data, a operacao sera considerada para a estatistica.
alter table HistoricoContrato add COLUMN DataInicioTemporal TIMESTAMP;

-- Descricao: Corrigir valores conta corrente negativos
-- Autor: Waller Maciel
-- Data: 05/05/2011
-- Motivacao: Corrigir valores gravados negativos incorretamente na conta corrente
update movimentocontacorrentecliente set valor = abs(valor) where valor < 0;

CREATE TABLE cupomfiscal
(
  responsavel integer NOT NULL,
  valor real NOT NULL,
  horavenda timestamp without time zone,
  horaemissao timestamp without time zone,
  statusimpressao integer NOT NULL,
  co_cupom character(50),
  recibo integer,
  cheque integer,
  "local" integer NOT NULL DEFAULT 1,
  cliente integer NOT NULL DEFAULT 1,
  codigo serial NOT NULL
)
WITH (
  OIDS=FALSE
);

CREATE TABLE cupomfiscalitens
(
  cupomfiscal integer NOT NULL,
  produto integer NOT NULL,
  quantidade integer NOT NULL,
  descricao character(150) NOT NULL,
  valorunitario real NOT NULL,
  valortotal real NOT NULL,
  codigo serial NOT NULL,
  valordescontoouacrescimo real DEFAULT 0.0,
  CONSTRAINT cupomfiscalitem_pkey PRIMARY KEY (codigo)
)
WITH (
  OIDS=FALSE
);

CREATE TABLE cupomfiscalformaspagamento
(
  cupomfiscal integer NOT NULL,
  formapagamento integer NOT NULL,
  valor real NOT NULL DEFAULT 0.0,
  codigo serial NOT NULL
)
WITH (
  OIDS=FALSE
);


-- Descricao: corrige o responsavel do produto a partir do responsavel pelo contrato
-- Autor: Mauricio Lhoji Shiozawa
-- Data: 2011-05-23
-- Motivacao: Erro no relatorio de faturamento por periodo que nao mostra o responsavel correto.
update movproduto set responsavellancamento = (select contrato.responsavelcontrato from contrato where movproduto.contrato = contrato.codigo)
where contrato is NOT NULL and
      contrato in (select contrato.codigo from contrato where movproduto.contrato = contrato.codigo and movproduto.responsavellancamento <> contrato.responsavelcontrato);

-- Descricao: o responsavel pelo contrato nao pode excluido da tabela de usuario se ele estiver presente na tabela de contrato
-- Autor: Mauricio Lhoji Shiozawa
-- Data: 2011-05-23
-- Motivacao: Um contrato foi encontrado mas o responsavel pelo contrato ja estava excluido.
ALTER TABLE contrato
  ADD CONSTRAINT fk_contrato_responsavel FOREIGN KEY (responsavelcontrato)
    REFERENCES usuario(codigo)
    ON DELETE RESTRICT
    ON UPDATE NO ACTION
    NOT DEFERRABLE;

-- Descricao: Tributacao na configuracao do sistema. Outros relacionamentos do cupom
-- Autor: Joao Felipe
-- Data: 2011-05-25
-- Motivacao: Evolucao do ECF
ALTER TABLE configuracaosistema ADD COLUMN tributacao REAL DEFAULT 0;

-- Descricao: Tributacao de servicos e uso de ecf na configuracao do sistema.
-- Autor: Joao Felipe
-- Data: 2011-05-26
-- Motivacao: Permitir a emissao automatica de ECF e distinguir a tributacao de produto e a de servico.
ALTER TABLE configuracaosistema ADD COLUMN tributacaoservico REAL DEFAULT 0;
ALTER TABLE configuracaosistema ADD COLUMN usaecf BOOLEAN DEFAULT false;

-- Descricao: Alterar o tamanho da coluna senhaAcesso
-- Autor: Ulisses Silva de Souza
-- Data: 2011-06-01
-- Motivacao: Quando criptografa a senha, o tamanho da mesma fica maior que 30 caracteres
ALTER TABLE pessoa ALTER COLUMN senhaAcesso TYPE character varying(64);
ALTER TABLE pessoa ADD CONSTRAINT unique_Pessoa_SenhaAcesso UNIQUE(senhaAcesso);

-- Descricao: Criar flag aberturaretroativa na tabela abertura meta para identificar as metas abertas de forma retroativa
-- Autor: Joao Alcides
-- Data: 2011-06-02
-- Motivacao: O sistema vai passar a nao calcular algumas metas que estao ligadas ao estado atual dos alunos (como meta de faltosos)
-- quando as mesmas forem abertas de forma retroativa. E necessario identificar estes casos para informar o usuario 
ALTER TABLE aberturameta ADD COLUMN aberturaretroativa BOOLEAN DEFAULT false;

-- Descricao: Criar tabela do Plano de Contas
-- A tabela foi removida pois estah sendo criada na versao 78
-- Autor: Waller Maciel
-- Data: 2011-06-06
-- Motivacao: criar tabela do Modulo Financeiro Plano de Contas
DROP TABLE planocontas;

-- Descricao: Customizacao de data base na negociacao de um contrato
-- Autor: Mauricio Lhoji Shiozawa
-- Data: 2011-06-08
-- Motivacao: Customizacao de data base na negociacao de um contrato
ALTER TABLE contrato ADD COLUMN dataAlteracaoManual timestamp without time zone;
ALTER TABLE contrato ADD COLUMN responsavelDataBase integer;
ALTER TABLE contrato ADD CONSTRAINT fk_contrato_responsaveldatabase FOREIGN KEY (responsaveldatabase)
      REFERENCES usuario (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE RESTRICT;
ALTER TABLE movparcela ADD COLUMN dataAlteracaoManual timestamp without time zone;

-- Descricao: Criacao das tabelas de contas, tipo conta e tipo documento
-- Autor: Joao Felipe
-- Data: 2011-06-08
-- Motivacao: Cadastros auxiliares do modulo financeiro
create table TIPOCONTA (
   CODIGO               SERIAL               not null,
   DESCRICAO            VARCHAR(100)         null,
   constraint PK_TIPOCONTA primary key (CODIGO)
);
create table TIPODOCUMENTO (
   CODIGO               SERIAL               not null,
   DESCRICAO            VARCHAR(100)         null,
   constraint PK_TIPODOCUMENTO primary key (CODIGO)
);
create table CONTA (
   CODIGO               SERIAL               not null,
   EMPRESA              INT4                 null,
   TIPOCONTA            INT4                 null,
   DESCRICAO            VARCHAR(100)         null,
   BANCO                INT4                 null,
   NUMERO               VARCHAR(10)          null,
   NUMERODV             CHAR                 null,
   AGENCIA              INT4                 null,
   AGENCIADV            INT2                 null,
   DESATIVADA           boolean                 null,
   MOSTRARNOBI          boolean                 null,
   OBSERVACAO           VARCHAR(254)         null,
   constraint PK_CONTA primary key (CODIGO),
   constraint FK_CONTA_ASSOCIATI_TIPOCONT foreign key (TIPOCONTA)
      references TIPOCONTA (CODIGO)
      on delete restrict on update restrict,
   constraint FK_CONTA_REFERENCE_EMPRESA foreign key (EMPRESA)
      references EMPRESA (CODIGO)
      on delete restrict on update restrict,
   constraint FK_CONTA_REFERENCE_BANCO foreign key (BANCO)
      references BANCO (CODIGO)
      on delete restrict on update restrict
);