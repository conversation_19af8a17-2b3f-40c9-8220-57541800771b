# To change this template, choose <PERSON><PERSON> | Templates
# and open the template in the editor.
principal.lingua=L\u00edngua
principal.cadastros = Cadastros
principal.gestao = Gest\u00e3o
principal.alunos = Alunos
principal.agenda = Agenda
principal.notificacoes = Notifica\u00e7\u00f5es
principal.pesquisa = Pesquisa

cadastros.musculos = M\u00fasculos
cadastros.gruposmusculares = Grupos Musculares
cadastros.aparelhos = Aparelhos
cadastros.atividades = Atividades
cadastros.programas = Programas de Treino
cadastros.todosalunos = Alunos
cadastros.agendamentos = Agendamentos
cadastros.compromissos = Compromissos
cadastros.objetivos = Objetivos pr\u00e9-definidos
cadastros.fichas = Fichas

cadastros.salvar=Salvar
cadastros.salvarTodos=Salvar Todos
cadastros.addNovaAtividade=Adicionar nova atividade
cadastros.addNovoGrupo=Adicionar novo grupo
cadastros.musculosCadastrados=M\u00fasculos cadastrados
cadastros.adicionarMusculo=Adicionar m\u00fasculo
cadastros.filtrar=Filtrar
cadastros.relacoes=rela\u00e7\u00f5es
cadastros.musculo.nome=Nome do m\u00fasculo
cadastros.excluir=Excluir
cadastros.gruposmuscularescadastrados=Grupos Musculares Cadastrados
cadastros.addNovoMusculo=Adicionar novo m\u00fasculo
cadastros.grupoMuscular.nome=Nome do grupo
cadastros.adicionarGrupoMuscular=Adicionar Grupo Muscular
cadastros.aparelho.nome=Nome do Aparelho
cadastros.adicionarAparelho=Adicionar Aparelho
cadastros.aparelhoscadastrados=Aparelhos Cadastrados
cadastros.aparelho.tipo=Tipo
cadastros.atividade.nome=Nome da atividade
cadastros.atividade.descricao=Descri\u00e7\u00e3o
cadastros.atividade.ativo=Ativa
cadastros.atividade.seriesApenasDuracao=S\u00e9ries apenas com Dura\u00e7\u00e3o
cadastros.atividade.tipo=Tipo
cadastros.atividade.copyDescToSerie=Copia desc. para s\u00e9rie
cadastros.adicionarAtividade=Adicionar atividade
cadastros.addNovoAparelho=Adicionar novo aparelho
cadastros.addCategoria=Adicionar nova categoria
cadastros.categoriaAtividades=Cat. de Atividades
cadastros.atividadescadastradas=Atividades cadastradas
cadastros.nome=Nome
cadastros.descricao=Descri\u00e7\u00e3o
cadastros.sim=Sim
cadastros.nao=N\u00e3o
cadastros.categoriaatividadeadicionar=Adicionar categoria
cadastros.categoriaatividade.nome=Nome da categoria
cadastros.categoriaatividade=Categorias de Atividades
cadastros.categoriaatividadecadastradas=Categorias de atividades cadastradas
cadastros.categoriaficha=Categorias de Fichas
cadastros.categoriafichacadastradas=Categorias de fichas cadastradas
cadastros.addFichas=Adicionar nova ficha
cadastros.nivel=N\u00edveis
cadastros.adicionarNivel=Adicionar n\u00edvel
cadastros.nivel.nome=Nome do N\u00edvel
cadastros.nivel.ordem=Ordem
cadastros.adicionarobjetivos=Adicionar Objetivo
cadastros.objetivos.nome=Nome do objetivo
cadastros.objetivoscadastrados=Objetivos pr\u00e9-definidos cadastrados
cadastros.niveiscadastrados=N\u00edveis cadastrados
cadastros.imagens=Imagens
cadastros.imagensadicionar=Adicionar imagem
cadastros.imagem.nome=Nome da imagem
cadastros.imagem.endereco=Endere\u00e7o da imagem
cadastros.imagemcadastradas=Imagens cadastradas
cadastros.addImagem=Adicionar nova imagem
cadastros.todos=Todos
domingo=Domingo
segunda=Segunda
terca=Ter\u00e7a
quarta=Quarta
quinta=Quinta
sexta=Sexta
sabado=S\u00e1bado
cadastros.ficha=Ficha
cadastros.programa.nome=Nome do Programa
cadastros.programa.professorCarteira=Na carteira de
cadastros.detalhes=Editar Detalhes
cadastros.datainicio=Data In\u00edcio
cadastros.datafim=Data Fim
cadastros.diassemana=Dias por Semana
cadastros.programa.aulasprevistas=Aulas previstas
cadastros.programa.proximarevisao=Pr\u00f3xima revis\u00e3o
cadastros.programa.termino=T\u00e9rmino (Previs\u00e3o)
cadastros.ficha.nova=Nova ficha
cadastros.fichas.predefinidas=Fichas pr\u00e9-definidas
cadastros.objetivos.predefinidos=Objetivos pr\u00e9-definidos
cadastros.atividades.selecionadas=Atividades da ficha
cadastros.fichas.adicionadas=Fichas Adicionadas
cadastros.ficha.editar=Editar ficha
cadastros.ficha.tornarpre=Tornar pr\u00e9-definida
cadastros.fichas.nome=Nome da nova ficha
cadastros.ficha.tipoexecucao=Tipo de execu\u00e7\u00e3o
cadastros.mensagem=Mensagem
cadastros.ajuste=Ajuste
cadastros.valor=Valor
cadastros.adicionarAjuste=Adicionar ajuste
cadastros.serie=S\u00e9rie
cadastros.quantidade=Sets
cadastros.carga=Carga
cadastros.repeticoes=Repeti\u00e7\u00f5es
cadastros.duracao=Dura\u00e7\u00e3o
cadastros.distancia=Dist\u00e2ncia
cadastros.velocidade=Velocidade
cadastros.descanso=Descanso
cadastros.repeticoes.abrev=Rep
cadastros.carga.abrev=C
cadastros.duracao.abrev=Dur
cadastros.distancia.abrev=Dist
cadastros.velocidade.abrev=Vel
cadastros.descanso.abrev=Desc
cadastros.complemento=Complemento
cadastros.remover=Remover
cadastros.serie.adicionar=S\u00e9rie
cadastros.serie.remover=Excluir S\u00e9rie
cadastros.ficha.concluir=Concluir ficha
cadastros.programa.novo=Novo programa
cadastros.musculo=M\u00fasculo
cadastros.grupomuscular=Grupo muscular
cadastros.aparelho=Aparelho
cadastros.atividade=Atividade
cadastros.categoriaatividadesing=Categoria de atividade
cadastros.categoriafichasing=Categoria de ficha
cadastros.nivelsing=N\u00edvel
cadastros.objetivo=Objetivo pr\u00e9-definido
cadastros.imagem=Imagem
cadastros.programa=Programa de treino
cadastros.serie.adicionada=S\u00e9rie adicionada com sucesso!
cancelar=Cancelar
cadastros.atividade.remover=Atividade
cadastros.atividade.salvar=Atividade
disponibilidade=Disponibilidade
contatoInterpessoal=Contato Interpessoal
prescricaoTreino=Prescri\u00e7\u00e3o Treino
revisaoTreino=Revis\u00e3o Treino
renovarTreino=Renovar Treino
agenda.tipo=Tipo
agenda.inicio=In\u00edcio
agenda.fim=Fim
agenda.diaTodo=Dia todo
agenda.novo=Novo agendamento
agenda.aluno=Aluno
professor=Professor
professores=Professores
categorias=Categorias
calendario=Calend\u00e1rio
tiposEvento=Tipos de Evento
editarDisponibilidade=Editar disponibilidade
start=Iniciar
finishTreino=Abandonar treino
finishSerie=Finalizar s\u00e9rie
cadastros.ajustes=Ajustes
realizada=Realizada
alterarSerie=Alterar s\u00e9rie
status=Status
historicoExecucoes=Hist\u00f3rico de execu\u00e7\u00f5es
series=S\u00e9ries
quilos=Kg
metros=m
minutos=min
semNotificacao=Sem notifica\u00e7\u00e3o
diminuiuCarga=Diminuiu carga
aumentouCarga=Aumentou carga
buscar=Buscar
concluirCadastro=Concluir cadastro
selecioneProfessor=Selecione um professor
naotemprofessor=N\u00e3o tem professor
voltar=Voltar
emaillogincontato=E-mail para login e contato
senhapodedeixar=Senha (Pode deixar em branco)
nomecompletoparte=Nome (Completo ou Parte)
CPF=CPF
matriculaaluno=Matr\u00edcula do aluno
badge=Badge
badges=Badges

valorBadge=Valor badge
ativo=Ativo
cadastros.metodo=M\u00e9todo de treinamento
horarioComercial=Hr. comercial
repetir=Repetir
diaSemana=Dias da semana
repetirAte=Data limite
remover=Remover
disponibilidades=Disponibilidades
concluirEdicao=Concluir edi\u00e7\u00e3o
dia=Dia
veragenda=Ver agenda
acompanhar=Acompanhar
agendar=Agendar
configuracoesGlobais=Configura\u00e7\u00f5es globais
configuracoesUsuario=Configura\u00e7\u00f5es do usu\u00e1rio
configuracoesPerfis=Configura\u00e7\u00f5es de perfis
novoperfil=Novo perfil
nomeperfil=Titulo do Perfil
cadastros.tipoEvento=Tipos de Eventos
cadastros.adicionarTipoEvento=Adicionar tipo
cadastros.tiposcadastrados=Tipos de evento cadastrados
cadastros.tiposEvento=Tipos de evento
cadastros.tiponome=Nome do tipo
cor=Cor
comportamento=Comportamento
cortipoevento=Cor do tipo do evento
disponibilidadepara=Disponibilidade para
todosTipos=Todos os tipos
agruparPor=Agrupar disponibilidades por
editar=Editar
fechar=Fechar
aplicarNSU=Confirmar altera\u00e7\u00f5es
simsomentemesmotipo=Apenas do mesmo tipo
NENHUM=N\u00e3o agrupar
PROFESSOR=Professor
TIPOEVENTO=Tipo evento
CADASTROS_AUXILIARES=Cadastros Auxiliares
AGENDA=Agenda
ALUNO=Aluno
GERAL=Geral
entidade=Entidades
funcionalidade=Funcionalidades
permitir=Permitir
Janeiro=Janeiro
Fevereiro=Fevereiro
Marco=Mar\u00e7o
Abril=Abril
Maio=Maio
Junho=Junho
Julho=Julho
Agosto=Agosto
Setembro=Setembro
Outubro=Outubro
Novembro=Novembro
Dezembro=Dezembro
nrAgendamentos=N\u00famero de agendamentos
cada=a cada
dias=dias
agrupar=Agrupar
apenasAlunosCarteira=S\u00f3 alunos na carteira do prof.
configuracoesUsuarios=Usu\u00e1rios
usuario=Usu\u00e1rio
senha=Nova senha
perfil=Perfil
DM=D
SG=S
TR=T
QA=Q
QI=Q
SX=S
SB=S

editarUsuario=Editar usu\u00e1rios
editarPerfil=Editar perfis
somenteEste=Somente este evento
todosEventosRepetidos=Todos os eventos repetidos
acompanhadoPor=Acompanhado por
vencimentoContrato=Vencimento do contrato
nascimento=Nascimento
PORC_ACIMA_NOTIFICAR=Porcentagem m\u00ednima acima da carga para notificar o professor
PORC_ABAIXO_NOTIFICAR=Porcentagem m\u00ednima abaixo da carga para notificar o professor
voltarAgenda=Voltar
gestao.indicadores=Indicadores
gestao.agendados=Agendados
gestao.executados=Executados
gestao.inidicadores=Indicadores
itens=itens
total=Total
SUGERIR_TIPO_ALTERNADO=Sugerir tipo de execu\u00e7\u00e3o alternado na ficha
qrCodePlayStore=Obter aplicativo na Play Store
qrCodeAppStore=Obter aplicativo na App Store
qrCodeChaveEmpresa=Capturar QrCode Chave da Empresa
verAgendados=Ver Agendados
porcExecucao=Execu\u00e7\u00e3o do Programa de Treino em rela\u00e7\u00e3o ao Previsto
enfileirarImpressao=Enviar para impressora
gestao.cancelados=Cancelados
gestao.faltas=Faltas
gestao.indicadoresAgenda=Indicadores da Agenda
DURACAO_LIVRE=Dura\u00e7\u00e3o livre
DURACAO_PREDEFINIDA=Dura\u00e7\u00e3o pr\u00e9-definida
INTERVALO_DE_TEMPO=Intervalo de dura\u00e7\u00e3o
duracao_min=Dura\u00e7\u00e3o m\u00ednima (hr:min)
duracao_max=Dura\u00e7\u00e3o m\u00e1xima
duracao_evento=Dura\u00e7\u00e3o (hr:min)
pesquisaGeralAluno=Pesquisar Aluno (Ctrl+Shift+L)
fichaDeHoje=Ficha de Hoje
previstoRealizado=Previsto / Realizado
digiteAlgoAguarde=Digite algo e pressione <ENTER>...
SUGERIR_DESCANSO=Sugerir decanso padr\u00e3o nas s\u00e9ries
intervalo_minimo=Intervalo m\u00ednimo em caso de falta
legenda=Legenda
carregando=Carregando
leve=Leve
grave=Grave
media=M\u00e9dia
criadoPor=Criado por
AGENDA_NOVA_ABA=Abrir Agenda sempre em uma nova aba
irParaAluno=Ir para Aluno
atividadesNaFicha=Atividades na ficha
dadosFicha=Dados da ficha
adicionandoAtividadesFicha=Adicionando atividades na ficha
verdadosficha=Editar dados da ficha
cadastros.serie.gerar=S\u00e9ries
cadastros.serie.gerar.hint=Gerar s\u00e9ries em todas as atividades da ficha
addAluno.usarAplicativo=Vai usar o aplicativo agora?
gestao.carteira=Carteira
gestaoAgenda=Gest\u00e3o: Agenda
gestaoProfessores=Gest\u00e3o: Carteiras dos professores
indicadoresAgenda=Indicadores da agenda
indicadoresProfessores=Indicadores da carteira dos professores
gestao.semCarteira=Sem treino
intervaloPesquisa=Intervalo da pesquisa
filtros=Filtros
data=Data
carteiras=Carteiras dos professores
atividadesProfessores=Atividades dos professores
indicadoresAtividadesProfessores=Indicadores das atividades dos professores
gestaoAtividades=Gest\u00e3o: Atividades
gestao.treinoNovo=Novos
renovar=Renovar
gestao.treinoRenovado=Renovados
gestao.treinoRevisado=Revisados
gestao.treinoAcompanhados=Acompanhados
notificacao.push=Push enviado
notificacao.sms=SMS enviado
DIAS_ANTES_VENCIMENTO=Dias antes vencimento
DIAS_DEPOIS_VENCIMENTO=Dias depois vencimento
gestao.treinoProxVencimento=Pr\u00f3x.Vencimento
gestao.vencidos=Vencidos
gestao.avaliacao=Avalia\u00e7\u00e3o
gestao.duasEstrelas=2 Estrelas
GRAVE=Grave
LEVE=Leve
MEDIA=M\u00e9dia
horaInicio=Hora in\u00edcio
horaFim=Hora fim
gestao.indicadoresTreinos=Indicadores de treinos
ATIVO=Ativo
VENCIDO=Vencido
TRANCADO=Trancado
DESISTENTE=Desistente
CANCELADO=Cancelado
MATRICULA=Matr\u00edcula
REMATRICULA=Rematr\u00edcula
RENOVACAO=Renova\u00e7\u00e3o
VISITANTE=Visitante
cliente.enviarEmailAtivacao=Enviar/Reenviar email de ativa\u00e7\u00e3o ao aluno
revisar=Revisado
OUTROS=Outros
descricaoSituacao=Situa\u00e7\u00e3o de contrato vigente na data de hoje do aluno. Independente da data base da consulta, esta situa\u00e7\u00e3o \u00e9 a atual.
situacoesContrato=Situa\u00e7\u00f5es de contrato
situacoesPrograma=Situa\u00e7\u00f5es do programa
gravidade=Gravidade
gestaoNotificacoes=Gest\u00e3o de notifica\u00e7\u00f5es
notificacoes=notifica\u00e7\u00f5es
dataReferenciaCarteira=Data refer\u00eancia carteira
cadastros.programa.comoAlterarProfCarteira=Para alterar o professor da carteira, deve-se executar "Adicionar Aluno ao Pacto Treino" novamente.
tipSemTreino=N\u00famero de alunos que est\u00e3o no treino mas que nunca tiveram um programa de treino associado.
tipTotalAlunos=Este indicador leva em considera\u00e7\u00e3o o n\u00famero de alunos com programa de treino na data base da consulta, associando ao professor da carteira. S\u00e3o considerados alunos com programas ativos e vencidos.
tipTreinoNovo=Numero de treinos novos, ou seja o primeiro treino do aluno ap\u00f3s a matricula, o professor identificou deste treino como um treino novo. O professor escolheu o bot\u00e3o "Treino Novo"
tipProgramasVencidos=N\u00famero de alunos que possuem programa de treino que est\u00e1 vencido e n\u00e3o tem nenhum ativo na data base.
tipProximoVencidos=Alunos com programa pr\u00f3ximo de vencer de acordo com configura\u00e7\u00e3o do sistema.
tipIndiceAvaliacao=M\u00e9dia das notas dos treinos recebidas pelos professores  durante dois meses (m\u00eas da data base e o anterior). O valor do rodap\u00e9 \u00e9 a soma de todas as m\u00e9dias, dividida pelo n\u00famero de professores que receberam alguma nota, e n\u00e3o pelo total de professores.
tipDuasEstrelas=N\u00famero de notas menor ou igual a 2 recebidas pelos professores  durante dois meses (m\u00eas da data base e o anterior). 
tipTreinosRenovados=A partir de um programa de treino o professor pode apertar o bot\u00e3o \u201cRenovar\u201d e o sistema cria um novo treino a partir do treino anterior e coloca o treino anterior como renovado e este como sendo uma renova\u00e7\u00e3o.
tipTreinosRevisados=Quando professor entra do programa de treino e clica no bot\u00e3o revisar, o sistema registra uma revis\u00e3o.
tipTreinosAcompanhados=Quando o professor escolhe a op\u00e7\u00e3o de acompanhar no cadastro do aluno o sistema registra a data e hora. A a\u00e7\u00e3o representa que o professor est\u00e1 junto com o alunos na execu\u00e7\u00e3o de todas atividades de dia de treino do aluno. 
cadastros.atividade.abrirGaleriaImagens=Abrir galeria de Imagens
cadastros.atividade.selecaoImagens=Selecionar
cadastros.programa.datarevisao=Data revis\u00e3o
cadastros.programa.justificativaRevisao=Justificativa
cadastros.programa.historicoRevisoes=Hist\u00f3rico Revis\u00f5es
cadastros.confirmar=Confirmar
cadastros.subir=Subir

RETENCAO=Reten\u00e7\u00e3o
VENDAS=Vendas
ICV=ICV
CRM_VENDAS=CRM de Vendas
PREVISAO_RENOVACAO=Previs\u00e3o renova\u00e7\u00e3o
MAIOR=Maior que
MENOR=Menor que
IGUAL=Igual a
GESTOR=Gestor
TODOS=Todos
CONSULTOR=Consultor
PLANOS_POR_DURACAO=Plano por dura\u00e7\u00e3o
PREVISAO_RENOVACAO_CONTRATOS_ATIVOS=Previs\u00e3o de renova\u00e7\u00e3o X nr. contratos
PREVISAO_RENOVACAO_CORRENTE=Previs\u00e3o de renova\u00e7\u00e3o corrente
CONTATOS_GRUPO_RISCO=Contatos no grupo de risco
SALDO_ALUNOS=Saldo de alunos
CLIENTES_PARCELA_ATRASO=Clientes com parcelas em atraso
PARCELAS_DCC_VENCIDAS_NAO_RECEBIDAS=Parcelas DCC vencidas e n\u00e3o recebidas
NAO_USA_RECORRENCIA=N\u00e3o usa recorr\u00eancia
ICV_CONSULTOR=ICV por consultor
PRODUTOS_ESTOQUE_MINIMO = Produtos Estoque M\u00ednimo
cadastros.nova=Nova
cadastros.updateServices=Update Services
cadastros.updateVerificador=Update Verificador
cadastros.gerarBackups=Gerar Backups
cadastros.gerarBackupsTodos=Gerar Todos Backups
cadastros.editarCrontab=Editar Crontab
cadastros.gravarCrontab=Gravar Crontab
cadastros.nuvens=Servidores
cadastros.lerVersaoPgsql=Obter Vers\u00e3o PG
cadastros.gravarVersaoPgsql=Gravar Vers\u00e3o PG
FINANCEIRO=Financeiro
TREINO=Treino
CRM=CRM
ADMINISTRADOR=Administrador
GERENTE=Gerente
TELA_BI=Tela BI