------SQLS padroes do ZillyonWeb-------

CREATE TABLE GrauInstrucao
(
  descricao character varying(45) NOT NULL,
  codigo serial NOT NULL,
  CONSTRAINT grauinstrucao_pkey PRIMARY KEY (codigo)
);
CREATE INDEX CH_grauinstrucao_descricao ON grauinstrucao(descricao);

---------------------------------------------------------------- 
CREATE TABLE Banco 
(
    nome VARCHAR(50) NOT NULL,
    codigo SERIAL NOT NULL,
    codigobanco INT NOT NULL, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Banco_nome ON Banco(nome);
 
---------------------------------------------------------------- 
CREATE TABLE ContaCorrente 
(	
    banco INT NOT NULL, 
    contaCorrenteDV VARCHAR(5) NOT NULL, 
    contaCorrente VARCHAR(15) NOT NULL, 
    agenciaDV VARCHAR(5) NOT NULL, 
    agencia VARCHAR(50) NOT NULL, 
    codigo SERIAL NOT NULL, 
    CONSTRAINT FK_ContaCorrente_banco FOREIGN KEY (banco) 
		REFERENCES Banco(codigo) ON DELETE RESTRICT ON UPDATE RESTRICT, 
	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_ContaCorrente_banco ON ContaCorrente(banco);
CREATE INDEX CH_ContaCorrente_contaCorrenteDV ON ContaCorrente(contaCorrenteDV);
CREATE INDEX CH_ContaCorrente_contaCorrente ON ContaCorrente(contaCorrente);
CREATE INDEX CH_ContaCorrente_agenciaDV ON ContaCorrente(agenciaDV);
CREATE INDEX CH_ContaCorrente_agencia ON ContaCorrente(agencia);
 
---------------------------------------------------------------- 
CREATE TABLE TipoRetorno 
(
    arquivoLayoutRetorno VARCHAR(50) NOT NULL, 
    descricao VARCHAR(50) NOT NULL, 
    codigo SERIAL NOT NULL, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_TipoRetorno_arquivoLayoutRetorno ON TipoRetorno(arquivoLayoutRetorno);
CREATE INDEX CH_TipoRetorno_descricao ON TipoRetorno(descricao); 
 
---------------------------------------------------------------- 
CREATE TABLE TipoRemessa 
(
    tipoRemessa VARCHAR(50) NOT NULL, 
    arquivoLayoutRemessa VARCHAR(50) NOT NULL, 
    tipoRetorno INT NOT NULL, 
    descricao VARCHAR(50) NOT NULL, 
    codigo SERIAL NOT NULL, 
    CONSTRAINT FK_TipoRemessa_tipoRetorno FOREIGN KEY (tipoRetorno) 
		REFERENCES TipoRetorno(codigo) ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_TipoRemessa_tipoRemessa ON TipoRemessa(tipoRemessa);
CREATE INDEX CH_TipoRemessa_arquivoLayoutRemessa ON TipoRemessa(arquivoLayoutRemessa);
CREATE INDEX CH_TipoRemessa_tipoRetorno ON TipoRemessa(tipoRetorno);
CREATE INDEX CH_TipoRemessa_descricao ON TipoRemessa(descricao);

---------------------------------------------------------------- 
CREATE TABLE CondicaoPagamento 
(	
    intervaloentreparcela INT,
    percentualValorEntrada REAL, 
    condicaoPagamentoDefault BOOLEAN,
    entrada BOOLEAN, 
    nrParcelas INT NOT NULL, 
    descricao VARCHAR(45) NOT NULL, 
    codigo SERIAL NOT NULL, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_CondicaoPagamento_percentualValorEntrada ON CondicaoPagamento(percentualValorEntrada);
CREATE INDEX CH_CondicaoPagamento_nrParcelas ON CondicaoPagamento(nrParcelas);
CREATE INDEX CH_CondicaoPagamento_descricao ON CondicaoPagamento(descricao);
 
---------------------------------------------------------------- 
CREATE TABLE Pais 
(	
    nome VARCHAR(40) NOT NULL, 
    codigo SERIAL NOT NULL, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Pais_nome ON Pais(nome);

---------------------------------------------------------------- 

CREATE TABLE Estado 
(	
    pais INT NOT NULL, 
    sigla VARCHAR(2) NOT NULL, 
    descricao VARCHAR(30) NOT NULL, 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_Estado_pais FOREIGN KEY (pais) 
		REFERENCES Pais(codigo) ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Estado_pais ON Estado(pais);
CREATE INDEX CH_Estado_sigla ON Estado(sigla);
CREATE INDEX CH_Estado_descricao ON Estado(descricao);

---------------------------------------------------------------- 
CREATE TABLE Horario 
(	
    horarioDefault BOOLEAN, 
    domingo boolean,
    segunda boolean,
    terca boolean,
    quarta boolean,
    quinta boolean,
    sexta boolean,
    sabado boolean,   
    livre BOOLEAN,      
    descricao VARCHAR(45) NOT NULL, 
    codigo SERIAL NOT NULL, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Horario_descricao ON Horario(descricao);
 
---------------------------------------------------------------- 
CREATE TABLE Classificacao 
(	
    nome VARCHAR(45) NOT NULL, 
    codigo SERIAL NOT NULL, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Classificacao_nome ON Classificacao(nome);
 
---------------------------------------------------------------- 
CREATE TABLE Ambiente 
(	
    descricao VARCHAR(45) NOT NULL, 
    codigo SERIAL NOT NULL, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Ambiente_descricao ON Ambiente(descricao);
 
---------------------------------------------------------------- 
CREATE TABLE Pergunta 
(	
    tipoPergunta VARCHAR(2) NOT NULL, 
    descricao VARCHAR(300) NOT NULL, 
    codigo SERIAL NOT NULL, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Pergunta_tipoPergunta ON Pergunta(tipoPergunta);
CREATE INDEX CH_Pergunta_descricao ON Pergunta(descricao);
 
---------------------------------------------------------------- 
CREATE TABLE HorarioDisponibilidade 
(	
    noturno BOOLEAN,
    vespertino BOOLEAN,
    matutino BOOLEAN,
    desenhartodos BOOLEAN,
    hora2330 BOOLEAN, 
    hora2300 BOOLEAN, 
    hora2230 BOOLEAN, 
    hora2200 BOOLEAN, 
    hora2130 BOOLEAN, 
    hora2100 BOOLEAN, 
    hora2030 BOOLEAN, 
    hora2000 BOOLEAN, 
    hora1930 BOOLEAN, 
    hora1900 BOOLEAN, 
    hora1830 BOOLEAN, 
    hora1800 BOOLEAN, 
    hora1730 BOOLEAN, 
    hora1700 BOOLEAN, 
    hora1630 BOOLEAN, 
    hora1600 BOOLEAN, 
    hora1530 BOOLEAN, 
    hora1500 BOOLEAN, 
    hora1430 BOOLEAN, 
    hora1400 BOOLEAN, 
    hora1330 BOOLEAN, 
    hora1300 BOOLEAN, 
    hora1230 BOOLEAN, 
    hora1200 BOOLEAN, 
    hora1130 BOOLEAN, 
    hora1100 BOOLEAN, 
    hora1030 BOOLEAN, 
    hora1000 BOOLEAN, 
    hora0930 BOOLEAN, 
    hora0900 BOOLEAN, 
    hora0830 BOOLEAN, 
    hora0800 BOOLEAN, 
    hora0730 BOOLEAN, 
    hora0700 BOOLEAN, 
    hora0630 BOOLEAN, 
    hora0600 BOOLEAN, 
    hora0530 BOOLEAN, 
    hora0500 BOOLEAN, 
    hora0430 BOOLEAN, 
    hora0400 BOOLEAN, 
    hora0330 BOOLEAN, 
    hora0300 BOOLEAN, 
    hora0230 BOOLEAN, 
    hora0200 BOOLEAN, 
    hora0130 BOOLEAN, 
    hora0100 BOOLEAN, 
    hora0030 BOOLEAN, 
    hora0000 BOOLEAN, 
    horario INT, 
    identificador VARCHAR(10) NOT NULL, 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_HorarioDisponibilidade_horario FOREIGN KEY (horario) 
		REFERENCES Horario(codigo) ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_HorarioDisponibilidade_horario ON HorarioDisponibilidade(horario);
CREATE INDEX CH_HorarioDisponibilidade_identificador ON HorarioDisponibilidade(identificador);
 
---------------------------------------------------------------- 
CREATE TABLE CategoriaProduto 
(	
    descricao VARCHAR(45) NOT NULL, 
    codigo SERIAL NOT NULL, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_CategoriaProduto_descricao ON CategoriaProduto(descricao);
 
---------------------------------------------------------------- 
CREATE TABLE Parentesco 
(	
    idadeLimiteDependencia INT NOT NULL, 
    descricao VARCHAR(45) NOT NULL, 
    codigo SERIAL NOT NULL, 	
	PRIMARY KEY (codigo)
);
CREATE INDEX CH_Parentesco_idadeLimiteDependencia ON Parentesco(idadeLimiteDependencia);
CREATE INDEX CH_Parentesco_descricao ON Parentesco(descricao);
 
---------------------------------------------------------------- 
CREATE TABLE PerguntaCliente 
(	
    multipla boolean,
    simples boolean,
    textual boolean,
    tipoPergunta VARCHAR(2) NOT NULL, 
    descricao VARCHAR(300) NOT NULL, 
    codigo SERIAL NOT NULL, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_PerguntaCliente_tipoPergunta ON PerguntaCliente(tipoPergunta);
CREATE INDEX CH_PerguntaCliente_descricao ON PerguntaCliente(descricao);
 
---------------------------------------------------------------- 
CREATE TABLE Profissao 
(	
    descricao VARCHAR(45) NOT NULL, 
    codigo SERIAL NOT NULL, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Profissao_descricao ON Profissao(descricao);
 
---------------------------------------------------------------- 
CREATE TABLE Grupo 
(
    tipoDesconto VARCHAR(2) NOT NULL, 
    valorDescontoGrupo REAL NOT NULL, 
    percentualDescontoGrupo REAL NOT NULL, 
    descricao VARCHAR(45) NOT NULL, 
    codigo SERIAL NOT NULL, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Grupo_tipoDesconto ON Grupo(tipoDesconto);
CREATE INDEX CH_Grupo_descricao ON Grupo(descricao);
 

----------------------------------------------------------------
CREATE TABLE RespostaPergunta 
(
    descricaoRespota text NOT NULL, 
    pergunta INT NOT NULL, 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_RespostaPergunta_pergunta FOREIGN KEY (pergunta) 
		REFERENCES Pergunta(codigo) ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_RespostaPergunta_descricaoRespota ON RespostaPergunta(descricaoRespota);
CREATE INDEX CH_RespostaPergunta_pergunta ON RespostaPergunta(pergunta);
 
---------------------------------------------------------------- 
CREATE TABLE Questionario 
(	
    descricao VARCHAR(300) NOT NULL, 
    codigo SERIAL NOT NULL, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Questionario_descricao ON Questionario(descricao);
 
---------------------------------------------------------------- 
CREATE TABLE Categoria 
(	
    nrConvitePermitido INT, 
    tipoCategoria VARCHAR(2), 
    nome VARCHAR(50) NOT NULL, 
    codigo SERIAL NOT NULL, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Categoria_nrConvitePermitido ON Categoria(nrConvitePermitido);
CREATE INDEX CH_Categoria_tipoCategoria ON Categoria(tipoCategoria);
CREATE INDEX CH_Categoria_nome ON Categoria(nome);
 
---------------------------------------------------------------- 
CREATE TABLE PerfilAcesso 
(	
    nome VARCHAR(100) NOT NULL UNIQUE, 
    codigo SERIAL NOT NULL, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_PerfilAcesso_nome ON PerfilAcesso(nome);
 
---------------------------------------------------------------- 
CREATE TABLE NivelTurma 
(	
    descricao VARCHAR(45) NOT NULL, 
    codigo SERIAL NOT NULL, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_NivelTurma_descricao ON NivelTurma(descricao);
 
---------------------------------------------------------------- 
CREATE TABLE Modalidade 
(	
    modalidadeDefault BOOLEAN, 
    utilizarTurma BOOLEAN, 
    utilizarProduto BOOLEAN, 
    valorMensal REAL NOT NULL, 
    ativo BOOLEAN, 
    nome VARCHAR(50) NOT NULL, 
    nrvezes Int NOT NULL, 
    codigo SERIAL NOT NULL, 		
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Modalidade_nome ON Modalidade(nome);
 
---------------------------------------------------------------- 
CREATE TABLE RespostaPergCliente 
(
    respostaTextual text, 
    respostaOpcao BOOLEAN, 
    descricaoRespota text NOT NULL, 
    perguntaCliente INT NOT NULL, 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_RespostaPergCliente_perguntaCliente FOREIGN KEY (perguntaCliente) 
		REFERENCES PerguntaCliente(codigo) ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_RespostaPergCliente_descricaoRespota ON RespostaPergCliente(descricaoRespota);
CREATE INDEX CH_RespostaPergCliente_perguntaCliente ON RespostaPergCliente(perguntaCliente);

---------------------------------------------------------------- 
CREATE TABLE Cidade 
(	
    Pais INT NOT NULL, 
    estado INT NOT NULL, 
    nome VARCHAR(40) NOT NULL, 
    nomesemacento VARCHAR(40)NOT NULL, 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_Cidade_estado FOREIGN KEY (estado) 
		REFERENCES estado(codigo) ON DELETE RESTRICT ON UPDATE RESTRICT, 
	CONSTRAINT FK_Cidade_Pais FOREIGN KEY (Pais) 
		REFERENCES Pais(codigo) ON DELETE RESTRICT ON UPDATE RESTRICT, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Cidade_Pais ON Cidade(Pais);
CREATE INDEX CH_Cidade_estado ON Cidade(estado);
CREATE INDEX CH_Cidade_nome ON Cidade(nome);
---------------------------------------------------------------- 
CREATE TABLE Desconto 
(	
    tipoProduto VARCHAR(2) NOT NULL, 
    tipoDesconto VARCHAR(2) NOT NULL, 
    valor REAL NOT NULL, 
    descricao VARCHAR(50) NOT NULL, 
    codigo SERIAL NOT NULL, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Desconto_tipoProduto ON Desconto(tipoProduto);
CREATE INDEX CH_Desconto_tipoDesconto ON Desconto(tipoDesconto);
CREATE INDEX CH_Desconto_valor ON Desconto(valor);
CREATE INDEX CH_Desconto_descricao ON Desconto(descricao);
---------------------------------------------------------------- 
CREATE TABLE Produto 
(	
    desativado Boolean,
    tipoProduto VARCHAR(2), 
    desconto INT,   
    valorbasecalculo REAL, 
    valorfinal REAL,          
    nrDiasVigencia INT, 
    dataFinalVigenciaFixa TIMESTAMP, 
    dataInicioVigencia TIMESTAMP, 
    tipoVigencia VARCHAR(2), 
    descricao VARCHAR(50) NOT NULL, 
    categoriaProduto INT, 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_Produto_desconto FOREIGN KEY (desconto) 
		REFERENCES desconto(codigo) ON DELETE RESTRICT 
		ON UPDATE RESTRICT,
	CONSTRAINT fk_produto_categoriaproduto FOREIGN KEY (categoriaproduto) 
		REFERENCES categoriaproduto (codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT ON DELETE RESTRICT,
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Produto_tipoVigencia ON Produto(tipoVigencia);
CREATE INDEX CH_Produto_descricao ON Produto(descricao);
CREATE INDEX CH_Produto_categoriaProduto ON Produto(categoriaProduto);


---------------------------------------------------------------- 
CREATE TABLE Permissao 
(	
    valorFinal VARCHAR(100), 
    valorInicial VARCHAR(100), 
    valorEspecifico VARCHAR(100), 
    tipoPermissao INT, 
    tituloApresentacao VARCHAR(100), 
    permissoes VARCHAR(30), 
    nomeEntidade VARCHAR(60) NOT NULL, 
    codPerfilAcesso INT NOT NULL, 	
    CONSTRAINT FK_Permissao_codPerfilAcesso FOREIGN KEY (codPerfilAcesso) 
		REFERENCES PerfilAcesso(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codPerfilAcesso, nomeEntidade)
);
CREATE INDEX CH_Permissao_tituloApresentacao ON Permissao(tituloApresentacao);
 
---------------------------------------------------------------- 
CREATE TABLE Empresa 
(	
    nrdiasavencer integer,
	toleranciapagamento integer,
    qtdfaltapeso1 integer,
    qtdfaltainiciopeso2 integer,
    qtdfaltaterminopeso2 integer,
    qtdfaltapeso3 integer,
	fotorelatorio bytea,
    foto bytea,
    alturafotoempresa character varying(50),
    alturafotorelatorio character varying(50),
    largurafotoempresa character varying(50),
    largurafotorelatorio character varying(50),  
    carenciaRenovacao INT,    
    mascaramatricula character varying,    
    nrdiasvigentequestionariovisita integer,
    nrdiasvigentequestionarioretorno integer,
    nrdiasvigentequestionariorematricula integer,    
    permiteContratosConcomintante BOOLEAN, 
    permiteSituacaoAtestadoContrato BOOLEAN, 
    questionarioReMatricula INT, 
    questionarioRetorno INT, 
    questionarioPrimeiraVisita INT, 
    juroParcela real,
    multa real,
    fax VARCHAR(14), 
    site VARCHAR(50), 
    email VARCHAR(50), 
    telComercial3 VARCHAR(14), 
    telComercial2 VARCHAR(14), 
    telComercial1 VARCHAR(14), 
    inscEstadual VARCHAR(20) NOT NULL, 
    CNPJ VARCHAR(18) NOT NULL, 
    CEP VARCHAR(10), 
    estado Int NOT NULL,
    cidade INT NOT NULL, 
    Pais INT NOT NULL, 
    complemento VARCHAR(50), 
    numero VARCHAR(5), 
    setor VARCHAR(50), 
    endereco VARCHAR(50) NOT NULL, 
    razaoSocial VARCHAR(50) NOT NULL, 
    nome VARCHAR(50) NOT NULL, 
    codigo SERIAL NOT NULL, 
	carencia integer,
    nrDiasProrata INTEGER DEFAULT 0,
    CONSTRAINT FK_Empresa_questionarioReMatricula FOREIGN KEY (questionarioReMatricula) 
		REFERENCES Questionario(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_Empresa_questionarioRetorno FOREIGN KEY (questionarioRetorno) 
		REFERENCES Questionario(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_Empresa_questionarioPrimeiraVisita FOREIGN KEY (questionarioPrimeiraVisita) 
		REFERENCES Questionario(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Empresa_questionarioReMatricula ON Empresa(questionarioReMatricula);
CREATE INDEX CH_Empresa_questionarioRetorno ON Empresa(questionarioRetorno);
CREATE INDEX CH_Empresa_questionarioPrimeiraVisita ON Empresa(questionarioPrimeiraVisita);
CREATE INDEX CH_Empresa_inscEstadual ON Empresa(inscEstadual);
CREATE INDEX CH_Empresa_cidade ON Empresa(cidade);
CREATE INDEX CH_Empresa_razaoSocial ON Empresa(razaoSocial);
CREATE INDEX CH_Empresa_nome ON Empresa(nome);


---------------------------------------------------------------- 
CREATE TABLE ConvenioCobranca 
(
	tipoconvenio INT NOT NULL, 
    tipoRemessa INT, 
    variacao INT , 
    carteira INT , 
    numeroContrato VARCHAR(50) NOT NULL, 
    contaEmpresa INT NOT NULL, 
    sequencialDoArquivo INT NOT NULL, 
    mensagem VARCHAR(200), 
    diretorioLerRetorno VARCHAR(20) NOT NULL, 
    diretorioGravaRemessa VARCHAR(50) NOT NULL, 
    extensaoArquivoRetorno VARCHAR(5) NOT NULL, 
    extensaoArquivoRemessa VARCHAR(5) NOT NULL, 
    juros REAL, 
    multa REAL, 
    empresa INT, 
    descricao VARCHAR(30) NOT NULL, 
    codigo SERIAL NOT NULL, 
	CONSTRAINT fk_conveniocobranca_empresa FOREIGN KEY (empresa)  
		REFERENCES empresa (codigo) MATCH SIMPLE ON UPDATE RESTRICT ON DELETE RESTRICT,	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_ConvenioCobranca_codigo ON ConvenioCobranca(codigo);
CREATE INDEX CH_ConvenioCobranca_variacao ON ConvenioCobranca(variacao);
CREATE INDEX CH_ConvenioCobranca_carteira ON ConvenioCobranca(carteira);
CREATE INDEX CH_ConvenioCobranca_numeroContrato ON ConvenioCobranca(numeroContrato);
CREATE INDEX CH_ConvenioCobranca_contaEmpresa ON ConvenioCobranca(contaEmpresa);
CREATE INDEX CH_ConvenioCobranca_sequencialDoArquivo ON ConvenioCobranca(sequencialDoArquivo);
CREATE INDEX CH_ConvenioCobranca_diretorioLerRetorno ON ConvenioCobranca(diretorioLerRetorno);
CREATE INDEX CH_ConvenioCobranca_diretorioGravaRemessa ON ConvenioCobranca(diretorioGravaRemessa);
CREATE INDEX CH_ConvenioCobranca_extensaoArquivoRetorno ON ConvenioCobranca(extensaoArquivoRetorno);
CREATE INDEX CH_ConvenioCobranca_extensaoArquivoRemessa ON ConvenioCobranca(extensaoArquivoRemessa);
CREATE INDEX CH_ConvenioCobranca_descricao ON ConvenioCobranca(descricao);

---------------------------------------------------------------- 
CREATE TABLE ContaCorrenteEmpresa 
(	
    contaCorrente INT,
    empresa INT,    
    codigo SERIAL NOT NULL, 
    CONSTRAINT FK_ContaCorrenteEmpresa_contaCorrente  FOREIGN KEY (contaCorrente ) 
		REFERENCES contaCorrente (codigo)  ON DELETE CASCADE ON UPDATE CASCADE,  
    CONSTRAINT FK_ContaCorrenteEmpresa_empresa  FOREIGN KEY (empresa ) 
		REFERENCES empresa (codigo) ON DELETE RESTRICT ON UPDATE RESTRICT,  
    PRIMARY KEY (codigo)
);

-----------------------------------------
CREATE TABLE CondicaoPagamentoParcela 
(	
    nrdiasparcela INT,
    nrparcela INT, 
    percentualParcela REAL, 
    condicaoPagamento INT NOT NULL, 
    codigo SERIAL NOT NULL, 
    CONSTRAINT FK_CondicaoPagamentoParcela_condicaoPagamento FOREIGN KEY (condicaoPagamento) 
		REFERENCES CondicaoPagamento(codigo) ON DELETE CASCADE ON UPDATE CASCADE, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_CondicaoPagamentoParcela_percentualParcela ON CondicaoPagamentoParcela(percentualParcela);
CREATE INDEX CH_CondicaoPagamentoParcela_condicaoPagamento ON CondicaoPagamentoParcela(condicaoPagamento);
 
---------------------------------------------------------------- 
CREATE TABLE ConfiguracaoSistema 
(	    
    nrdiasavencer integer,
    qtdfaltapeso1 integer,
    qtdfaltainiciopeso2 integer,
    qtdfaltaterminopeso2 integer,
    qtdfaltapeso3 integer,
	carenciaRenovacao INT,    
    mascaramatricula character varying,
    multa REAL, 
    juroParcela REAL,     
    questionarioReMatricula INT NOT NULL, 
    questionarioRetorno INT NOT NULL, 
    questionarioPrimeiraVisita INT NOT NULL, 
    nrdiasvigentequestionariovisita integer,
    nrdiasvigentequestionarioretorno integer,
    nrdiasvigentequestionariorematricula integer,
    toleranciapagamento integer,
    emailContaPagDigital character varying(60),
    tokenContaPagDigital character varying(60),
    tokenContaSMS character varying(60),
    codigo SERIAL NOT NULL, 
	carencia integer NOT NULL,
    nrDiasProrata INTEGER DEFAULT 0,
    cpfValidar boolean,
    dataexpiracao date,
    CONSTRAINT FK_ConfiguracaoSistema_questionarioReMatricula FOREIGN KEY (questionarioReMatricula) 
		REFERENCES Questionario(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_ConfiguracaoSistema_questionarioRetorno FOREIGN KEY (questionarioRetorno) 
		REFERENCES Questionario(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_ConfiguracaoSistema_questionarioPrimeiraVisita FOREIGN KEY (questionarioPrimeiraVisita) 
		REFERENCES Questionario(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_ConfiguracaoSistema_questionarioReMatricula ON ConfiguracaoSistema(questionarioReMatricula);
CREATE INDEX CH_ConfiguracaoSistema_questionarioRetorno ON ConfiguracaoSistema(questionarioRetorno);
CREATE INDEX CH_ConfiguracaoSistema_questionarioPrimeiraVisita ON ConfiguracaoSistema(questionarioPrimeiraVisita);

update configuracaosistema  set tokenContaSMS = '';--informe o codigo md5 da conta de sms
update configuracaosistema  set emailcontapagdigital = '';--informe o email cadastrado no pagamento digital
update configuracaosistema  set tokencontapagdigital = '';--token em hexa que identifica a conta no pagamento digital
 
---------------------------------------------------------------- 
CREATE TABLE FormaPagamento 
(	
    taxaCartao REAL, 
    tipoFormaPagamento VARCHAR(2) NOT NULL, 
    conveniocobranca INT, 
    descricao VARCHAR(45) NOT NULL, 
    codigo SERIAL NOT NULL, 
	CONSTRAINT formapagamento_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_formapagamento_conveniocobranca FOREIGN KEY (conveniocobranca)
		REFERENCES conveniocobranca (codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT ON DELETE RESTRICT
);

CREATE INDEX CH_FormaPagamento_tipoFormaPagamento ON FormaPagamento(tipoFormaPagamento);
CREATE INDEX CH_FormaPagamento_descricao ON FormaPagamento(descricao);
 
---------------------------------------------------------------- 
CREATE TABLE Pessoa 
(	
    webpage VARCHAR(50),    
    foto bytea,
    grauInstrucao int,
    sexo VARCHAR(2), 
    naturalidade VARCHAR(20), 
    nacionalidade VARCHAR(20), 
    estadoCivil VARCHAR(10), 
    pais INT, 
    estado INT,
    cidade INT, 
    rgUf VARCHAR(2), 
    rgOrgao VARCHAR(10), 
    rg VARCHAR(20), 
    cfp VARCHAR(14), 
    nomeMae VARCHAR(50), 
    nomePai VARCHAR(50), 
    dataNasc TIMESTAMP, 
    nome VARCHAR(50), 
    dataCadastro TIMESTAMP, 
    profissao INT,    
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_Pessoa_cidade FOREIGN KEY (cidade) 
		REFERENCES Cidade(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 	
	CONSTRAINT fk_pessoa_grauinstrucao FOREIGN KEY (grauinstrucao) 
		REFERENCES grauinstrucao (codigo) MATCH SIMPLE  
		ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT fk_pessoa_profissao FOREIGN KEY (profissao) 
		REFERENCES profissao(codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT fk_pessoa_pais FOREIGN KEY (pais) 
		REFERENCES pais(codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT fk_pessoa_estado FOREIGN KEY (estado) 
		REFERENCES estado(codigo) MATCH SIMPLE  
		ON UPDATE RESTRICT ON DELETE RESTRICT,	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Pessoa_cidade ON Pessoa(cidade);
CREATE INDEX CH_Pessoa_cfp ON Pessoa(cfp);
CREATE INDEX CH_Pessoa_nome ON Pessoa(nome);
CREATE INDEX CH_Pessoa_profissao ON Pessoa(profissao);

---------------------------------------------------------------- 
CREATE TABLE Colaborador
(	
    funcionario BOOLEAN, 
    codAcesso VARCHAR(10), 
    codAcessoAlternativo VARCHAR(15), 
    situacao VARCHAR(2) NOT NULL,    
    pessoa INT NOT NULL, 
    codigo SERIAL NOT NULL,
    empresa INT,	
    CONSTRAINT FK_colaborador_pessoa FOREIGN KEY (pessoa) 
		REFERENCES Pessoa(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_colaborador_empresa FOREIGN KEY (empresa) 
		REFERENCES empresa(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Colaborador_situacao ON Colaborador(situacao);
CREATE INDEX CH_Colaborador_pessoa ON Colaborador(pessoa);
CREATE INDEX CH_Colaborador_empresa ON Colaborador(empresa);

---------------------------------------------------------------- 
CREATE TABLE tipoColaborador
(
  codigo serial NOT NULL,
  colaborador integer NOT NULL,
  descricao character varying(2) NOT NULL,
  CONSTRAINT tipoColaborador_pkey PRIMARY KEY (codigo),
  CONSTRAINT fk_tipoColaborador_Colaborador FOREIGN KEY (colaborador) 
	REFERENCES colaborador (codigo) MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE
); 
 
---------------------------------------------------------------- 
CREATE TABLE QuestionarioPergunta 
(	
    pergunta INT NOT NULL, 
    questionario INT NOT NULL, 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_QuestionarioPergunta_pergunta FOREIGN KEY (pergunta) 
		REFERENCES Pergunta(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_QuestionarioPergunta_questionario FOREIGN KEY (questionario) 
		REFERENCES Questionario(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_QuestionarioPergunta_pergunta ON QuestionarioPergunta(pergunta);
CREATE INDEX CH_QuestionarioPergunta_questionario ON QuestionarioPergunta(questionario);
 
---------------------------------------------------------------- 
CREATE TABLE Turma 
(
    bloquearMatriculasAcimaLimite BOOLEAN, 
    empresa INT,
    idadeMaxima INT NOT NULL,
    idadeMaximaMeses INT DEFAULT(0),  
    idadeMinima INT NOT NULL, 
    idadeMinimaMeses INT DEFAULT(0),
    dataFinalVigencia TIMESTAMP, 
    dataInicialVigencia TIMESTAMP NOT NULL, 
    modalidade INT NOT NULL, 
    identificador VARCHAR(20) NOT NULL, 
    descricao VARCHAR(20), 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_Turma_modalidade FOREIGN KEY (modalidade) 
		REFERENCES Modalidade(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_Turma_empresa FOREIGN KEY (empresa) 
		REFERENCES empresa(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Turma_idadeMaxima ON Turma(idadeMaxima);
CREATE INDEX CH_Turma_idadeMinima ON Turma(idadeMinima);
CREATE INDEX CH_Turma_dataInicialVigencia ON Turma(dataInicialVigencia);
CREATE INDEX CH_Turma_modalidade ON Turma(modalidade);
CREATE INDEX CH_Turma_identificador ON Turma(identificador);

---------------------------------------------------------------- 
CREATE TABLE Usuario 
(	
    administrador BOOLEAN, 
    cliente integer,
    colaborador integer,
    tipousuario character varying(2),
    senha VARCHAR(64) NOT NULL, 
    username VARCHAR(10) NOT NULL UNIQUE, 
    nome VARCHAR(100) NOT NULL UNIQUE, 
    codigo SERIAL NOT NULL,	  
    PRIMARY KEY (codigo),
    CONSTRAINT FK_Usuario_colaborador FOREIGN KEY (colaborador) 
		REFERENCES Colaborador(codigo) 
		ON DELETE RESTRICT ON UPDATE NO ACTION    
);

CREATE INDEX CH_Usuario_username ON Usuario(username);
CREATE INDEX CH_Usuario_nome ON Usuario(nome);

 ---------------------------------------------------------------- 
CREATE TABLE ConvenioDesconto 
(
    pagaRematricula BOOLEAN, 
    pagaMatricula BOOLEAN, 
    dataAutorizacao TIMESTAMP NOT NULL, 
    responsavelAutorizacao INT, 
    descontoParcela Real,
    dataFinalVigencia TIMESTAMP NOT NULL, 
    dataInicioVigencia TIMESTAMP NOT NULL, 
    dataAssinatura TIMESTAMP NOT NULL, 
    descricao VARCHAR(50) NOT NULL, 
    codigo SERIAL NOT NULL, 
	CONSTRAINT fk_conveniodesconto_responsavelautorizacao FOREIGN KEY (responsavelautorizacao) 
		REFERENCES usuario (codigo) MATCH SIMPLE    
		ON UPDATE RESTRICT ON DELETE RESTRICT,	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_ConvenioDesconto_dataAutorizacao ON ConvenioDesconto(dataAutorizacao);
CREATE INDEX CH_ConvenioDesconto_responsavelAutorizacao ON ConvenioDesconto(responsavelAutorizacao);
CREATE INDEX CH_ConvenioDesconto_descontoParcela ON ConvenioDesconto(descontoParcela);
CREATE INDEX CH_ConvenioDesconto_dataFinalVigencia ON ConvenioDesconto(dataFinalVigencia);
CREATE INDEX CH_ConvenioDesconto_dataInicioVigencia ON ConvenioDesconto(dataInicioVigencia);
CREATE INDEX CH_ConvenioDesconto_dataAssinatura ON ConvenioDesconto(dataAssinatura);
CREATE INDEX CH_ConvenioDesconto_descricao ON ConvenioDesconto(descricao);
 
---------------------------------------------------------------- 
CREATE TABLE ConvenioDescontoConfiguracao 
(
    convenioDesconto INT, 
    tipoDesconto VARCHAR(2) NOT NULL, 
    porcentagemDesconto REAL NOT NULL, 
    valorDesconto REAL NOT NULL, 
    duracao INT NOT NULL, 
    codigo SERIAL NOT NULL, 
    CONSTRAINT FK_ConvenioDescontoConfiguracao_convenioDesconto FOREIGN KEY (convenioDesconto) 
		REFERENCES ConvenioDesconto(codigo) ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_ConvenioDescontoConfiguracao_tipoDesconto ON ConvenioDescontoConfiguracao(tipoDesconto);
CREATE INDEX CH_ConvenioDescontoConfiguracao_porcentagemDesconto ON ConvenioDescontoConfiguracao(porcentagemDesconto);
CREATE INDEX CH_ConvenioDescontoConfiguracao_valorDesconto ON ConvenioDescontoConfiguracao(valorDesconto);
CREATE INDEX CH_ConvenioDescontoConfiguracao_duracao ON ConvenioDescontoConfiguracao(duracao); 

---------------------------------------------------------------- 
CREATE TABLE Cliente 
(
    freepass INT,	
    responsavelfreepass integer,
    codigomatricula INT,
    identificadorParaCobranca VARCHAR(20), 
    contaDigito VARCHAR(10), 
    conta VARCHAR(20), 
    agenciaDigito VARCHAR(10), 
    agencia VARCHAR(20), 
    banco VARCHAR(30), 
    codAcesso VARCHAR(20),
    codAcessoAlternativo VARCHAR(25), 
    categoria INT, 
    matricula VARCHAR(10), 
    situacao VARCHAR(2), 
    pessoa INT NOT NULL, 
    codigo SERIAL NOT NULL,
    empresa INT,
    CONSTRAINT FK_Cliente_categoria FOREIGN KEY (categoria) 
		REFERENCES Categoria(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_Cliente_pessoa FOREIGN KEY (pessoa) 
		REFERENCES Pessoa(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_Cliente_empresa FOREIGN KEY (empresa) 
		REFERENCES empresa(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 	
    CONSTRAINT fk_cliente_freepass FOREIGN KEY (freepass) 
		REFERENCES produto(codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT fk_cliente_responsavelfreepass FOREIGN KEY (responsavelfreepass)  
		REFERENCES usuario (codigo) MATCH SIMPLE  
		ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT un_cliente_pessoa UNIQUE(pessoa),
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Cliente_conta ON Cliente(conta);
CREATE INDEX CH_Cliente_agencia ON Cliente(agencia);
CREATE INDEX CH_Cliente_banco ON Cliente(banco);
CREATE INDEX CH_Cliente_codAcesso ON Cliente(codAcesso);
CREATE INDEX CH_Cliente_categoria ON Cliente(categoria);
CREATE INDEX CH_Cliente_matricula ON Cliente(matricula);
CREATE INDEX CH_Cliente_situacao ON Cliente(situacao);
CREATE INDEX CH_Cliente_pessoa ON Cliente(pessoa);
CREATE INDEX CH_Cliente_empresa ON Cliente(empresa);

ALTER TABLE usuario ADD CONSTRAINT FK_Usuario_cliente FOREIGN KEY (cliente) REFERENCES Cliente(codigo) ON DELETE RESTRICT ON UPDATE NO ACTION;

---------------------------------------------------------------- 
CREATE TABLE Vinculo 
(
    colaborador INT NOT NULL, 
    tipoVinculo VARCHAR(2),
    cliente INT NOT NULL, 
    codigo SERIAL NOT NULL, 
    CONSTRAINT FK_Vinculo_colaborador FOREIGN KEY (colaborador) 
		REFERENCES Colaborador(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_Vinculo_cliente FOREIGN KEY (cliente) 
		REFERENCES Cliente(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Vinculo_colaborador ON Vinculo(colaborador);
CREATE INDEX CH_Vinculo_cliente ON Vinculo(cliente);

------------------------------------------------------------------
CREATE TABLE PlanoTextoPadrao 
(
    texto TEXT, 
    situacao VARCHAR(2), 
    responsavelDefinicao INT NOT NULL, 
    dataDefinicao TIMESTAMP NOT NULL, 
    descricao VARCHAR(45) NOT NULL, 
	imagemLogo bytea,
    codigo SERIAL NOT NULL, 
    CONSTRAINT FK_PlanoTextoPadrao_responsavelDefinicao FOREIGN KEY (responsavelDefinicao) 
		REFERENCES usuario(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_PlanoTextoPadrao_responsavelDefinicao ON PlanoTextoPadrao(responsavelDefinicao);
CREATE INDEX CH_PlanoTextoPadrao_dataDefinicao ON PlanoTextoPadrao(dataDefinicao);
CREATE INDEX CH_PlanoTextoPadrao_descricao ON PlanoTextoPadrao(descricao); 
 
---------------------------------------------------------------- 
CREATE TABLE Plano 
(
    recibotextopadrao integer,
    permitepagarcomboleto BOOLEAN,
    percentualMultaCancelamento real,
    produtoTaxaCancelamento INT NOT NULL,     
    planoTextoPadrao INT NOT NULL,     
    produtoPadraoGerarParcelasContrato INT NOT NULL,     
    bolsa BOOLEAN, 
    permiteAcessoHorarioTurma BOOLEAN, 
    permiteAcessoHorarioPlano BOOLEAN, 
    ingressoAte TIMESTAMP, 
    vigenciaAte TIMESTAMP, 
    vigenciaDe TIMESTAMP, 
    empresa INT, 
    descricao VARCHAR(50) NOT NULL, 
    codigo SERIAL NOT NULL, 
    prorataObrigatorio BOOLEAN NOT NULL DEFAULT FALSE,
    diasVencimentoProrata CHARACTER VARYING(20) NOT NULL DEFAULT '',
	CONSTRAINT FK_plano_planotextopadrao FOREIGN KEY (planotextopadrao) 
		REFERENCES planotextopadrao (codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
	CONSTRAINT FK_plano_empresa FOREIGN KEY (empresa) 
		REFERENCES empresa (codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
	CONSTRAINT fk_plano_produtotaxacancelamento FOREIGN KEY (produtotaxacancelamento) 
		REFERENCES produto (codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_plano_produtopadraogerarparcelascontrato FOREIGN KEY (produtopadraogerarparcelascontrato) 
		REFERENCES produto (codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_plano_recibotextopadrao FOREIGN KEY (recibotextopadrao) 
		REFERENCES planotextopadrao (codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT ON DELETE RESTRICT,
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Plano_ingressoAte ON Plano(ingressoAte);
CREATE INDEX CH_Plano_vigenciaAte ON Plano(vigenciaAte);
CREATE INDEX CH_Plano_vigenciaDe ON Plano(vigenciaDe);
CREATE INDEX CH_Plano_empresa ON Plano(empresa);
CREATE INDEX CH_Plano_descricao ON Plano(descricao);
 
---------------------------------------------------------------- 
CREATE TABLE PlanoModalidade 
(
    listavezessemana VARCHAR(255),
    modalidade INT NOT NULL, 
    plano INT NOT NULL, 
    codigo SERIAL NOT NULL, 
    CONSTRAINT FK_PlanoModalidade_modalidade FOREIGN KEY (modalidade) 
		REFERENCES Modalidade(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_PlanoModalidade_plano FOREIGN KEY (plano) 
		REFERENCES Plano(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_PlanoModalidade_modalidade ON PlanoModalidade(modalidade);
CREATE INDEX CH_PlanoModalidade_plano ON PlanoModalidade(plano);
 
---------------------------------------------------------------- 
CREATE TABLE PlanoModalidadeVezesSemana 
(	
    nrvezes INT NOT NULL,
    tipooperacao VARCHAR(2),
    tipoValor VARCHAR(2), 
    valorEspecifico REAL, 
    percentualDesconto REAL, 
    planoModalidade INT NOT NULL, 
    codigo SERIAL NOT NULL, 
	CONSTRAINT FK_PlanoModalidadeVezesSemana_planoModalidade 
		FOREIGN KEY (planoModalidade) 
		REFERENCES planoModalidade(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_PlanoModalidadeVezesSemana_percentualDesconto ON PlanoModalidadeVezesSemana (percentualDesconto);
CREATE INDEX CH_PlanoModalidadeVezesSemana_planoModalidade ON PlanoModalidadeVezesSemana(planoModalidade);

---------------------------------------------------------------- 
CREATE TABLE PlanoDuracao 
(	
    valordesejadomensal REAL, 
    valordesejadoparcela REAL, 
    valordesejado real,
    tipooperacao VARCHAR(2),
    tipoValor VARCHAR(2), 
    valorEspecifico REAL, 
    percentualDesconto REAL, 
    nrMaximoParcelasCondPagamento INT NOT NULL, 
    plano INT NOT NULL, 
    numeromeses INT NOT NULL,
    carencia INT NOT NULL,  
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_PlanoDuracao_plano FOREIGN KEY (plano) 
		REFERENCES Plano(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_PlanoDuracao_percentualDesconto ON PlanoDuracao (percentualDesconto);
CREATE INDEX CH_PlanoDuracao_nrMaximoParcelasCondPagamento ON PlanoDuracao(nrMaximoParcelasCondPagamento);
CREATE INDEX CH_PlanoDuracao_plano ON PlanoDuracao(plano);

---------------------------------------------------------------- 
CREATE TABLE PlanoCondicaoPagamento 
(
    percentualDesconto REAL, 
	qtdParcela integer,
    condicaoPagamento INT NOT NULL, 
    planoDuracao integer, 
    codigo SERIAL NOT NULL, 
    tipooperacao character varying(2),
    tipovalor character varying(2),
    valorespecifico real,	
    CONSTRAINT FK_PlanoCondicaoPagamento_condicaoPagamento 
		FOREIGN KEY (condicaoPagamento) 
		REFERENCES CondicaoPagamento(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_PlanoCondicaoPagamento_planoduracao 
		FOREIGN KEY (planoDuracao) 
		REFERENCES  planoDuracao (codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_PlanoCondicaoPagamento_condicaoPagamento ON PlanoCondicaoPagamento(condicaoPagamento);
CREATE INDEX CH_PlanoCondicaoPagamento_planoDuracao ON PlanoCondicaoPagamento(planoDuracao);
 
---------------------------------------------------------------- 
CREATE TABLE PlanoTextoPadraoTag 
(
    tag text, 
    planotextopadrao INT NOT NULL, 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_PlanoTextoPadrao_planotextopadrao FOREIGN KEY (planotextopadrao) 
		REFERENCES planoTextoPadrao(codigo) ON DELETE CASCADE ON UPDATE CASCADE, 	
    PRIMARY KEY (codigo)
);

---------------------------------------------------------------- 
CREATE TABLE Telefone 
(
    pessoa INT NOT NULL, 
    tipoTelefone VARCHAR(2) NOT NULL, 
    numero VARCHAR(15) NOT NULL, 
    codigo SERIAL NOT NULL, 
    CONSTRAINT FK_Telefone_pessoa 
		FOREIGN KEY (pessoa) 
		REFERENCES Pessoa(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Telefone_pessoa ON Telefone(pessoa);
CREATE INDEX CH_Telefone_tipoTelefone ON Telefone(tipoTelefone);
CREATE INDEX CH_Telefone_numero ON Telefone(numero);
 
---------------------------------------------------------------- 
CREATE TABLE HorarioTurma 
(
   diasemananumero INT,
    nrAlunoMatriculado INT, 	
    nrMaximoAluno INT, 
	identificadorturma VARCHAR(100), 
    situacao VARCHAR(2) NOT NULL, 
    diaSemana VARCHAR(2) NOT NULL, 
    nivelTurma INT NOT NULL, 
    ambiente INT NOT NULL, 
    professor INT NOT NULL, 
    horaFinal VARCHAR(5), 
    horaInicial VARCHAR(5), 
    turma INT NOT NULL, 
    codigo SERIAL NOT NULL, 
    CONSTRAINT FK_HorarioTurma_nivelTurma 
		FOREIGN KEY (nivelTurma) 
		REFERENCES NivelTurma(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_HorarioTurma_ambiente 
		FOREIGN KEY (ambiente) 
		REFERENCES Ambiente(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_HorarioTurma_professor 
		FOREIGN KEY (professor) 
		REFERENCES Colaborador(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_HorarioTurma_turma 
		FOREIGN KEY (turma) 
		REFERENCES Turma(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_HorarioTurma_situacao ON HorarioTurma(situacao);
CREATE INDEX CH_HorarioTurma_nivelTurma ON HorarioTurma(nivelTurma);
CREATE INDEX CH_HorarioTurma_ambiente ON HorarioTurma(ambiente);
CREATE INDEX CH_HorarioTurma_professor ON HorarioTurma(professor);
CREATE INDEX CH_HorarioTurma_horaInicial ON HorarioTurma(horaInicial);
CREATE INDEX CH_HorarioTurma_turma ON HorarioTurma(turma);

---------------------------------------------------------------- 
CREATE TABLE ProdutoSugerido 
(
    Obrigatorio BOOLEAN, 
    produto INT NOT NULL, 
    modalidade INT NOT NULL, 
    codigo SERIAL NOT NULL, 
    CONSTRAINT FK_ProdutoSugerido_produto 
		FOREIGN KEY (produto) 
		REFERENCES Produto(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_ProdutoSugerido_modalidade 
		FOREIGN KEY (modalidade) 
		REFERENCES Modalidade(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_ProdutoSugerido_produto ON ProdutoSugerido(produto);
CREATE INDEX CH_ProdutoSugerido_modalidade ON ProdutoSugerido(modalidade);

---------------------------------------------------------------- 
CREATE TABLE PlanoProdutoSugerido
(
    Obrigatorio BOOLEAN, 
    produto INT NOT NULL, 
    valorproduto real,
    plano INT NOT NULL, 
    codigo SERIAL NOT NULL, 
    CONSTRAINT FK_PlanoProdutoSugerido_produto 
		FOREIGN KEY (produto) 
		REFERENCES Produto(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_PlanoProdutoSugerido_plano 
		FOREIGN KEY (plano) 
		REFERENCES Plano(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_PlanoProdutoSugerido_produto ON PlanoProdutoSugerido(produto);
CREATE INDEX CH_PlanoProdutoSugerido_plano ON PlanoProdutoSugerido(plano);
 
---------------------------------------------------------------- 
CREATE TABLE ClienteClassificacao 
(
    classificacao INT, 
    cliente INT NOT NULL, 
    codigo SERIAL NOT NULL, 
    CONSTRAINT FK_ClienteClassificacao_cliente 
		FOREIGN KEY (cliente) 
		REFERENCES Cliente(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
    CONSTRAINT fk_clienteclassificacao_classificacao 
		FOREIGN KEY (classificacao)  
		REFERENCES classificacao (codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT  ON DELETE RESTRICT,
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_ClienteClassificacao_classificacao ON ClienteClassificacao(classificacao);
CREATE INDEX CH_ClienteClassificacao_cliente ON ClienteClassificacao(cliente);
 
---------------------------------------------------------------
CREATE TABLE OperadoraCartao 
(	
    descricao VARCHAR(100) NOT NULL, 
    codigoOperadora INT NOT NULL,
    codigoIntegracao INT,
    codigo SERIAL NOT NULL, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_OperadoraCartao_descricao ON OperadoraCartao(descricao);
CREATE INDEX CH_OperadoraCartao_codigoOperadora ON OperadoraCartao(codigoOperadora);

---------------------------------------------------------------- 
CREATE TABLE MovPagamento 
(
    responsavelpagamento INT NOt NULL,
    nrparcelacartaocredito INT, 
    movpagamentoescolhida  boolean,
    operadoraCartao INT,     
    nomePagador VARCHAR(50) NOT NULL, 
    formaPagamento INT NOT NULL, 
    valor REAL, 
    dataLancamento TIMESTAMP NOT NULL, 
    dataPagamento TIMESTAMP NOT NULL, 
    pessoa INT, 
    codigo SERIAL NOT NULL, 
	dataquitacao DATE,
	convenioCobranca INT,
    CONSTRAINT FK_MovPagamento_formaPagamento 
		FOREIGN KEY (formaPagamento) 
		REFERENCES FormaPagamento(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
	CONSTRAINT FK_MovPagamento_operadoraCartao 
		FOREIGN KEY (operadoraCartao) 
		REFERENCES operadoraCartao(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_MovPagamento_pessoa 
		FOREIGN KEY (pessoa) 
		REFERENCES Pessoa(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT fk_movpagamento_responsavelpagamento 
		FOREIGN KEY (responsavelpagamento) 
		REFERENCES usuario (codigo) MATCH SIMPLE 
		ON DELETE RESTRICT ON UPDATE RESTRICT,	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_MovPagamento_operadoraCartao ON MovPagamento(operadoraCartao);
CREATE INDEX CH_MovPagamento_nomePagador ON MovPagamento(nomePagador);
CREATE INDEX CH_MovPagamento_formaPagamento ON MovPagamento(formaPagamento);
CREATE INDEX CH_MovPagamento_dataLancamento ON MovPagamento(dataLancamento);
CREATE INDEX CH_MovPagamento_dataPagamento ON MovPagamento(dataPagamento);
CREATE INDEX CH_MovPagamento_pessoa ON MovPagamento(pessoa);

----------------------------------------------------------------
CREATE TABLE Cheque 
(   
    vistaouprazo character varying(2),
    datacompesancao  timestamp without time zone,
    valor REAL NOT NULL, 
    banco VARCHAR(50) NOT NULL, 
    agencia VARCHAR(50) NOT NULL, 
    conta VARCHAR(50) NOT NULL, 
    numero VARCHAR(50) NOT NULL,
    MovPagamento Int NOT NULL, 
    codigo SERIAL NOT NULL, 
    cpf varchar(14),
    cnpj varchar(20),
	situacao character varying(2),
    CONSTRAINT FK_cheque_MovPagamento 
		FOREIGN KEY (MovPagamento) 
		REFERENCES MovPagamento(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE,  
    PRIMARY KEY (codigo)
);
 
 -----------------------------------------------------------------
 CREATE TABLE CartaoCredito 
 (
    dataCompesancao TIMESTAMP,     
    movPagamento INT, 
    valor REAL,     
    codigo SERIAL NOT NULL, 
	CONSTRAINT FK_CartaoCredito_MovPagamento 
		FOREIGN KEY (MovPagamento) 
		REFERENCES MovPagamento(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE,  
    PRIMARY KEY (codigo)
);

---------------------------------------------------------------- 
CREATE TABLE Endereco 
(
    pessoa INT NOT NULL, 
    tipoEndereco VARCHAR(2) NOT NULL, 
    cep VARCHAR(10), 
    bairro VARCHAR(35), 
    numero VARCHAR(10), 
    complemento VARCHAR(40), 
    endereco VARCHAR(40), 
    enderecoCorrespondencia BOOLEAN,
    codigo SERIAL NOT NULL, 
    CONSTRAINT FK_Endereco_pessoa 
		FOREIGN KEY (pessoa) 
		REFERENCES Pessoa(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codigo)
);
 
---------------------------------------------------------------- 
CREATE TABLE Email 
(
    pessoa INT NOT NULL,
    emailCorrespondencia Boolean, 
    email VARCHAR(50), 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_email_pessoa 
		FOREIGN KEY (pessoa) 
		REFERENCES Pessoa(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Endereco_pessoa ON Endereco(pessoa);
CREATE INDEX CH_Endereco_tipoEndereco ON Endereco(tipoEndereco);
 
---------------------------------------------------------------- 
CREATE TABLE Composicao 
(
   composicaoDefault BOOLEAN, 
   composicaoAdicional BOOLEAN, 
   precoComposicao REAL, 
   descricao VARCHAR(45) NOT NULL,     
   empresa INT NOT NULL, 
   codigo SERIAL NOT NULL,  
   CONSTRAINT FK_Composicao_empresa 
	FOREIGN KEY (empresa) 
	REFERENCES empresa (codigo) 
	ON DELETE RESTRICT ON UPDATE RESTRICT,  
   PRIMARY KEY (codigo)
);
CREATE INDEX CH_Composicao_descricao ON Composicao(descricao);

---------------------------------------------------------------- 
CREATE TABLE Contrato 
(	
    bolsa BOOLEAN,
	naopermitirrenovacaorematriculadecontratoanteriores boolean,
	valordescontoespecifico real,
	valordescontoporcentagem real,
	nomemodalidades VARCHAR(50),
	somaproduto real,
	contratoresponsavelrenovacaomatricula integer,
    contratoresponsavelrematriculamatricula integer,
    dataLancamento TIMESTAMP,
    dataMatricula TIMESTAMP,
    dataPrevistaRenovar TIMESTAMP,
    dataRenovarRealizada TIMESTAMP,
    dataPrevistaRematricula TIMESTAMP,
    dataRematriculaRealizada TIMESTAMP,
    situacaoContrato VARCHAR(2),
    situacaoRenovacao VARCHAR(2),
    situacaoRematricula VARCHAR(2),
    contratoBaseadoRenovacao INT,
    contratoBaseadoRematricula INT,
    pagarcomboleto BOOLEAN, 
    responsavelcontrato INT NOT NULL,
    observacao text,
    responsavelLiberacaoCondicaoPagamento INT , 
    valorFinal REAL, 
    valorBaseCalculo REAL,     
    vigenciaAte TIMESTAMP, 
	vigenciaateajustada TIMESTAMP, 
    vigenciaDe TIMESTAMP, 
    estendeCoberturaFamiliares BOOLEAN, 
    situacao VARCHAR(2) NOT NULL, 
    plano INT NOT NULL, 
    pessoa INT NOT NULL, 
    consultor INT , 
	empresa INT NOT NULL, 
    codigo SERIAL NOT NULL, 
    conveniodesconto integer,
    dividirProdutosNasParcelas BOOLEAN,
    desconto INT, 
    tipoDesconto VARCHAR(2) NOT NULL, 
    valorDesconto REAL,
	diaVencimentoProrata INTEGER DEFAULT 0,   
    CONSTRAINT FK_Contrato_consultor 
		FOREIGN KEY (consultor) 
		REFERENCES Colaborador(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
	CONSTRAINT FK_Contrato_plano 
		FOREIGN KEY (plano) 
		REFERENCES Plano(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_Contrato_pessoa 
		FOREIGN KEY (pessoa) 
		REFERENCES Pessoa(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_Contrato_empresa 
		FOREIGN KEY (empresa) 
		REFERENCES Empresa(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT fk_contrato_desconto 
		FOREIGN KEY (desconto) 
		REFERENCES produto (codigo) MATCH SIMPLE 
		ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT fk_contrato_conveniodesconto 
		FOREIGN KEY (conveniodesconto)  
		REFERENCES conveniodesconto (codigo) MATCH SIMPLE  
		ON UPDATE RESTRICT ON DELETE RESTRICT,	
    PRIMARY KEY (codigo)
);

CREATE INDEX CH_Contrato_situacao ON Contrato(situacao);
CREATE INDEX CH_Contrato_plano ON Contrato(plano);
CREATE INDEX CH_Contrato_pessoa ON Contrato(pessoa);
CREATE INDEX CH_Contrato_empresa ON Contrato(empresa);


---------------------------------------------------------------------
CREATE TABLE recibopagamento
(
  codigo serial NOT NULL,
  valortotal real NOT NULL,
  pessoapagador integer,
  nomepessoapagador character varying(50),
  responsavelLancamento integer NOT NULL,
  contrato integer,
  data timestamp without time zone NOT NULL,
  CONSTRAINT recibopagamento_pkey PRIMARY KEY (codigo),
  CONSTRAINT fk_reciboPagamento_pessoa 
	FOREIGN KEY (pessoapagador) 
	REFERENCES pessoa (codigo) MATCH SIMPLE 
	ON UPDATE CASCADE ON DELETE CASCADE,
  CONSTRAINT fk_reciboPagamento_usuario FOREIGN KEY (responsavelLancamento) 
	REFERENCES usuario (codigo) MATCH SIMPLE 
	ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_recibopagamento_contrato FOREIGN KEY (contrato) 
	REFERENCES contrato (codigo) MATCH SIMPLE 
	ON UPDATE CASCADE ON DELETE CASCADE
);
CREATE INDEX CH_Recibopagamento_contrato ON Recibopagamento(contrato);
CREATE INDEX CH_Recibopagamento_pessoapagador ON Recibopagamento(pessoapagador);

----------------------------------------------------------------
CREATE TABLE contratoplanoprodutosugerido
(	
	codigo SERIAL NOT NULL,
	valorfinalproduto real,
	contrato INT, 
	planoprodutosugerido INT,
	CONSTRAINT fk_contratoplanoprodutosugerido_contrato 
		FOREIGN KEY (contrato) 
		REFERENCES contrato (codigo) MATCH SIMPLE 
		ON UPDATE CASCADE ON DELETE CASCADE,
	CONSTRAINT fk_contratoplanoprodutosugerido_planoprodutosugerido FOREIGN KEY (planoprodutosugerido) 
		REFERENCES planoprodutosugerido (codigo) MATCH SIMPLE  
		ON UPDATE RESTRICT ON DELETE RESTRICT,	
    PRIMARY KEY (codigo)
); 

------------------------------------------------------------
CREATE TABLE vendaAvulsa
(
  tipocomprador character varying(2),
  nomecomprador character varying(50),
  dataregistro timestamp without time zone,
  cliente integer,
  colaborador integer,
  empresa integer,
  responsavel integer,
  valortotal real,
  codigo  serial NOT NULL,
  CONSTRAINT FK_vendaAvulsa_empresa 
	FOREIGN KEY (empresa) 
	REFERENCES Empresa(codigo) 
	ON DELETE RESTRICT ON UPDATE RESTRICT, 
  CONSTRAINT FK_vendaAvulsa_responsavel 
	FOREIGN KEY (responsavel) 
	REFERENCES Usuario(codigo) 
	ON DELETE RESTRICT ON UPDATE RESTRICT, 
  CONSTRAINT FK_vendaAvulsa_cliente 
	FOREIGN KEY (cliente) 
	REFERENCES Cliente(codigo) 
	ON DELETE RESTRICT ON UPDATE RESTRICT, 
  CONSTRAINT FK_vendaAvulsa_colaborador 
	FOREIGN KEY (colaborador) 
	REFERENCES Colaborador(codigo) 
	ON DELETE RESTRICT ON UPDATE RESTRICT, 
  CONSTRAINT vendaAvulsa_pkey PRIMARY KEY (codigo)
) ;

------------------------------------------------------------
CREATE TABLE itemvendaavulsa
(
  codigo serial NOT NULL,
  produto integer,
  quantidade integer, 
  vendaavulsa integer,
  valorparcial real,  
  tabeladesconto integer,
  CONSTRAINT FK_itemvendaavulsa_vendaAvulsa 
	FOREIGN KEY (vendaAvulsa) 
	REFERENCES VendaAvulsa(codigo) 
	ON DELETE CASCADE ON UPDATE CASCADE, 
  CONSTRAINT FK_itemvendaavulsa_produto 
	FOREIGN KEY (produto) 
	REFERENCES Produto(codigo) 
	ON DELETE RESTRICT ON UPDATE RESTRICT, 
  CONSTRAINT FK_itemvendaavulsa_tabelaDesconto 
	FOREIGN KEY (tabeladesconto) 
	REFERENCES Desconto(codigo) 
	ON DELETE RESTRICT ON UPDATE RESTRICT, 
  CONSTRAINT itemvendaavulsa_pkey PRIMARY KEY (codigo)
) ;

--------------------------------------------------------------------------
CREATE TABLE aulaavulsadiaria
(
	codigo serial NOT NULL,
	empresa integer,
	cliente integer,  
	produto integer,
	modalidade integer,
	responsavel integer,
	dataregistro timestamp without time zone NOT NULL,
	valor real,  
	nomecomprador character varying(40),
	CONSTRAINT aulaavulsadiaria_pkey PRIMARY KEY (codigo),  
	CONSTRAINT fk_aulaavulsadiaria_cliente 
		FOREIGN KEY (cliente)
		REFERENCES cliente (codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT ON DELETE RESTRICT,  
	CONSTRAINT fk_aulaavulsadiaria_empresa 
		FOREIGN KEY (empresa)  
		REFERENCES empresa (codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_aulaavulsadiaria_modalidade 
		FOREIGN KEY (modalidade)  
		REFERENCES modalidade (codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_aulaavulsadiaria_produto 
		FOREIGN KEY (produto)    
		REFERENCES produto (codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT ON DELETE RESTRICT,  
	CONSTRAINT fk_aulaavulsadiaria_usuario 
		FOREIGN KEY (responsavel)
		REFERENCES usuario (codigo) MATCH SIMPLE      
		ON UPDATE RESTRICT ON DELETE RESTRICT
);

---------------------------------------------------------------- 
CREATE TABLE MovParcela 
(	
    descricao VARCHAR(80),
    imprimirBoletoParcela BOOLEAN, 
    convenioCobranca INT , 
    utilizaConvenio BOOLEAN, 
    vendaavulsa INT, 
    aulaavulsadiaria INT, 
    percentualJuro REAL, 
    percentualMulta REAL, 
    responsavel INT NOT NULL, 
    valorparcela real NOT NULL,
    contrato INT,
    situacao VARCHAR(2) NOT NULL, 
    dataVencimento TIMESTAMP, 
    dataRegistro TIMESTAMP NOT NULL, 
    codigo SERIAL NOT NULL,
    CONSTRAINT fk_movparcela_aulaavulsadiaria 
		FOREIGN KEY (aulaavulsadiaria) 
		REFERENCES Aulaavulsadiaria (codigo) 
		ON UPDATE CASCADE ON DELETE CASCADE,
    CONSTRAINT fk_movparcela_vendaavulsa 
		FOREIGN KEY (vendaavulsa) 
		REFERENCES vendaavulsa (codigo) MATCH SIMPLE 
		ON UPDATE CASCADE ON DELETE CASCADE,
	CONSTRAINT fk_movparcela_contrato 
		FOREIGN KEY (contrato) 
		REFERENCES contrato (codigo) MATCH SIMPLE 
		ON UPDATE CASCADE ON DELETE CASCADE,
    CONSTRAINT fk_movparcela_conveniocobranca 
		FOREIGN KEY (conveniocobranca) 
		REFERENCES conveniocobranca (codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT fk_movparcela_responsavel 
		FOREIGN KEY (responsavel) 
		REFERENCES usuario (codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT ON DELETE RESTRICT,
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_MovParcela_convenioCobranca ON MovParcela(convenioCobranca);
CREATE INDEX CH_MovParcela_responsavel ON MovParcela(responsavel);
CREATE INDEX CH_MovParcela_situacao ON MovParcela(situacao);
CREATE INDEX CH_MovParcela_dataRegistro ON MovParcela(dataRegistro);
  
---------------------------------------------------------------- 
CREATE TABLE ContratoModalidade 
(	
    vezessemana integer,
	valormodalidade Real,
    valorFinalModalidade REAL, 
    valorBaseCalculo REAL,     
    modalidade INT NOT NULL, 
    contrato INT NOT NULL, 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_ContratoModalidade_modalidade 
		FOREIGN KEY (modalidade) 
		REFERENCES Modalidade(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_ContratoModalidade_contrato 
		FOREIGN KEY (contrato) 
		REFERENCES Contrato(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codigo)
);

CREATE INDEX CH_ContratoModalidade_modalidade ON ContratoModalidade(modalidade);
CREATE INDEX CH_ContratoModalidade_contrato ON ContratoModalidade(contrato);

----------------------------------------------------------------
CREATE TABLE contratomodalidadeprodutosugerido
(	
   codigo SERIAL NOT NULL,
   valorfinalproduto real,
   contratomodalidade INT, 
   produtosugerido INT,
	CONSTRAINT fk_contratomodalidadeprodutosugerido_contratomodalidade 
		FOREIGN KEY (contratomodalidade) 
		REFERENCES contratomodalidade (codigo) MATCH SIMPLE 
		ON UPDATE CASCADE ON DELETE CASCADE,
	CONSTRAINT fk_contratomodalidadeprodutosugerido_produtosugerido 
		FOREIGN KEY (produtosugerido) 
		REFERENCES produtosugerido (codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT ON DELETE RESTRICT,
    PRIMARY KEY (codigo)
);

---------------------------------------------------------------- 
CREATE TABLE ContratoModalidadeTurma 
(
    turma INT NOT NULL, 
    contratoModalidade INT NOT NULL, 
    codigo SERIAL NOT NULL, 
    CONSTRAINT FK_ContratoModalidadeTurma_turma 
		FOREIGN KEY (turma) 
		REFERENCES Turma (codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT fk_contratomodalidadeturma_contratomodalidade 
		FOREIGN KEY (contratomodalidade) 
		REFERENCES contratomodalidade (codigo) MATCH SIMPLE 
		ON UPDATE CASCADE ON DELETE CASCADE,
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_ContratoModalidadeTurma_turma ON ContratoModalidadeTurma(turma);
CREATE INDEX CH_ContratoModalidadeTurma_contratoModalidade ON ContratoModalidadeTurma(contratoModalidade);

---------------------------------------------------------------- 
CREATE TABLE ContratoModalidadeHorarioTurma 
(
    horarioTurma INT NOT NULL, 
    contratoModalidadeTurma INT NOT NULL, 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_ContratoModalidadeHorarioTurma_contratoModalidadeTurma 
		FOREIGN KEY (contratoModalidadeTurma) 
		REFERENCES ContratoModalidadeTurma(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
	CONSTRAINT fk_contratomodalidadehorarioturma_horarioturma 
		FOREIGN KEY (horarioturma) 
		REFERENCES horarioturma (codigo) MATCH SIMPLE  
		ON UPDATE RESTRICT ON DELETE RESTRICT,
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_ContratoModalidadeHorarioTurma_horarioTurma ON ContratoModalidadeHorarioTurma(horarioTurma);
CREATE INDEX CH_ContratoModalidadeHorarioTurma_contratoModalidadeTurma ON ContratoModalidadeHorarioTurma(contratoModalidadeTurma);
  
---------------------------------------------------------------- 
CREATE TABLE MovProduto 
(	
    situacao VARCHAR(2), 	
	quitado boolean,
    dataFinalVigencia TIMESTAMP, 
    dataInicioVigencia TIMESTAMP, 
    apresentarmovproduto boolean,
    anoReferencia INT, 
    mesReferencia VARCHAR(10), 
    responsavelLancamento INT NOT NULL, 
    dataLancamento TIMESTAMP, 
    totalFinal REAL, 
    valorDesconto REAL, 
    precoUnitario REAL, 
    quantidade INT, 
    descricao VARCHAR(80) NOT NULL, 
    empresa INT NOT NULL, 
    pessoa INT, 
    contrato INT, 
    produto INT NOT NULL, 
    codigo SERIAL NOT NULL,  	
    CONSTRAINT FK_MovProduto_responsavelLancamento 
		FOREIGN KEY (responsavelLancamento) 
		REFERENCES usuario(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_MovProduto_empresa 
		FOREIGN KEY (empresa) 
		REFERENCES Empresa(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_MovProduto_pessoa 
		FOREIGN KEY (pessoa) 
		REFERENCES Pessoa(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_MovProduto_contrato 
		FOREIGN KEY (contrato) 
		REFERENCES Contrato(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
    CONSTRAINT FK_MovProduto_produto 
		FOREIGN KEY (produto) 
		REFERENCES Produto(codigo)
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_MovProduto_responsavelLancamento ON MovProduto(responsavelLancamento);
CREATE INDEX CH_MovProduto_descricao ON MovProduto(descricao);
CREATE INDEX CH_MovProduto_empresa ON MovProduto(empresa);
CREATE INDEX CH_MovProduto_pessoa ON MovProduto(pessoa);
CREATE INDEX CH_MovProduto_contrato ON MovProduto(contrato);
CREATE INDEX CH_MovProduto_produto ON MovProduto(produto);

---------------------------------------------------------------- 
CREATE TABLE MovimentoContaCorrenteCliente 
(	
    responsavelAutorizacao INT NOT NULL,      
    dataRegistro TIMESTAMP, 
	tipomovimentacao character varying(2),
	recibopagamento INT,	
    descricao text, 
    saldoAtual REAL,     
	valor REAL,     
    pessoa INT NOT NULL, 
    codigo SERIAL NOT NULL, 
    CONSTRAINT FK_MovimentoContaCorrenteCliente_Pessoa 
		FOREIGN KEY (pessoa) 
		REFERENCES Pessoa(codigo) 
		ON DELETE CASCADE ON UPDATE RESTRICT, 
	CONSTRAINT fk_movimentocontacorrentecliente_responsavelautorizacao 
		FOREIGN KEY (responsavelautorizacao)  
		REFERENCES usuario (codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT fk_movimentocontacorrentecliente_recibopagamento 
		FOREIGN KEY (recibopagamento) 
		REFERENCES recibopagamento (codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT ON DELETE RESTRICT,
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_MovimentoContaCorrenteCliente_responsavelAutorizacao ON MovimentoContaCorrenteCliente(responsavelAutorizacao);
CREATE INDEX CH_MovimentoContaCorrenteCliente_dataRegistro ON MovimentoContaCorrenteCliente(dataRegistro);
CREATE INDEX CH_MovimentoContaCorrenteCliente_descricao ON MovimentoContaCorrenteCliente(descricao);
CREATE INDEX CH_MovimentoContaCorrenteCliente_saldoAtual ON MovimentoContaCorrenteCliente(saldoAtual);
CREATE INDEX CH_MovimentoContaCorrenteCliente_cliente ON MovimentoContaCorrenteCliente(pessoa);
 
---------------------------------------------------------------- 
CREATE TABLE PlanoHorario 
(	
    tipooperacao VARCHAR(2),
    tipoValor VARCHAR(2), 
    valorEspecifico REAL, 
    percentualDesconto REAL, 
    plano INT NOT NULL, 
    horario INT NOT NULL, 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_PlanoHorario_plano 
		FOREIGN KEY (plano) 
		REFERENCES Plano(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
    CONSTRAINT FK_PlanoHorario_horario 
		FOREIGN KEY (horario) 
		REFERENCES Horario(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_PlanoHorario_percentualDesconto ON PlanoHorario (percentualDesconto);
CREATE INDEX CH_PlanoHorario_plano ON PlanoHorario(plano);
CREATE INDEX CH_PlanoHorario_horario ON PlanoHorario(horario);
 
---------------------------------------------------------------- 
CREATE TABLE Familiar 
(
    identificador VARCHAR(5) NOT NULL, 
    codAcesso VARCHAR(10) NOT NULL, 
    nome VARCHAR(50) NOT NULL, 
    parentesco INT NOT NULL, 
    familiar INT, 
    cliente INT NOT NULL, 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_Familiar_parentesco 
		FOREIGN KEY (parentesco) 
		REFERENCES Parentesco(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_Familiar_cliente 
		FOREIGN KEY (cliente) 
		REFERENCES Cliente(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_Familiar_identificador ON Familiar(identificador);
CREATE INDEX CH_Familiar_parentesco ON Familiar(parentesco);
CREATE INDEX CH_Familiar_cliente ON Familiar(cliente);
 
---------------------------------------------------------------- 
CREATE TABLE QuestionarioCliente 
(
    data TIMESTAMP NOT NULL, 
    consultor INT NOT NULL, 
    cliente INTEGER, 	
    questionario INT NOT NULL, 
    codigo SERIAL NOT NULL, 
    observacao text,	
    CONSTRAINT FK_QuestionarioCliente_consultor 
		FOREIGN KEY (consultor) 
		REFERENCES colaborador(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_QuestionarioCliente_cliente 
		FOREIGN KEY (cliente) 
		REFERENCES Cliente(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_QuestionarioCliente_questionario 
		FOREIGN KEY (questionario) 
		REFERENCES Questionario(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_QuestionarioCliente_data ON QuestionarioCliente(data);
CREATE INDEX CH_QuestionarioCliente_consultor ON QuestionarioCliente(consultor);
CREATE INDEX CH_QuestionarioCliente_cliente ON QuestionarioCliente(cliente);
CREATE INDEX CH_QuestionarioCliente_questionario ON QuestionarioCliente(questionario);

---------------------------------------------------------------- 
CREATE TABLE ClienteGrupo 
(
    cliente INT NOT NULL, 
    grupo INT NOT NULL, 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_ClienteGrupo_cliente 
		FOREIGN KEY (cliente) 
		REFERENCES Cliente(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
    CONSTRAINT FK_ClienteGrupo_grupo 
		FOREIGN KEY (grupo) 
		REFERENCES Grupo(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_ClienteGrupo_cliente ON ClienteGrupo(cliente);
CREATE INDEX CH_ClienteGrupo_grupo ON ClienteGrupo(grupo);
 
---------------------------------------------------------------- 
CREATE TABLE PlanoComposicao 
(	
    composicao INT NOT NULL, 
    plano INT NOT NULL, 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_PlanoComposicao_composicao 
		FOREIGN KEY (composicao) 
		REFERENCES Composicao(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_PlanoComposicao_plano 
		FOREIGN KEY (plano) 
		REFERENCES Plano(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_PlanoComposicao_composicao ON PlanoComposicao(composicao);
CREATE INDEX CH_PlanoComposicao_plano ON PlanoComposicao(plano);
 
---------------------------------------------------------------- 
CREATE TABLE MovProdutoParcela 
(	
    valorPago REAL, 
    movParcela INT NOT NULL, 
    reciboPagamento INT, 
    movProduto INT NOT NULL, 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_MovProdutoParcela_movParcela 
		FOREIGN KEY (movParcela) 
		REFERENCES MovParcela(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
    CONSTRAINT fk_movprodutoparcela_movproduto 
		FOREIGN KEY (movproduto) 
		REFERENCES movproduto(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE,
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_MovProdutoParcela_movParcela ON MovProdutoParcela(movParcela);
CREATE INDEX CH_MovProdutoParcela_movProduto ON MovProdutoParcela(movProduto);
 
---------------------------------------------------------------- 
CREATE TABLE QuestionarioPerguntaCliente 
(	
    perguntaCliente INT, 
    questionarioCliente INT NOT NULL, 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_QuestionarioPerguntaCliente_questionarioCliente 
		FOREIGN KEY (questionarioCliente) 
		REFERENCES QuestionarioCliente(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_QuestionarioPerguntaCliente_perguntaCliente ON QuestionarioPerguntaCliente(perguntaCliente);
CREATE INDEX CH_QuestionarioPerguntaCliente_questionarioCliente ON QuestionarioPerguntaCliente(questionarioCliente);
 
---------------------------------------------------------------- 
CREATE TABLE HistoricoContrato 
(	
    dataFinalSituacao TIMESTAMP, 
	tipohistorico VARCHAR(2) ,
    dataInicioSituacao TIMESTAMP, 
    situacaoRelativaHistorico VARCHAR(2) NOT NULL, 
    dataRegistro TIMESTAMP NOT NULL, 
    responsavelLiberacaoMudancaHistorico INT , 
    responsavelRegistro INT NOT NULL, 
    descricao VARCHAR(50), 
    contrato INT NOT NULL, 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_HistoricoContrato_responsavelLiberacaoMudancaHistorico 
		FOREIGN KEY (responsavelLiberacaoMudancaHistorico) 
		REFERENCES colaborador(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_HistoricoContrato_responsavelRegistro 
		FOREIGN KEY (responsavelRegistro) 
		REFERENCES Usuario(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_HistoricoContrato_contrato 
		FOREIGN KEY (contrato) 
		REFERENCES Contrato(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_HistoricoContrato_situacaoRelativaHistorico ON HistoricoContrato(situacaoRelativaHistorico);
CREATE INDEX CH_HistoricoContrato_dataRegistro ON HistoricoContrato(dataRegistro);
CREATE INDEX CH_HistoricoContrato_responsavelLiberacaoMudancaHistorico ON HistoricoContrato(responsavelLiberacaoMudancaHistorico);
CREATE INDEX CH_HistoricoContrato_responsavelRegistro ON HistoricoContrato(responsavelRegistro);
CREATE INDEX CH_HistoricoContrato_contrato ON HistoricoContrato(contrato);
 
---------------------------------------------------------------- 
CREATE TABLE PagamentoMovParcela 
(
    valorpago real,
    reciboPagamento INT, 
    movParcela INT NOT NULL, 
    movPagamento INT NOT NULL, 
    codigo SERIAL NOT NULL, 	    
    CONSTRAINT FK_PagamentoMovParcela_movPagamento 
		FOREIGN KEY (movPagamento) 
		REFERENCES MovPagamento(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 	
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_PagamentoMovParcela_movParcela ON PagamentoMovParcela(movParcela);
CREATE INDEX CH_PagamentoMovParcela_movPagamento ON PagamentoMovParcela(movPagamento);
 
---------------------------------------------------------------- 
CREATE TABLE ComposicaoModalidade 
(	
    valorMensalComposicao REAL, 
    precoModalidade REAL, 
    composicao INT NOT NULL, 
    modalidade INT NOT NULL, 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_ComposicaoModalidade_composicao 
		FOREIGN KEY (composicao) 
		REFERENCES Composicao(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
    CONSTRAINT FK_ComposicaoModalidade_modalidade 
		FOREIGN KEY (modalidade) 
		REFERENCES Modalidade(codigo) 
		ON UPDATE RESTRICT ON DELETE RESTRICT, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_ComposicaoModalidade_composicao ON ComposicaoModalidade(composicao);
CREATE INDEX CH_ComposicaoModalidade_modalidade ON ComposicaoModalidade(modalidade);

-------------------------------------------------------------------
CREATE TABLE usuarioperfilacesso
(
  empresa integer,
  perfilacesso integer NOT NULL,
  usuario integer NOT NULL,
  codigo serial NOT NULL,
  CONSTRAINT usuarioperfilacesso_pkey PRIMARY KEY (codigo),  
  CONSTRAINT fk_usuarioperfilacesso_perfilacesso 
	FOREIGN KEY (perfilacesso) 
	REFERENCES perfilacesso (codigo) MATCH SIMPLE 
	ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_usuarioperfilacesso_usuario 
	FOREIGN KEY (usuario) 
	REFERENCES usuario (codigo) MATCH SIMPLE 
	ON UPDATE CASCADE ON DELETE CASCADE
);

CREATE INDEX ch_usuarioperfilacesso_empresa ON usuarioperfilacesso USING btree (empresa);
CREATE INDEX ch_usuarioperfilacesso_perfilacesso ON usuarioperfilacesso USING btree  (perfilacesso);
CREATE INDEX ch_usuarioperfilacesso_usuario ON usuarioperfilacesso  USING btree  (usuario);

----------------------------------------------------------------
CREATE TABLE ModalidadeEmpresa 
(	
    empresa INT NOT NULL, 
    modalidade INT NOT NULL, 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_ModalidadeEmpresa_empresa 
		FOREIGN KEY (empresa) 
		REFERENCES Empresa(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_ModalidadeEmpresa_modalidade 
		FOREIGN KEY (modalidade) 
		REFERENCES Modalidade(codigo) 
		ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_ModalidadeEmpresa_empresa ON ModalidadeEmpresa(empresa);
CREATE INDEX CH_ModalidadeEmpresa_modalidade ON ModalidadeEmpresa(modalidade);

---------------------------------------------------------------- 
CREATE TABLE MatriculaAlunoHorarioTurma 
(	
    dataInicioMatricula TIMESTAMP, 
    dataFimMatricula TIMESTAMP, 
    modalidade INT NOT NULL, 
    horarioTurma INT NOT NULL, 
    contrato INT NOT NULL, 
    pessoa INT NOT NULL, 
    turma INT NOT NULL, 
    empresa INT NOT NULL, 
    codigo SERIAL NOT NULL, 	
    CONSTRAINT FK_MatriculaAlunoHorarioTurma_contrato 
		FOREIGN KEY (contrato) 
		REFERENCES Contrato(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_MatriculaAlunoHorarioTurma_pessoa 
		FOREIGN KEY (pessoa) 
		REFERENCES Pessoa(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_MatriculaAlunoHorarioTurma_turma 
		FOREIGN KEY (turma) 
		REFERENCES Turma(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
    CONSTRAINT FK_MatriculaAlunoHorarioTurma_empresa 
		FOREIGN KEY (empresa) 
		REFERENCES Empresa(codigo) 
		ON DELETE RESTRICT ON UPDATE RESTRICT, 
	CONSTRAINT fk_matriculaalunohorarioturma_modalidade 
		FOREIGN KEY (modalidade) 
		REFERENCES modalidade (codigo) MATCH SIMPLE  
		ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT fk_matriculaalunohorarioturma_horarioturma 
		FOREIGN KEY (horarioturma) 
		REFERENCES horarioturma (codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT ON DELETE RESTRICT,
    PRIMARY KEY (codigo)
);
CREATE INDEX CH_MatriculaAlunoHorarioTurma_modalidade ON MatriculaAlunoHorarioTurma(modalidade);
CREATE INDEX CH_MatriculaAlunoHorarioTurma_horarioTurma ON MatriculaAlunoHorarioTurma(horarioTurma);
CREATE INDEX CH_MatriculaAlunoHorarioTurma_contrato ON MatriculaAlunoHorarioTurma(contrato);
CREATE INDEX CH_MatriculaAlunoHorarioTurma_pessoa ON MatriculaAlunoHorarioTurma(pessoa);
CREATE INDEX CH_MatriculaAlunoHorarioTurma_turma ON MatriculaAlunoHorarioTurma(turma);

-------------------------------------------------------------------
CREATE TABLE log
(
  nomeentidade character varying(150),
  nomeentidadedescricao character varying(150),
  chaveprimaria character varying(30),
  chaveprimariaentidadesubordinada character varying(30),
  nomecampo character varying(100),
  valorcampoanterior text,
  valorcampoalterado text,
  dataalteracao timestamp without time zone,
  responsavelalteracao character varying(150),
  operacao character varying(50), 
  codigo serial NOT NULL,
  CONSTRAINT log_pkey PRIMARY KEY (codigo)
);

------------------------------------------------------------
CREATE TABLE contratoduracao
(
  valordesejadomensal real,
  valordesejadoparcela real,
  valordesejado real,
  tipooperacao character varying(2),
  tipovalor character varying(2),
  valorespecifico real,
  percentualdesconto real,
  nrmaximoparcelascondpagamento integer NOT NULL,
  contrato integer NOT NULL,
  numeromeses integer NOT NULL,
  carencia integer,
  codigo serial NOT NULL,
  CONSTRAINT contratoduracao_pkey PRIMARY KEY (codigo),
  CONSTRAINT fk_contratoduracao_contrato FOREIGN KEY (contrato)
      REFERENCES contrato (codigo) MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE
);

------------------------------------------------------------
CREATE TABLE contratohorario
(
  tipooperacao character varying(2),
  tipovalor character varying(2),
  valorespecifico real,
  percentualdesconto real,
  contrato integer NOT NULL,
  horario integer NOT NULL,
  codigo serial NOT NULL,
  CONSTRAINT contratohorario_pkey PRIMARY KEY (codigo),
  CONSTRAINT fk_contratohorario_horario 
	FOREIGN KEY (horario)
      REFERENCES horario (codigo) MATCH SIMPLE 
	  ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_contratohorario_contrato 
	FOREIGN KEY (contrato)
      REFERENCES contrato (codigo) MATCH SIMPLE 
	  ON UPDATE CASCADE ON DELETE CASCADE
);

------------------------------------------------------------
CREATE TABLE contratocondicaopagamento
(
  percentualdesconto real,
  condicaopagamento integer NOT NULL,
  codigo serial NOT NULL,
  tipooperacao character varying(2),
  tipovalor character varying(2),
  valorespecifico real,
  contrato integer,
  CONSTRAINT contratocondicaopagamento_pkey PRIMARY KEY (codigo),
  CONSTRAINT fk_contratocondicaopagamento_condicaopagamento 
	FOREIGN KEY (condicaopagamento)
    REFERENCES condicaopagamento (codigo) MATCH SIMPLE 
	ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_contratocondicaopagamento_contrato 
	FOREIGN KEY (contrato)
    REFERENCES contrato (codigo) MATCH SIMPLE 
	ON UPDATE CASCADE ON DELETE CASCADE
);

------------------------------------------------------------
CREATE TABLE contratocomposicao
(
  composicao integer NOT NULL,
  contrato integer NOT NULL,
  codigo serial NOT NULL,
  CONSTRAINT contratocomposicao_pkey PRIMARY KEY (codigo),
  CONSTRAINT fk_contratocomposicao_composicao 
	FOREIGN KEY (composicao)
    REFERENCES composicao (codigo) MATCH SIMPLE 
	ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_contratocomposicao_contrato 
	FOREIGN KEY (contrato)
    REFERENCES contrato (codigo) MATCH SIMPLE  
	ON UPDATE CASCADE ON DELETE CASCADE
);

-------------------------------------------------------------------
CREATE TABLE periodoacessocliente
(
  tipoacesso character varying(10),
  datafinalacesso timestamp without time zone,
  datainicioacesso timestamp without time zone,
  aulaAvulsaDiaria integer,
  contrato integer,
  contratobaseadorenovacao INT,
  pessoa integer,
  codigo serial NOT NULL,
  CONSTRAINT fk_periodoacessocliente_contrato 
	FOREIGN KEY (contrato)  
	REFERENCES contrato (codigo) MATCH SIMPLE  
	ON UPDATE CASCADE ON DELETE CASCADE,
  CONSTRAINT fk_periodoacessocliente_aulaAvulsaDiaria 
	FOREIGN KEY (aulaAvulsaDiaria) 
	REFERENCES aulaAvulsaDiaria (codigo) MATCH SIMPLE 
	ON UPDATE CASCADE ON DELETE CASCADE,
  CONSTRAINT fk_periodoacessocliente_contratobaseadorenovacao 
	FOREIGN KEY (contratobaseadorenovacao) 
	REFERENCES contrato(codigo) MATCH SIMPLE 
	ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_periodoacessocliente_pessoa 
	FOREIGN KEY (pessoa)  
	REFERENCES pessoa(codigo) MATCH SIMPLE  
	ON UPDATE RESTRICT ON DELETE CASCADE,
  CONSTRAINT periodoacessocliente_pkey PRIMARY KEY (codigo)
);

-------------------------------------------------------------------
CREATE TABLE justificativaoperacao
(
  empresa integer NOT NULL,
  tipooperacao character(2) NOT NULL,
  descricao character(50) NOT NULL,
  codigo serial NOT NULL,
  CONSTRAINT justificativaoperacao_pkey PRIMARY KEY (codigo),
  CONSTRAINT fk_justificativaoperacao_empresa 
	FOREIGN KEY (empresa)
    REFERENCES empresa (codigo) MATCH SIMPLE
    ON UPDATE RESTRICT ON DELETE RESTRICT
);

-------------------------------------------------------------------
CREATE TABLE contratooperacao
(
  tipojustificativa integer,
  responsavelliberacao integer,
  clientetransferedias integer,
  clienterecebedias integer,
  descricaocalculo text,
  observacao text,
  responsavel integer,
  datafimefetivacaooperacao timestamp without time zone,
  datainicioefetivacaooperacao timestamp without time zone,
  dataoperacao timestamp without time zone,
  operacaopaga boolean,
  tipooperacao character varying(2),
  contrato integer,
  codigo serial NOT NULL,
  CONSTRAINT fk_contratooperacao_contrato 
	FOREIGN KEY (contrato) 
	REFERENCES contrato (codigo) MATCH SIMPLE 
	ON UPDATE CASCADE ON DELETE CASCADE,
  CONSTRAINT fk_contratooperacao_responsavelliberacao 
	FOREIGN KEY (responsavelliberacao) 
	REFERENCES usuario (codigo) MATCH SIMPLE  
	ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_contratooperacao_clientetransferedias 
	FOREIGN KEY (clientetransferedias)   
	REFERENCES cliente (codigo) MATCH SIMPLE   
	ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_contratooperacao_clienterecebedias 
	FOREIGN KEY (clienterecebedias) 
	REFERENCES cliente (codigo) MATCH SIMPLE 
	ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_contratooperacao_responsavel 
	FOREIGN KEY (responsavel) 
	REFERENCES usuario (codigo) MATCH SIMPLE  
	ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT contratooperacao_pkey PRIMARY KEY (codigo)
);

--------------------------------------------------------------------------
CREATE TABLE trancamentocontrato
(
  contrato INT, 
  valorCongelado REAL, 
  nrDiasCongelado INT, 
  dataTrancamento timestamp without time zone,
  datafimtrancamento timestamp without time zone,
  dataRetorno timestamp without time zone,
  valorTrancamento REAL, 
  tipoJustificativa INT, 
  observacao text, 
  responsavelOperacao INT, 
  produtoTrancamento INT,  
  codigo serial NOT NULL,
  CONSTRAINT fk_trancamentocontrato_produtoTrancamento 
	FOREIGN KEY (produtoTrancamento)
    REFERENCES produto (codigo) MATCH SIMPLE 
	ON UPDATE RESTRICT ON DELETE RESTRICT,     
  CONSTRAINT fk_trancamentocontrato_responsavelOperacao 
	FOREIGN KEY (responsavelOperacao)
    REFERENCES usuario (codigo) MATCH SIMPLE 
	ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_trancamentocontrato_tipoJustificativa 
	FOREIGN KEY (tipoJustificativa)
    REFERENCES justificativaoperacao (codigo) MATCH SIMPLE 
	ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_trancamentocontrato_contrato 
	FOREIGN KEY (contrato)
    REFERENCES contrato (codigo) MATCH SIMPLE 
	ON UPDATE RESTRICT ON DELETE CASCADE,
  CONSTRAINT tracamentocontrato_pkey PRIMARY KEY (codigo)
);

--------------------------------------------------------------------------
CREATE TABLE logcontroleusabilidade 
(
    codigo serial NOT NULL, 
    empresa INT, 
    usuario INT, 
    dataRegistro TIMESTAMP, 
    acao VARCHAR(20), 
    maquina VARCHAR(80) NOT NULL, 
    entidade VARCHAR(50) NOT NULL,
    chaveprimaria INT NOT NULL,  
	CONSTRAINT logcontroleusabilidade_pkey PRIMARY KEY (codigo),
    CONSTRAINT fk_logcontroleusabilidade_empresa 
		FOREIGN KEY (empresa) 
		REFERENCES empresa (codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT fk_logcontroleusabilidade_usuario 
		FOREIGN KEY (usuario) 
		REFERENCES usuario (codigo) MATCH SIMPLE 
		ON UPDATE RESTRICT ON DELETE RESTRICT
);

-----------------------------------------------------------------------------------------------------------
CREATE TABLE situacaocontratoanaliticodw
(
  codigo serial NOT NULL,
  dia timestamp without time zone,
  situacao character varying(2), 
  empresa integer,
  plano integer,
  cliente integer,
  contrato integer,  
  fonecliente character varying(40), 
  emailcliente character varying(50),
  modalidadecliente character varying(40),
  enderecocliente character varying(100),
  CONSTRAINT situacaocontratoanaliticodw_pkey PRIMARY KEY (codigo), 
  CONSTRAINT fk_situacaocontratoanaliticodw_plano 
	FOREIGN KEY (plano) 
	REFERENCES plano (codigo) MATCH SIMPLE 
	ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_situacaocontratoanaliticodw_cliente 
	FOREIGN KEY (cliente) 
	REFERENCES cliente (codigo) MATCH SIMPLE 
	ON UPDATE RESTRICT ON DELETE RESTRICT,  
  CONSTRAINT fk_situacaocontratoanaliticodw_empresa 
	FOREIGN KEY (empresa) 
	REFERENCES empresa (codigo) MATCH SIMPLE 
	ON UPDATE RESTRICT ON DELETE RESTRICT        
);

-----------------------------------------------------------------------------------------------------------
CREATE TABLE situacaocontratosinteticodw
(
  codigo serial NOT NULL,
  dia timestamp without time zone,
  situacao character varying(2),
  empresa integer,
  peso integer,
  vinculocarteira text,
  plano text,
  CONSTRAINT situacaocontratosinteticodw_pkey PRIMARY KEY (codigo)   
);
----------------------------------------------------------------------------------------------------------------------------------------------------
CREATE TABLE robo
(  
  dia timestamp without time zone,
  dataHoraInicio timestamp without time zone,
  dataHoraFim timestamp without time zone,
  texto text,
  rotinaProcessada Boolean,
  codigo serial NOT NULL,
  CONSTRAINT robo_pkey PRIMARY KEY (codigo)
);
----------------------------------------------------------------------------------------------------------------------------------------------------
CREATE TABLE RotatividadeSinteticoDW 
(
    vencido integer,
	dia timestamp without time zone,
    qtdvigentesmesanterior integer,
    situacao VARCHAR(40), 
    peso INT, 
    ano integer,	
    empresa INT, 
    mes INT,    
    codigo SERIAL NOT NULL, 	
    PRIMARY KEY (codigo)
);

---------------------------------------------------------------------------------------------------------------------------------------------------
CREATE TABLE rotatividadeanaliticodw
(
  situacao character varying(40),
  peso integer,
  contrato integer,
  cliente integer,
  empresa integer,
  mes integer,
  ano integer,
  dia timestamp without time zone,
  codigo serial NOT NULL,
  fonecliente character varying(100),
  CONSTRAINT rotatividadeanaliticodw_pkey PRIMARY KEY (codigo)
);

------------------------------------------------------------------------------------------------------------------------------------
CREATE TABLE clientemensagem
(
  cadastroincompleto boolean,
  movparcela integer,
  questionariocliente integer,
  usuario integer,
  tipomensagem character varying(2),
  cliente integer,
  mensagem text,
  codigo serial NOT NULL,
  bloqueio boolean,
  databloqueio date,
  pesoRisco integer,
  CONSTRAINT clientemensagem_pkey PRIMARY KEY (codigo),
  CONSTRAINT fk_clientemensagem_cliente 
	FOREIGN KEY (cliente) 
	REFERENCES cliente (codigo) MATCH SIMPLE 
	ON UPDATE CASCADE ON DELETE CASCADE,
  CONSTRAINT fk_clientemensagem_movparcela 
	FOREIGN KEY (movparcela) 
	REFERENCES movparcela (codigo) MATCH SIMPLE 
	ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT fk_clientemensagem_questionariocliente 
	FOREIGN KEY (questionariocliente) 
	REFERENCES questionariocliente (codigo) MATCH SIMPLE 
	ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_clientemensagem_usuario 
	FOREIGN KEY (usuario)  
	REFERENCES usuario (codigo) MATCH SIMPLE  
	ON UPDATE RESTRICT ON DELETE RESTRICT
);

---------------------------------------------------------------------------------------------------------------------------------]
CREATE TABLE risco
(
  codigo serial NOT NULL,
  dia timestamp without time zone,
  cliente integer,
  colaborador integer,
  empresa integer,
  peso integer,
  nomecliente character varying(200),
  matriculacliente character varying(100),
  fonecliente character varying(100),
  CONSTRAINT pk_codigo PRIMARY KEY (codigo),
  CONSTRAINT fk_risco_cliente FOREIGN KEY (cliente)
      REFERENCES cliente (codigo) MATCH SIMPLE 
	  ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_risco_colaborador FOREIGN KEY (colaborador)
      REFERENCES colaborador (codigo) MATCH SIMPLE 
	  ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_risco_empresa FOREIGN KEY (empresa)
      REFERENCES empresa (codigo) MATCH SIMPLE 
	  ON UPDATE RESTRICT ON DELETE RESTRICT
);

------------------------------------------------------------------------------------------------------------------------------------
CREATE TABLE contratotextopadrao
(
  planotextopadrao integer NOT NULL,
  contrato integer NOT NULL,
  codigo serial NOT NULL,
  CONSTRAINT contratotextopadrao_pkey PRIMARY KEY (codigo),
  CONSTRAINT fk_contratotextopadrao_planotextopadrao 
	FOREIGN KEY (planotextopadrao)
    REFERENCES planotextopadrao (codigo) MATCH SIMPLE 
	ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_contratotextopadrao_contrato 
	FOREIGN KEY (contrato)
    REFERENCES contrato (codigo) MATCH SIMPLE 
	ON UPDATE CASCADE ON DELETE CASCADE
)
;

----------------------------------------------------------------------------------------------------------------------------------------
CREATE TABLE localacesso
(
  descricao character varying(40) NOT NULL,
  codigo serial NOT NULL,
  nomecomputador character varying(40) NOT NULL,
  empresa integer,
  tempoentreacessos integer,
  servidorimpressoes character varying(50),
  portaservidorimp smallint,
  CONSTRAINT localacesso_pkey PRIMARY KEY (codigo),
  CONSTRAINT fk_localacesso_empresa 
	FOREIGN KEY (empresa)
    REFERENCES empresa (codigo) MATCH SIMPLE
    ON UPDATE RESTRICT ON DELETE RESTRICT
);
CREATE INDEX ch_localacesso_descricao   ON localacesso  USING btree  (descricao);

----------------------------------------------------------------------------------------------------------------------------------------------------
CREATE TABLE coletor
(
  codigo serial NOT NULL,
  descricao character varying(40) NOT NULL,
  modelo character varying(70) NOT NULL,
  numserie character varying(40),
  portacomunicacao character varying(70),
  modotransmissao character varying(70),
  veloctransmissao integer,
  resolucaodpi integer,
  releentrada integer,
  temporeleentrada integer,
  relesaida integer,
  temporelesaida integer,
  msgdisplay character varying(30),
  sentidoacesso character varying(70),
  aguardagiro boolean,
  arquivoprograma bytea,
  padraocadastro boolean,
  localacesso integer,
  sensorentrada integer,
  sensorsaida integer,
  numeroterminal integer,
  CONSTRAINT coletor_pkey PRIMARY KEY (codigo),
  CONSTRAINT fk_localacesso_coletor 
	FOREIGN KEY (localacesso)
    REFERENCES localacesso (codigo) MATCH SIMPLE 
	ON UPDATE RESTRICT ON DELETE RESTRICT
);
CREATE INDEX ch_coletor_descricao   ON coletor USING btree  (descricao);

-----------------------------------------------------------------------------------------------------------------------------------------
CREATE TABLE acessocliente
(
  codigo serial NOT NULL,
  cliente integer NOT NULL,
  sentido character varying(1) NOT NULL,
  situacao character varying(50),
  localacesso integer NOT NULL,
  coletor integer NOT NULL,
  usuario integer,
  dthrentrada timestamp without time zone NOT NULL,
  dthrsaida timestamp without time zone,
  CONSTRAINT "codigoPK" PRIMARY KEY (codigo),
  CONSTRAINT "clienteFK" FOREIGN KEY (cliente)
      REFERENCES cliente (codigo) MATCH SIMPLE 
	  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "coletorFK" FOREIGN KEY (coletor)
      REFERENCES coletor (codigo) MATCH SIMPLE 
	  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "localacessoFK" FOREIGN KEY (localacesso)
      REFERENCES localacesso (codigo) MATCH SIMPLE 
	  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "usuarioFK" FOREIGN KEY (usuario)
      REFERENCES usuario (codigo) MATCH SIMPLE 
	  ON UPDATE NO ACTION ON DELETE NO ACTION
);


-----------------------------------------------------------------------------------------------------------------------------------------
CREATE TABLE acessocolaborador
(
  codigo serial NOT NULL,
  colaborador integer,
  sentido character varying(1),
  localacesso integer,
  coletor integer,
  dthrentrada timestamp without time zone NOT NULL,
  dthrsaida timestamp without time zone,
  CONSTRAINT "codigo_PK" PRIMARY KEY (codigo),
  CONSTRAINT "colaborador_FK" FOREIGN KEY (colaborador)
      REFERENCES colaborador (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "localacesso_FK" FOREIGN KEY (localacesso)
      REFERENCES localacesso (codigo) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

-------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE TABLE contratomodalidadevezessemana
(
  nrvezes integer NOT NULL,
  tipooperacao character varying(2),
  tipovalor character varying(2),
  valorespecifico real,
  percentualdesconto real,
  contratomodalidade integer,
  codigo serial NOT NULL,
  
  CONSTRAINT fk_contratomodalidadevezessemana_contratomodalidade 
	FOREIGN KEY (contratomodalidade) REFERENCES contratomodalidade (codigo) MATCH SIMPLE 
	ON UPDATE CASCADE ON DELETE CASCADE,
  CONSTRAINT contratomodalidadevezessemana_pkey PRIMARY KEY (codigo)
);

-----------------------------------------------------------------------------------------------------------------
CREATE TABLE HistoricoVinculo(
	codigo SERIAL NOT NULL,	 
    dataRegistro TIMESTAMP NOT NULL,
    tipoHistoricoVinculo VARCHAR(2)  NOT NULL,
    tipoColaborador VARCHAR(2)  NOT NULL,
    cliente INT NOT NULL,
    colaborador INT NOT NULL,	
	CONSTRAINT FK_HistoricoVinculo_cliente 
		FOREIGN KEY (cliente) 
		REFERENCES cliente(codigo) 
		ON UPDATE RESTRICT ON DELETE CASCADE,
	CONSTRAINT FK_HistoricoVinculo_colaborador 
		FOREIGN KEY (colaborador) 
		REFERENCES colaborador(codigo) 
		ON UPDATE RESTRICT ON DELETE RESTRICT,
	PRIMARY KEY(codigo)
);

----------------------------------------------------------------------------------------------------------------
CREATE TABLE numeroMatricula(
	matricula Integer NOT NULL,	
	PRIMARY KEY(matricula)
);

----------------------------------------------------------------------------------------------------------------
create table SituacaoClienteSinteticoDW
(
	codigo 			serial NOT NULL,
	dia			timestamp without time zone,
	codigoCliente 		integer,
	matricula 		integer,
	nomeCliente 		character varying(50),
	dataNascimento 		timestamp without time zone,
	idade 			integer,
	profissao 		character varying(50),
	colaboradores 		character varying(255),
	codigoContrato 		integer,
	situacao 		character varying(20),
	duracaoContratoMeses 	integer,
	mnemonicoContrato 	character varying(20),
	nomePlano 		character varying(50),
	valorFaturadoContrato 	real,
	valorPagoContrato 	real,
	valorParcAbertoContrato real,
	saldoContaCorrenteCliente real,
	dataVigenciaDe 		timestamp without time zone,
	dataVigenciaAte 	timestamp without time zone,
	dataVigenciaAteAjustada timestamp without time zone,
	dataLancamentoContrato 	timestamp without time zone,
	dataRenovacaoContrato 	timestamp without time zone,
	dataRematriculaContrato timestamp without time zone,
	dataUltimoBV 		timestamp without time zone,
	dataMatricula 		timestamp without time zone,
	dataUltimaRematricula 	timestamp without time zone,
	diasAssiduidadeUltRematriculaAteHoje integer,
	diasAcessoSemanaPassada integer,
	diasFaltaSemAcesso 	integer,
	dataUltimoAcesso 	timestamp without time zone,
	faseAtualCRM 		character varying(50),
	dataUltimoContatoCRM 	timestamp without time zone,
	responsavelUltimoContatoCRM character varying(50),
	codigoUltimoContatoCRM integer,
	situacaoContrato character varying(20),
	tipoPeriodoAcesso character varying(20),
	dataInicioPeriodoAcesso timestamp without time zone,
	dataFimPeriodoAcesso timestamp without time zone,
	diasAcessoSemana2 integer,
	diasAcessoSemana3 integer,
	diasAcessoSemana4 integer,
CONSTRAINT situacaoclientesinteticodw_pkey PRIMARY KEY (codigo)
);

-----------------------------------------------------------------------------------------------------------------------------------------
CREATE TABLE configuracaosistemacadastrocliente
(
  nome character varying(50) NOT NULL,
  obrigatorio boolean,
  mostrar boolean,
  pendente boolean,
  CONSTRAINT configuracaosistemacliente_pkey PRIMARY KEY (nome)
);
-----------------------------------------------------------------------------------------------------------------------------------------
CREATE TABLE metacrescimento
(
  codigo serial NOT NULL,
  empresa integer NOT NULL,
  ano integer,
  mes integer,
  totalinicial integer NOT NULL DEFAULT 0,
  metacrescimento integer NOT NULL DEFAULT 0,
  totalfinal integer NOT NULL DEFAULT 0,
  qtdeprevistosrenovar integer NOT NULL DEFAULT 0,
  iranterior real NOT NULL DEFAULT 0,
  irinformado real NOT NULL DEFAULT 0,
  totalrenovacoes integer NOT NULL DEFAULT 0,
  totalrenovacoesatrasadas integer NOT NULL DEFAULT 0,
  icvanterior real NOT NULL DEFAULT 0,
  icvinformado real NOT NULL DEFAULT 0,
  totalvendas integer NOT NULL DEFAULT 0,
  totalvisitas integer NOT NULL DEFAULT 0,
  icanceladosanterior real NOT NULL DEFAULT 0,
  icanceladosinformado real NOT NULL DEFAULT 0,
  totalcancelados integer NOT NULL DEFAULT 0,
  itrancadosanterior real NOT NULL DEFAULT 0,
  itrancadosinformado real NOT NULL DEFAULT 0,
  totaltrancados integer NOT NULL DEFAULT 0,
  dataedicao timestamp without time zone NOT NULL,
  CONSTRAINT metacrescimento_pkey PRIMARY KEY (codigo),
  CONSTRAINT fk_metacrescimento_empresa 
	FOREIGN KEY (empresa)
    REFERENCES empresa (codigo) MATCH SIMPLE
    ON UPDATE CASCADE ON DELETE CASCADE
);
-----------------------------------------------------------------------------------------------------------------------------------------
CREATE TABLE bdversaoatual
(
	codigo serial NOT NULL,
	versao integer NOT NULL,
	CONSTRAINT bdversaoatual_pkey PRIMARY KEY (codigo)
);
-----------------------------------------------------------------------------------------------------------------------------------------
CREATE TABLE bdatualizacao
(
	codigo serial NOT NULL,
	versao integer NOT NULL,
	script text,
	descricao text,
	resultado integer NOT NULL,
	mensagem text NOT NULL,
	stacktrace text,
	data timestamp without time zone NOT NULL,
	usuario integer NOT NULL,
	CONSTRAINT bdatualizacao_pkey PRIMARY KEY (codigo),
	CONSTRAINT fk_bdatualizacao_usuario 
		FOREIGN KEY (usuario)
		REFERENCES usuario (codigo) MATCH SIMPLE
		ON UPDATE RESTRICT ON DELETE RESTRICT
);

CREATE INDEX ch_bdatualizacao_usuario
	ON bdatualizacao
	USING btree
	(usuario);
-----------------------------------------------------------------------------------------------------------------------------------------	
INSERT INTO bdversaoatual (versao) VALUES (0); 
-----------------------------------------------------------------------------------------------------------------------------------------
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('Bairro', false, true, false);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('Categoria', false, false, false);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('Cep', false, true, false);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('Cidade', false, true, true);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('CPF', false, true, true);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('Data Nascimento', true, true, false);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('Email', false, true, true);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('Endereço', true, true, false);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('Endereço Complemento', false, true, false);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('Estado', false, true, false);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('Estado Civil', false, true, false);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('Grau de Instrução', false, true, false);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('Matrícula', false, true, true);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('Nome', true, true, false);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('Nome Mãe ou Responsável', false, false, false);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('Nome Pai', false, false, false);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('Número', false, true, false);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('País', false, true, false);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('Profissão', false, true, false);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('RG', false, true, false);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('Sexo', false, true, true);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('Telefone', true, true, false);
INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente) VALUES ('Web Page', false, false, false);

----------------------------------------------------------------------------------------------------------------
ALTER TABLE empresa ADD COLUMN permiteacessoalunosoutraempresa boolean;
ALTER TABLE empresa ALTER COLUMN permiteacessoalunosoutraempresa SET STORAGE PLAIN;
-----------------------------------------------------------------------------------------------------------------------------------------
ALTER TABLE empresa ADD COLUMN somadv integer;
ALTER TABLE empresa ALTER COLUMN somadv SET STORAGE PLAIN;
-----------------------------------------------------------------------------------------------------------------------------------------
ALTER TABLE cliente ADD COLUMN uadata timestamp without time zone;
ALTER TABLE cliente ALTER COLUMN uadata SET STORAGE PLAIN;
-----------------------------------------------------------------------------------------------------------------------------------------
ALTER TABLE cliente ADD COLUMN uasituacao character varying(50);
ALTER TABLE cliente ALTER COLUMN uasituacao SET STORAGE EXTENDED;
-----------------------------------------------------------------------------------------------------------------------------------------
ALTER TABLE cliente ADD COLUMN uasentido character varying(1);
ALTER TABLE cliente ALTER COLUMN uasentido SET STORAGE EXTENDED;
-----------------------------------------------------------------------------------------------------------------------------------------
ALTER TABLE colaborador ADD COLUMN uadata timestamp without time zone;
ALTER TABLE colaborador ALTER COLUMN uadata SET STORAGE PLAIN;
-----------------------------------------------------------------------------------------------------------------------------------------
ALTER TABLE colaborador ADD COLUMN uasentido character varying(1);  
ALTER TABLE colaborador ALTER COLUMN uasentido SET STORAGE EXTENDED;
-----------------------------------------------------------------------------------------------------------------------------------------
alter table movpagamento add column recibopagamento int;
alter table movpagamento add constraint fk_movpagamento_recibopagamento 
	FOREIGN KEY (reciboPagamento) 
	REFERENCES reciboPagamento (codigo) MATCH SIMPLE 
	ON UPDATE RESTRICT ON DELETE CASCADE;
-----------------------------------------------------------------------------------------------------------------------------------------
alter table movimentocontacorrentecliente add column contratooperacao INT;
alter table movimentocontacorrentecliente add CONSTRAINT fk_movimentocontacorrentecliente_contratooperacao 
	FOREIGN KEY (contratooperacao) 
	REFERENCES contratooperacao (codigo) MATCH SIMPLE     
	ON UPDATE RESTRICT ON DELETE RESTRICT;

---------------Usuario necessario para logar no sistema antes do processo de inclusao de dados------------------------------------
INSERT INTO usuario (administrador, cliente, colaborador, tipousuario, senha, username, nome) VALUES (true, NULL, NULL, '', '1ca3bff63ed1ac7e7b3b7d68b71ae7494c77ddcfd630c531a3dcd91e744e7f60', 'admin', 'Administrador');

------------------------Questionarios-------------------------------------------------------
INSERT INTO questionario (descricao, codigo) VALUES ('BV MATRICULA', 1);
INSERT INTO questionario (descricao, codigo) VALUES ('BV REMATRICULA', 2);
INSERT INTO questionario (descricao, codigo) VALUES ('BV RETORNO', 3);
SELECT setval('questionario_codigo_seq', 3, true);

---------------------------------ConfiguracaoSistema - Por favor nao retirar esse insert pois e essencial para a criação do banco inicial--------------------------------------
INSERT INTO configuracaosistema (nrdiasavencer, qtdfaltapeso1, qtdfaltainiciopeso2, qtdfaltaterminopeso2, qtdfaltapeso3, carenciarenovacao, mascaramatricula, multa, juroparcela, questionariorematricula, questionarioretorno, questionarioprimeiravisita, nrdiasvigentequestionariovisita, nrdiasvigentequestionarioretorno, nrdiasvigentequestionariorematricula, toleranciapagamento, emailcontapagdigital, tokencontapagdigital, tokencontasms, codigo, carencia, nrdiasprorata, cpfvalidar) VALUES (15, 1, 2, 3, 4, 10, 'xxxxxx', 0, 0, 2, 3, 1, 30, 30, 30, 0, NULL, NULL, NULL, 1, 7, 20, true);

ALTER TABLE configuracaosistema ADD COLUMN rodarSqlsBancoInicial boolean DEFAULT true;