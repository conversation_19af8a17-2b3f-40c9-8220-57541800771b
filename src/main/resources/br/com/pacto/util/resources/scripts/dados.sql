----------------<PERSON><PERSON> pad<PERSON>------------------------
INSERT INTO perfilacesso (nome, codigo) VALUES ('ADMINISTRADOR', 1);
INSERT INTO perfilacesso (nome, codigo) VALUES ('CONSULTOR', 2);
INSERT INTO perfilacesso (nome, codigo) VALUES ('GERENTE', 3);
INSERT INTO perfilacesso (nome, codigo) VALUES ('PROFESSOR', 4);

-----------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '1.2 - Perfil de Acesso', '(0)', 'PerfilAcesso', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '1.3 - Usuário', '(0)', 'Usuario', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.1 - Categoria', '(0)', 'Categoria', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.11 - Endereço', '(1)(9)', 'Endereco', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.12 - Familiar', '(1)(9)', 'Familiar', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.13 - Grau de Instrução', '(0)', 'GrauInstrucao', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.14 - Grupo', '(0)', 'Grupo', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.15 - Log Controle de Usabilidade - Autorizar', '(0)(1)(2)(3)(9)(12)', 'LogControleUsabilidade', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.16 - Movimento de Conta Corrente do Cliente', '(0)', 'MovimentoContaCorrenteCliente', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.17 - País', '(0)', 'Pais', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.18 - Parentesco', '(0)', 'Parentesco', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.19 - Pergunta', '(0)', 'Pergunta', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.2 - Cidade', '(1)(9)', 'Cidade', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.20 - Pergunta Cliente', '(0)', 'PerguntaCliente', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.21 - Pessoa', '(1)(9)', 'Pessoa', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.22 - Profissão', '(1)(9)', 'Profissao', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.23 - Questionário', '(0)', 'Questionario', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.24 -Questionário Cliente', '(0)', 'QuestionarioCliente', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.25 - Questionário Pergunta', '(0)', 'QuestionarioPergunta', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.26 - Resposta da Pergunta', '(1)(9)', 'RespostaPergunta', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.27 - Resposta da Pergunta do Cliente', '(1)(9)', 'RespostaPerguntaCliente', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.28 - Telefone', '(1)(9)', 'Telefone', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.3 - Classificação', '(0)', 'Classificacao', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.4 - Cliente', '(0)(1)(2)(3)(9)(12)', 'Cliente', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.5 - Cliente Classificação', '(1)(9)', 'ClienteClassificacao', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.6 - Cliente Grupo', '(1)(9)', 'ClienteGrupo', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.7 - Colaborador', '(0)', 'Colaborador', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.8 - Configuração do Sistema', '(0)', 'ConfiguracaoSistema', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.9 - Email', '(1)(9)', 'Email', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '3.1 - Contrato', '(0)(1)(2)(3)(9)(12)', 'Contrato', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '3.2 - Convênio', '(0)', 'Convenio', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '3.3 - Convênio de Desconto', '(1)(9)', 'ConvenioDesconto', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '3.4 - Justificativa de Operação', '(0)', 'JustificativaOperacao', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '3.5 - Movimentação do Produto', '(0)', 'MovProduto', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.1 - Aula Avulsa', '(0)', 'AulaAvulsaDiaria', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.11 - Venda Avulsa', '(0)', 'VendaAvulsa', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.13 - Estorno de Recibo', '(0)(1)(2)(3)(9)(12)', 'EstornoRecibo', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.15 - Estorno de Movimentação Produto', '(0)(1)(2)(3)(9)(12)', 'EstornoMovProduto', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.16 - Operadora de Cartão', '(0)', 'OperadoraCartao', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.2 - Banco', '(0)', 'Banco', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.6 - Forma de Pagamento', '(0)', 'FormaPagamento', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.7 - Movimento de Pagamento', '(0)', 'MovPagamento', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.8 - Movimento da Parcela', '(0)', 'MovParcela', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.2 - Categoria Produto', '(0)', 'CategoriaProduto', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.1 - Caixa Por Operador', '(0)(1)(2)(3)(9)(12)', 'CaixaPorOperadorRel', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.11 - Relatório Índice de Renovação', '(0)', 'IndiceRenovacao', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.12 - Relatório Índice de Conversão', '(0)', 'IndiceConversao', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.14 - Relatório Business Intelligence', '(0)', 'Business', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.15 - Relatório Pendência Cliente', '(0)(1)(2)(3)(9)(12)', 'PendenciaCliente', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.3 - Lista Chamada', '(0)(1)(2)(3)(9)(12)', 'ListaChamada', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.7 - Parcela Em Aberto', '(0)(1)(2)(3)(9)(12)', 'ParcelaEmAbertoRel', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.8 - Saldo Conta Corrente de Cliente', '(0)(1)(2)(3)(9)(12)', 'SaldoContaCorrenteRel', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.9 - Totalizadores de Frenquência', '(0)(1)(2)(3)(9)(12)', 'TotalizadorFrequenciaRel', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.1 - Módulo CRM', '(0)(1)(2)(3)(9)(12)', 'ModuloCRM', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.10 - Mensagem Notificação', '(0)', 'MensagemNotificacao', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.11 - Organizar Carteira', '(0)', 'OrganizadorCarteira', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.12 - Cliente Potencial', '(0)(1)(2)(3)(9)(12)', 'Passivo', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.13 - Indicação', '(0)(1)(2)(3)(9)(12)', 'Indicacao', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.14 - Feriado', '(0)', 'Feriado', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.15 - Agenda', '(0)(1)(2)(3)(9)(12)', 'Agenda', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.16 - Evento', '(0)', 'Evento', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.17 - Indicador de Vendas', '(0)', 'IndicadorVenda', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.18 - Indicador de Retenção', '(0)', 'IndicadorRetencao', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.19 - Visualizar Meta', '(0)(1)(2)(3)(9)(12)', 'VisualizarMeta', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.2 - Definir Layout', '(0)(1)(2)(3)(9)(12)', 'DefinirLayout', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.20 - Histórico Contato', '(0)', 'HistoricoContato', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.21 - Realizar Contato', '(0)(1)(2)(3)(9)(12)', 'RealizarContato', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.22 - Meta Agendamento', '(0)(1)(2)(3)(9)(12)', 'MetaAgendamento', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.23 - Meta Vinte Quatro Horas', '(0)(1)(2)(3)(9)(12)', 'MetaVinteQuatroHoras', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.24 - Meta Renovação', '(0)(1)(2)(3)(9)(12)', 'MetaRenovacao', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.25 - Meta Pós Venda', '(0)(1)(2)(3)(9)(12)', 'MetaPosVenda', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.26 - Meta Faturamento', '(0)(1)(2)(3)(9)(12)', 'MetaFaturamento', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.27 - Meta Quantidade Venda', '(0)(1)(2)(3)(9)(12)', 'MetaQtdeVenda', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.28 - Meta Indicados', '(0)(1)(2)(3)(9)(12)', 'MetaIndicado', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.29 - Meta Passivos', '(0)(1)(2)(3)(9)(12)', 'MetaPassivo', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.3 - Índice de Renovação CRM', '(0)(1)(2)(3)(9)(12)', 'IndiceRenovacaoCRM', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.30 - Meta Grupo Risco', '(0)(1)(2)(3)(9)(12)', 'MetaGrupoRisco', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.31 - Meta Aniversariante', '(0)(1)(2)(3)(9)(12)', 'MetaAniversariante', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.31 - Meta Perda', '(0)(1)(2)(3)(9)(12)', 'MetaPerda', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.32 - Meta Faltosos', '(0)(1)(2)(3)(9)(12)', 'MetaFaltosos', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.4 - Índice de Conversão CRM', '(0)', 'IndiceConversaoCRM', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.5 - Rotatividade CRM', '(0)', 'RotatividadeCRM', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.7 - Pendência Cliente CRM', '(0)', 'PendenciaClienteCRM', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.9 - Abertura de Meta', '(0)(1)(2)(3)(9)(12)', 'AberturaMeta', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.9 - Modelo Mensagem', '(0)', 'ModeloMensagem', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Alterar Horário - Autorizar', '(0)(1)(2)(3)(9)(12)', 'AlterarHorario_Autorizar', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Carência  para Contrato - Autorizar', '(0)(1)(2)(3)(9)(12)', 'Carencia_Autorizar', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lança Observação', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemObservacao', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lança Observação Geral', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemObservacaoGeral', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lançar aviso ao consultor', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemConsultor', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lançar aviso médico', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemMedico', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lançar mensagem para catraca', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemCatraca', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lançar objetivo do aluno academia', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemObjetivoAluno', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Liberar Trocar de Colabordores Abertura Dia', '(0)(1)(2)(3)(9)(12)', 'LiberarTrocarColabordorAberturaDia', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Manutenção Modalidade - Autorizar', '(0)(1)(2)(3)(9)(12)', 'ManutencaoModalidade_Autorizar', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Movimento de Pagamento - Autoriza Pagamento Cheque Data Posterior a Vencimento', '(0)(1)(2)(3)(9)(12)', 'MovPagamento_AutorizaPagamentoPosteriorDataVencimento', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Processar Rotina Robo - Autorizar', '(0)(1)(2)(3)(9)(12)', 'ProcessarRotinaRobo_Autorizar', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Trancamento do  Contrato - Autoriza', '(0)(1)(2)(3)(9)(12)', 'Trancamento_Autorizar', 2);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '1.1 - Controle de Log', '(0)(1)(2)(3)(9)(12)', 'Log', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '1.2 - Perfil de Acesso', '(0)(1)(2)(3)(9)(12)', 'PerfilAcesso', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '1.3 - Usuário', '(0)(1)(2)(3)(9)(12)', 'Usuario', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.1 - Categoria', '(0)(1)(2)(3)(9)(12)', 'Categoria', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.11 - Endereço', '(0)(1)(2)(3)(9)(12)', 'Endereco', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.12 - Familiar', '(0)(1)(2)(3)(9)(12)', 'Familiar', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.13 - Grau de Instrução', '(0)', 'GrauInstrucao', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.14 - Grupo', '(0)(1)(2)(3)(9)(12)', 'Grupo', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.15 - Log Controle de Usabilidade - Autorizar', '(0)(1)(2)(3)(9)(12)', 'LogControleUsabilidade', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.16 - Movimento de Conta Corrente do Cliente', '(0)(1)(2)(3)(9)(12)', 'MovimentoContaCorrenteCliente', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.17 - País', '(0)', 'Pais', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.18 - Parentesco', '(0)', 'Parentesco', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.19 - Pergunta', '(0)(1)(2)(3)(9)(12)', 'Pergunta', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.2 - Cidade', '(0)(1)(2)(3)(9)(12)', 'Cidade', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.20 - Pergunta Cliente', '(0)(1)(2)(3)(9)(12)', 'PerguntaCliente', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.21 - Pessoa', '(0)(1)(2)(3)(9)(12)', 'Pessoa', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.22 - Profissão', '(0)(1)(2)(3)(9)(12)', 'Profissao', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.23 - Questionário', '(0)(1)(2)(3)(9)(12)', 'Questionario', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.24 -Questionário Cliente', '(0)(1)(2)(3)(9)(12)', 'QuestionarioCliente', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.25 - Questionário Pergunta', '(0)(1)(2)(3)(9)(12)', 'QuestionarioPergunta', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.26 - Resposta da Pergunta', '(0)(1)(2)(3)(9)(12)', 'RespostaPergunta', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.27 - Resposta da Pergunta do Cliente', '(0)(1)(2)(3)(9)(12)', 'RespostaPerguntaCliente', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.28 - Telefone', '(0)(1)(2)(3)(9)(12)', 'Telefone', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.3 - Classificação', '(0)(1)(2)(3)(9)(12)', 'Classificacao', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.4 - Cliente', '(0)(1)(2)(3)(9)(12)', 'Cliente', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.5 - Cliente Classificação', '(0)(1)(2)(3)(9)(12)', 'ClienteClassificacao', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.6 - Cliente Grupo', '(0)(1)(2)(3)(9)(12)', 'ClienteGrupo', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.7 - Colaborador', '(0)(1)(2)(3)(9)(12)', 'Colaborador', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.8 - Configuração do Sistema', '(0)', 'ConfiguracaoSistema', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.9 - Email', '(0)(1)(2)(3)(9)(12)', 'Email', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '3.1 - Contrato', '(0)(1)(2)(3)(9)(12)', 'Contrato', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '3.2 - Convênio', '(0)(1)(2)(3)(9)(12)', 'Convenio', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '3.3 - Convênio de Desconto', '(0)(1)(2)(3)(9)(12)', 'ConvenioDesconto', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '3.4 - Justificativa de Operação', '(0)(1)(2)(3)(9)(12)', 'JustificativaOperacao', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '3.5 - Movimentação do Produto', '(0)(1)(2)(3)(9)(12)', 'MovProduto', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.1 - Aula Avulsa', '(0)(1)(2)(3)(9)(12)', 'AulaAvulsaDiaria', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.10 - Tipo de Retorno', '(0)(1)(2)(3)(9)(12)', 'TipoRetorno', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.11 - Venda Avulsa', '(0)(1)(2)(3)(9)(12)', 'VendaAvulsa', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.13 - Estorno de Recibo', '(0)(1)(2)(3)(9)(12)', 'EstornoRecibo', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.14 - Estorno do Contrato', '(0)(1)(2)(3)(9)(12)', 'EstornoContrato', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.15 - Estorno de Movimentação Produto', '(0)(1)(2)(3)(9)(12)', 'EstornoMovProduto', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.16 - Operadora de Cartão', '(0)(1)(2)(3)(9)(12)', 'OperadoraCartao', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.2 - Banco', '(0)(1)(2)(3)(9)(12)', 'Banco', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.3 - Conta Corrente', '(0)(1)(2)(3)(9)(12)', 'ContaCorrente', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.4 - Convênio de Cobrança', '(0)(1)(2)(3)(9)(12)', 'ConvenioCobranca', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.6 - Forma de Pagamento', '(0)(1)(2)(3)(9)(12)', 'FormaPagamento', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.7 - Movimento de Pagamento', '(0)(1)(2)(3)(9)(12)', 'MovPagamento', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.8 - Movimento da Parcela', '(0)(1)(2)(3)(9)(12)', 'MovParcela', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.9 - Tipo de Remessa', '(0)(1)(2)(3)(9)(12)', 'TipoRemessa', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.1 - Ambiente', '(0)(1)(2)(3)(9)(12)', 'Ambiente', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.10 - Produto', '(0)(1)(2)(3)(9)(12)', 'Produto', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.11 - Texto Padrão', '(0)(1)(2)(3)(9)(12)', 'PlanoTextoPadrao', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.12 - Turma', '(0)(1)(2)(3)(9)(12)', 'Turma', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.2 - Categoria Produto', '(0)(1)(2)(3)(9)(12)', 'CategoriaProduto', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.3 - Condição de Pagamento', '(0)(1)(2)(3)(9)(12)', 'CondicaoPagamento', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.4 - Desconto', '(0)(1)(2)(3)(9)(12)', 'Desconto', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.5 - Horário', '(0)(1)(2)(3)(9)(12)', 'Horario', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.6 - Modalidade', '(0)(1)(2)(3)(9)(12)', 'Modalidade', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.7 - Nível da Turma', '(0)(1)(2)(3)(9)(12)', 'NivelTurma', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.8 - Pacote', '(0)(1)(2)(3)(9)(12)', 'Composicao', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.9 - Plano', '(0)(1)(2)(3)(9)(12)', 'Plano', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.1 - Caixa Por Operador', '(0)(1)(2)(3)(9)(12)', 'CaixaPorOperadorRel', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.10 - Cliente Por Duração', '(0)(1)(2)(3)(9)(12)', 'ClientePorDuracaoRel', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.11 - Relatório Índice de Renovação', '(0)(1)(2)(3)(9)(12)', 'IndiceRenovacao', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.12 - Relatório Índice de Conversão', '(0)(1)(2)(3)(9)(12)', 'IndiceConversao', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.13 - Relatório Rotatividade', '(0)(1)(2)(3)(9)(12)', 'Rotatividade', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.14 - Relatório Business Intelligence', '(0)(1)(2)(3)(9)(12)', 'Business', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.15 - Relatório Pendência Cliente', '(0)(1)(2)(3)(9)(12)', 'PendenciaCliente', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.2 - Relatório Cliente', '(0)(1)(2)(3)(9)(12)', 'ClienteRel', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.3 - Lista Chamada', '(0)(1)(2)(3)(9)(12)', 'ListaChamada', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.7 - Parcela Em Aberto', '(0)(1)(2)(3)(9)(12)', 'ParcelaEmAbertoRel', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.8 - Saldo Conta Corrente de Cliente', '(0)(1)(2)(3)(9)(12)', 'SaldoContaCorrenteRel', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.9 - Totalizadores de Frenquência', '(0)(1)(2)(3)(9)(12)', 'TotalizadorFrequenciaRel', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.1 - Módulo CRM', '(0)(1)(2)(3)(9)(12)', 'ModuloCRM', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.10 - Mensagem Notificação', '(0)(1)(2)(3)(9)(12)', 'MensagemNotificacao', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.11 - Organizar Carteira', '(0)(1)(2)(3)(9)(12)', 'OrganizadorCarteira', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.12 - Cliente Potencial', '(0)(1)(2)(3)(9)(12)', 'Passivo', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.13 - Indicação', '(0)(1)(2)(3)(9)(12)', 'Indicacao', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.14 - Feriado', '(0)(1)(2)(3)(9)(12)', 'Feriado', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.15 - Agenda', '(0)(1)(2)(3)(9)(12)', 'Agenda', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.16 - Evento', '(0)(1)(2)(3)(9)(12)', 'Evento', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.17 - Indicador de Vendas', '(0)(1)(2)(3)(9)(12)', 'IndicadorVenda', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.18 - Indicador de Retenção', '(0)(1)(2)(3)(9)(12)', 'IndicadorRetencao', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.19 - Objeção', '(0)(1)(2)(3)(9)(12)', 'Objecao', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.19 - Visualizar Meta', '(0)(1)(2)(3)(9)(12)', 'VisualizarMeta', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.2 - Definir Layout', '(0)(1)(2)(3)(9)(12)', 'DefinirLayout', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.20 - Histórico Contato', '(0)(1)(2)(3)(9)(12)', 'HistoricoContato', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.4 - Cliente', '(0)(1)(2)(3)(9)(12)', 'Cliente', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.6 - Cliente Grupo', '(1)(9)', 'ClienteGrupo', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.9 - Email', '(1)(9)', 'Email', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.1 - Ambiente', '(0)(1)(2)(3)(9)(12)', 'Ambiente', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.12 - Turma', '(0)(1)(2)(3)(9)(12)', 'Turma', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.5 - Horário', '(0)', 'Horario', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.6 - Modalidade', '(0)', 'Modalidade', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.7 - Nível da Turma', '(0)(9)(1)', 'NivelTurma', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.11 - Relatório Índice de Renovação', '(0)', 'IndiceRenovacao', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.14 - Relatório Business Intelligence', '(0)', 'Business', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.15 - Relatório Pendência Cliente', '(0)(1)(2)(3)(9)(12)', 'PendenciaCliente', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.2 - Relatório Cliente', '(0)', 'ClienteRel', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.9 - Totalizadores de Frenquência', '(0)(1)(2)(3)(9)(12)', 'TotalizadorFrequenciaRel', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.1 - Módulo CRM', '(0)(1)(2)(3)(9)(12)', 'ModuloCRM', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.10 - Mensagem Notificação', '(0)(9)(1)', 'MensagemNotificacao', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.11 - Organizar Carteira', '(0)', 'OrganizadorCarteira', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.15 - Agenda', '(0)', 'Agenda', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.18 - Indicador de Retenção', '(0)', 'IndicadorRetencao', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.20 - Histórico Contato', '(0)', 'HistoricoContato', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.21 - Realizar Contato', '(0)(1)(2)(3)(9)(12)', 'RealizarContato', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.22 - Meta Agendamento', '(0)', 'MetaAgendamento', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.24 - Meta Renovação', '(0)(1)(2)(3)(9)(12)', 'MetaRenovacao', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.25 - Meta Pós Venda', '(0)(1)(2)(3)(9)(12)', 'MetaPosVenda', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.3 - Índice de Renovação CRM', '(0)(1)(2)(3)(9)(12)', 'IndiceRenovacaoCRM', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.30 - Meta Grupo Risco', '(0)(1)(2)(3)(9)(12)', 'MetaGrupoRisco', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.31 - Meta Aniversariante', '(0)(1)(2)(3)(9)(12)', 'MetaAniversariante', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.32 - Meta Faltosos', '(0)(1)(2)(3)(9)(12)', 'MetaFaltosos', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.7 - Pendência Cliente CRM', '(0)', 'PendenciaClienteCRM', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.9 - Abertura de Meta', '(0)', 'AberturaMeta', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lança Observação', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemObservacao', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lança Observação Geral', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemObservacaoGeral', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lançar aviso ao consultor', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemConsultor', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lançar aviso médico', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemMedico', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lançar objetivo do aluno academia', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemObjetivoAluno', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Liberar Trocar de Colabordores Abertura Dia', '(0)(1)(2)(3)(9)(12)', 'LiberarTrocarColabordorAberturaDia', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.21 - Realizar Contato', '(0)(1)(2)(3)(9)(12)', 'RealizarContato', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.22 - Meta Agendamento', '(0)(1)(2)(3)(9)(12)', 'MetaAgendamento', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.23 - Meta Vinte Quatro Horas', '(0)(1)(2)(3)(9)(12)', 'MetaVinteQuatroHoras', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '1.1 - Controle de Log', '(0)(1)(2)(3)(9)(12)', 'Log', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '1.2 - Perfil de Acesso', '(0)(1)(2)(3)(9)(12)', 'PerfilAcesso', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '1.3 - Usuário', '(0)(1)(2)(3)(9)(12)', 'Usuario', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '1.5 - Local de Acesso', '(0)(1)(2)(3)(9)(12)', 'LocalAcesso', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.1 - Categoria', '(0)(1)(2)(3)(9)(12)', 'Categoria', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.10 - Empresa', '(0)(1)(2)(3)(9)(12)', 'Empresa', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.11 - Endereço', '(0)(1)(2)(3)(9)(12)', 'Endereco', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.12 - Familiar', '(0)(1)(2)(3)(9)(12)', 'Familiar', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.13 - Grau de Instrução', '(0)(1)(2)(3)(9)(12)', 'GrauInstrucao', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.14 - Grupo', '(0)(1)(2)(3)(9)(12)', 'Grupo', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.15 - Log Controle de Usabilidade - Autorizar', '(0)(1)(2)(3)(9)(12)', 'LogControleUsabilidade', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.16 - Movimento de Conta Corrente do Cliente', '(0)(1)(2)(3)(9)(12)', 'MovimentoContaCorrenteCliente', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.17 - País', '(0)(1)(2)(3)(9)(12)', 'Pais', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.18 - Parentesco', '(0)(1)(2)(3)(9)(12)', 'Parentesco', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.19 - Pergunta', '(0)(1)(2)(3)(9)(12)', 'Pergunta', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.2 - Cidade', '(0)(1)(2)(3)(9)(12)', 'Cidade', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.20 - Pergunta Cliente', '(0)(1)(2)(3)(9)(12)', 'PerguntaCliente', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.21 - Pessoa', '(0)(1)(2)(3)(9)(12)', 'Pessoa', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.22 - Profissão', '(0)(1)(2)(3)(9)(12)', 'Profissao', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.23 - Questionário', '(0)(1)(2)(3)(9)(12)', 'Questionario', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.24 -Questionário Cliente', '(0)(1)(2)(3)(9)(12)', 'QuestionarioCliente', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.25 - Questionário Pergunta', '(0)(1)(2)(3)(9)(12)', 'QuestionarioPergunta', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.26 - Resposta da Pergunta', '(0)(1)(2)(3)(9)(12)', 'RespostaPergunta', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.27 - Resposta da Pergunta do Cliente', '(0)(1)(2)(3)(9)(12)', 'RespostaPerguntaCliente', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.28 - Telefone', '(0)(1)(2)(3)(9)(12)', 'Telefone', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.3 - Classificação', '(0)(1)(2)(3)(9)(12)', 'Classificacao', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.4 - Cliente', '(0)(1)(2)(3)(9)(12)', 'Cliente', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.5 - Cliente Classificação', '(0)(1)(2)(3)(9)(12)', 'ClienteClassificacao', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.6 - Cliente Grupo', '(0)(1)(2)(3)(9)(12)', 'ClienteGrupo', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.7 - Colaborador', '(0)(1)(2)(3)(9)(12)', 'Colaborador', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.8 - Configuração do Sistema', '(0)(1)(2)(3)(9)(12)', 'ConfiguracaoSistema', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.9 - Email', '(0)(1)(2)(3)(9)(12)', 'Email', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '3.1 - Contrato', '(0)(1)(2)(3)(9)(12)', 'Contrato', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '3.2 - Convênio', '(0)(1)(2)(3)(9)(12)', 'Convenio', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '3.3 - Convênio de Desconto', '(0)(1)(2)(3)(9)(12)', 'ConvenioDesconto', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '3.4 - Justificativa de Operação', '(0)(1)(2)(3)(9)(12)', 'JustificativaOperacao', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '3.5 - Movimentação do Produto', '(0)(1)(2)(3)(9)(12)', 'MovProduto', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.1 - Aula Avulsa', '(0)(1)(2)(3)(9)(12)', 'AulaAvulsaDiaria', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.10 - Tipo de Retorno', '(0)(1)(2)(3)(9)(12)', 'TipoRetorno', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.11 - Venda Avulsa', '(0)(1)(2)(3)(9)(12)', 'VendaAvulsa', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.13 - Estorno de Recibo', '(0)(1)(2)(3)(9)(12)', 'EstornoRecibo', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.14 - Estorno do Contrato', '(0)(1)(2)(3)(9)(12)', 'EstornoContrato', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.15 - Estorno de Movimentação Produto', '(0)(1)(2)(3)(9)(12)', 'EstornoMovProduto', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.16 - Operadora de Cartão', '(0)(1)(2)(3)(9)(12)', 'OperadoraCartao', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.2 - Banco', '(0)(1)(2)(3)(9)(12)', 'Banco', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.3 - Conta Corrente', '(0)(1)(2)(3)(9)(12)', 'ContaCorrente', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.4 - Convênio de Cobrança', '(0)(1)(2)(3)(9)(12)', 'ConvenioCobranca', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.6 - Forma de Pagamento', '(0)(1)(2)(3)(9)(12)', 'FormaPagamento', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.7 - Movimento de Pagamento', '(0)(1)(2)(3)(9)(12)', 'MovPagamento', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.8 - Movimento da Parcela', '(0)(1)(2)(3)(9)(12)', 'MovParcela', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '4.9 - Tipo de Remessa', '(0)(1)(2)(3)(9)(12)', 'TipoRemessa', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.1 - Ambiente', '(0)(1)(2)(3)(9)(12)', 'Ambiente', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.10 - Produto', '(0)(1)(2)(3)(9)(12)', 'Produto', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.11 - Texto Padrão', '(0)(1)(2)(3)(9)(12)', 'PlanoTextoPadrao', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.12 - Turma', '(0)(1)(2)(3)(9)(12)', 'Turma', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.2 - Categoria Produto', '(0)(1)(2)(3)(9)(12)', 'CategoriaProduto', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.3 - Condição de Pagamento', '(0)(1)(2)(3)(9)(12)', 'CondicaoPagamento', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.4 - Desconto', '(0)(1)(2)(3)(9)(12)', 'Desconto', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.5 - Horário', '(0)(1)(2)(3)(9)(12)', 'Horario', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.6 - Modalidade', '(0)(1)(2)(3)(9)(12)', 'Modalidade', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.7 - Nível da Turma', '(0)(1)(2)(3)(9)(12)', 'NivelTurma', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.8 - Pacote', '(0)(1)(2)(3)(9)(12)', 'Composicao', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '5.9 - Plano', '(0)(1)(2)(3)(9)(12)', 'Plano', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.1 - Caixa Por Operador', '(0)(1)(2)(3)(9)(12)', 'CaixaPorOperadorRel', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.10 - Cliente Por Duração', '(0)(1)(2)(3)(9)(12)', 'ClientePorDuracaoRel', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.11 - Relatório Índice de Renovação', '(0)(1)(2)(3)(9)(12)', 'IndiceRenovacao', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.12 - Relatório Índice de Conversão', '(0)(1)(2)(3)(9)(12)', 'IndiceConversao', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.24 - Meta Renovação', '(0)(1)(2)(3)(9)(12)', 'MetaRenovacao', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.25 - Meta Pós Venda', '(0)(1)(2)(3)(9)(12)', 'MetaPosVenda', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.26 - Meta Faturamento', '(0)(1)(2)(3)(9)(12)', 'MetaFaturamento', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.27 - Meta Quantidade Venda', '(0)(1)(2)(3)(9)(12)', 'MetaQtdeVenda', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.28 - Meta Indicados', '(0)(1)(2)(3)(9)(12)', 'MetaIndicado', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.29 - Meta Passivos', '(0)(1)(2)(3)(9)(12)', 'MetaPassivo', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.3 - Índice de Renovação CRM', '(0)(1)(2)(3)(9)(12)', 'IndiceRenovacaoCRM', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.30 - Meta Grupo Risco', '(0)(1)(2)(3)(9)(12)', 'MetaGrupoRisco', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.31 - Meta Aniversariante', '(0)(1)(2)(3)(9)(12)', 'MetaAniversariante', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.31 - Meta Perda', '(0)(1)(2)(3)(9)(12)', 'MetaPerda', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.32 - Meta Faltosos', '(0)(1)(2)(3)(9)(12)', 'MetaFaltosos', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.4 - Índice de Conversão CRM', '(0)(1)(2)(3)(9)(12)', 'IndiceConversaoCRM', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.5 - Rotatividade CRM', '(0)(1)(2)(3)(9)(12)', 'RotatividadeCRM', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.6 - Configuração Sistema CRM', '(0)(1)(2)(3)(9)(12)', 'ConfiguracaoSistemaCRM', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.7 - Pendência Cliente CRM', '(0)(1)(2)(3)(9)(12)', 'PendenciaClienteCRM', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.8 - Grupo Colaborador', '(0)(1)(2)(3)(9)(12)', 'GrupoColaborador', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.9 - Abertura de Meta', '(0)(1)(2)(3)(9)(12)', 'AberturaMeta', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.9 - Modelo Mensagem', '(0)(1)(2)(3)(9)(12)', 'ModeloMensagem', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Alterar Horário - Autorizar', '(0)(1)(2)(3)(9)(12)', 'AlterarHorario_Autorizar', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Atestado para Contrato - Autorizar', '(0)(1)(2)(3)(9)(12)', 'Atestado_Autorizar', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Bonus para Contrato - Autorizar', '(0)(1)(2)(3)(9)(12)', 'Bonus_Autorizar', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Cancelamento Contrato - Autorizar', '(0)(1)(2)(3)(9)(12)', 'CancelamentoContrato_Autorizar', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Carência  para Contrato - Autorizar', '(0)(1)(2)(3)(9)(12)', 'Carencia_Autorizar', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lança Observação', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemObservacao', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lança Observação Geral', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemObservacaoGeral', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lançar aviso ao consultor', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemConsultor', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lançar aviso médico', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemMedico', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lançar mensagem para catraca', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemCatraca', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lançar objetivo do aluno academia', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemObjetivoAluno', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Liberar Cancelamento Contrato - Autorizar', '(0)(1)(2)(3)(9)(12)', 'Liberar_CancelamentoContrato_Autorizar', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Liberar Relatório de Fechamento de Caixa por Operador - Todos os colaboradores', '(0)(1)(2)(3)(9)(12)', 'LiberarTodosColaboradoresRelatorioFechamentoCaixaOperador', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.13 - Relatório Rotatividade', '(0)(1)(2)(3)(9)(12)', 'Rotatividade', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.14 - Relatório Business Intelligence', '(0)(1)(2)(3)(9)(12)', 'Business', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.15 - Relatório Pendência Cliente', '(0)(1)(2)(3)(9)(12)', 'PendenciaCliente', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.2 - Relatório Cliente', '(0)(1)(2)(3)(9)(12)', 'ClienteRel', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.3 - Lista Chamada', '(0)(1)(2)(3)(9)(12)', 'ListaChamada', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.4 - Receita Por Período Sintético', '(0)(1)(2)(3)(9)(12)', 'ReceitaPorPeriodoSinteticoRel', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.5 - Competência Mensal', '(0)(1)(2)(3)(9)(12)', 'CompetenciaMensalRel', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.6 - Faturamento Sintético', '(0)(1)(2)(3)(9)(12)', 'FaturamentoSinteticoRel', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.7 - Parcela Em Aberto', '(0)(1)(2)(3)(9)(12)', 'ParcelaEmAbertoRel', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.8 - Saldo Conta Corrente de Cliente', '(0)(1)(2)(3)(9)(12)', 'SaldoContaCorrenteRel', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '6.9 - Totalizadores de Frenquência', '(0)(1)(2)(3)(9)(12)', 'TotalizadorFrequenciaRel', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.1 - Módulo CRM', '(0)(1)(2)(3)(9)(12)', 'ModuloCRM', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.10 - Mensagem Notificação', '(0)(1)(2)(3)(9)(12)', 'MensagemNotificacao', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.11 - Organizar Carteira', '(0)(1)(2)(3)(9)(12)', 'OrganizadorCarteira', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.12 - Cliente Potencial', '(0)(1)(2)(3)(9)(12)', 'Passivo', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.13 - Indicação', '(0)(1)(2)(3)(9)(12)', 'Indicacao', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.14 - Feriado', '(0)(1)(2)(3)(9)(12)', 'Feriado', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.15 - Agenda', '(0)(1)(2)(3)(9)(12)', 'Agenda', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.16 - Evento', '(0)(1)(2)(3)(9)(12)', 'Evento', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.17 - Indicador de Vendas', '(0)(1)(2)(3)(9)(12)', 'IndicadorVenda', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.18 - Indicador de Retenção', '(0)(1)(2)(3)(9)(12)', 'IndicadorRetencao', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.19 - Objeção', '(0)(1)(2)(3)(9)(12)', 'Objecao', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.19 - Visualizar Meta', '(0)(1)(2)(3)(9)(12)', 'VisualizarMeta', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.2 - Definir Layout', '(0)(1)(2)(3)(9)(12)', 'DefinirLayout', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.20 - Histórico Contato', '(0)(1)(2)(3)(9)(12)', 'HistoricoContato', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.21 - Realizar Contato', '(0)(1)(2)(3)(9)(12)', 'RealizarContato', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.22 - Meta Agendamento', '(0)(1)(2)(3)(9)(12)', 'MetaAgendamento', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.23 - Meta Vinte Quatro Horas', '(0)(1)(2)(3)(9)(12)', 'MetaVinteQuatroHoras', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.24 - Meta Renovação', '(0)(1)(2)(3)(9)(12)', 'MetaRenovacao', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.25 - Meta Pós Venda', '(0)(1)(2)(3)(9)(12)', 'MetaPosVenda', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.26 - Meta Faturamento', '(0)(1)(2)(3)(9)(12)', 'MetaFaturamento', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.27 - Meta Quantidade Venda', '(0)(1)(2)(3)(9)(12)', 'MetaQtdeVenda', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.28 - Meta Indicados', '(0)(1)(2)(3)(9)(12)', 'MetaIndicado', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.29 - Meta Passivos', '(0)(1)(2)(3)(9)(12)', 'MetaPassivo', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.3 - Índice de Renovação CRM', '(0)(1)(2)(3)(9)(12)', 'IndiceRenovacaoCRM', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.30 - Meta Grupo Risco', '(0)(1)(2)(3)(9)(12)', 'MetaGrupoRisco', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.31 - Meta Aniversariante', '(0)(1)(2)(3)(9)(12)', 'MetaAniversariante', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.31 - Meta Perda', '(0)(1)(2)(3)(9)(12)', 'MetaPerda', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.32 - Meta Faltosos', '(0)(1)(2)(3)(9)(12)', 'MetaFaltosos', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.4 - Índice de Conversão CRM', '(0)(1)(2)(3)(9)(12)', 'IndiceConversaoCRM', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.5 - Rotatividade CRM', '(0)(1)(2)(3)(9)(12)', 'RotatividadeCRM', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.6 - Configuração Sistema CRM', '(0)(1)(2)(3)(9)(12)', 'ConfiguracaoSistemaCRM', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.7 - Pendência Cliente CRM', '(0)(1)(2)(3)(9)(12)', 'PendenciaClienteCRM', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.8 - Grupo Colaborador', '(0)(1)(2)(3)(9)(12)', 'GrupoColaborador', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.9 - Abertura de Meta', '(0)(1)(2)(3)(9)(12)', 'AberturaMeta', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '7.9 - Modelo Mensagem', '(0)(1)(2)(3)(9)(12)', 'ModeloMensagem', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, '4.15 - Desconto do Plano', '(0)(1)(2)(3)(9)(12)', 'DescontoPlano', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Alterar Horário - Autorizar', '(0)(1)(2)(3)(9)(12)', 'AlterarHorario_Autorizar', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Atestado para Contrato - Autorizar', '(0)(1)(2)(3)(9)(12)', 'Atestado_Autorizar', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Bonus para Contrato - Autorizar', '(0)(1)(2)(3)(9)(12)', 'Bonus_Autorizar', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Cancelamento Contrato - Autorizar', '(0)(1)(2)(3)(9)(12)', 'CancelamentoContrato_Autorizar', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Carência  para Contrato - Autorizar', '(0)(1)(2)(3)(9)(12)', 'Carencia_Autorizar', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lança Observação', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemObservacao', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lança Observação Geral', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemObservacaoGeral', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lançar aviso ao consultor', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemConsultor', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lançar aviso médico', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemMedico', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lançar mensagem para catraca', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemCatraca', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Lançar objetivo do aluno academia', '(0)(1)(2)(3)(9)(12)', 'LancarMensagemObjetivoAluno', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Liberar Cancelamento Contrato - Autorizar', '(0)(1)(2)(3)(9)(12)', 'Liberar_CancelamentoContrato_Autorizar', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Liberar Relatório de Fechamento de Caixa por Operador - Todos os colaboradores', '(0)(1)(2)(3)(9)(12)', 'LiberarTodosColaboradoresRelatorioFechamentoCaixaOperador', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Liberar Relatório de Fechamento de Caixa por Operador - Todos os dias', '(0)(1)(2)(3)(9)(12)', 'LiberarTodosDiasRelatorioFechamentoCaixaOperador', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Liberar Trocar de Colabordores Abertura Dia', '(0)(1)(2)(3)(9)(12)', 'LiberarTrocarColabordorAberturaDia', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Manutenção Modalidade - Autorizar', '(0)(1)(2)(3)(9)(12)', 'ManutencaoModalidade_Autorizar', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Movimento de Pagamento - Autoriza Pagamento Cheque Data Posterior a Vencimento', '(0)(1)(2)(3)(9)(12)', 'MovPagamento_AutorizaPagamentoPosteriorDataVencimento', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Processar Rotina Robo - Autorizar', '(0)(1)(2)(3)(9)(12)', 'ProcessarRotinaRobo_Autorizar', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Trancamento do  Contrato - Autoriza', '(0)(1)(2)(3)(9)(12)', 'Trancamento_Autorizar', 1);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Liberar Relatório de Fechamento de Caixa por Operador - Todos os dias', '(0)(1)(2)(3)(9)(12)', 'LiberarTodosDiasRelatorioFechamentoCaixaOperador', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Liberar Trocar de Colabordores Abertura Dia', '(0)(1)(2)(3)(9)(12)', 'LiberarTrocarColabordorAberturaDia', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Manutenção Modalidade - Autorizar', '(0)(1)(2)(3)(9)(12)', 'ManutencaoModalidade_Autorizar', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Movimento de Pagamento - Autoriza Pagamento Cheque Data Posterior a Vencimento', '(0)(1)(2)(3)(9)(12)', 'MovPagamento_AutorizaPagamentoPosteriorDataVencimento', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Processar Rotina Robo - Autorizar', '(0)(1)(2)(3)(9)(12)', 'ProcessarRotinaRobo_Autorizar', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 2, 'Trancamento do  Contrato - Autoriza', '(0)(1)(2)(3)(9)(12)', 'Trancamento_Autorizar', 3);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '1.3 - Usuário', '(0)', 'Usuario', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.1 - Categoria', '(0)', 'Categoria', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.14 - Grupo', '(0)', 'Grupo', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.15 - Log Controle de Usabilidade - Autorizar', '(0)', 'LogControleUsabilidade', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.21 - Pessoa', '(0)(1)(2)(3)(9)(12)', 'Pessoa', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.22 - Profissão', '(0)', 'Profissao', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.23 - Questionário', '(0)', 'Questionario', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.24 -Questionário Cliente', '(0)', 'QuestionarioCliente', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.25 - Questionário Pergunta', '(0)', 'QuestionarioPergunta', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.28 - Telefone', '(0)', 'Telefone', 4);
INSERT INTO permissao (valorfinal, valorinicial, valorespecifico, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) VALUES ('', '', '', 1, '2.3 - Classificação', '(0)', 'Classificacao', 4);
-----------------------------------------------------------------------------------------------------------------------------------------
insert into numeroMatricula values(0);

insert into robo(dia,dataHoraInicio,dataHoraFim,texto,rotinaProcessada)
    values (current_timestamp, current_timestamp, current_timestamp,'Inicio do Sistema', true);
	
--------------Categoria Produto----------------------
INSERT INTO categoriaproduto (descricao, codigo) 
	VALUES ('SERVIÇOS', 1);
INSERT INTO categoriaproduto (descricao, codigo) 
	VALUES ('ALIMENTOS', 2);
INSERT INTO categoriaproduto (descricao, codigo) 
	VALUES ('BEBIDAS', 3);
INSERT INTO categoriaproduto (descricao, codigo) 
	VALUES ('SUPLEMENTOS ALIMENTARES', 4);
INSERT INTO categoriaproduto (descricao, codigo) 
	VALUES ('FAZ PARTE DOS PLANOS', 5);
INSERT INTO categoriaproduto (descricao, codigo) 
	VALUES ('VESTUÁRIO', 6);
SELECT setval('categoriaproduto_codigo_seq', 6, true);

-----------------------------Produtos necessarios nas Operacoes de Contrato---------------------------------------

INSERT INTO produto (desativado, tipoproduto, desconto, valorbasecalculo, valorfinal, 
					nrdiasvigencia, datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'DV', NULL, 0, 0, 0, NULL, NULL, NULL, 'Devolução de Dinheiro - Cancelamento', NULL, 1);
INSERT INTO produto (desativado, tipoproduto, desconto, valorbasecalculo, valorfinal, 
					nrdiasvigencia, datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'QU', NULL, 0, 0, 0, NULL, NULL, NULL, 'Quitação de Dinheiro - Cancelamento', NULL, 2);
INSERT INTO produto (desativado, tipoproduto, desconto, valorbasecalculo, valorfinal, 
					nrdiasvigencia, datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'MM', NULL, 0, 0, 0, NULL, NULL, NULL, 'Manutenção - Modalidade', NULL, 3);
INSERT INTO produto (desativado, tipoproduto, desconto, valorbasecalculo, valorfinal, 
					nrdiasvigencia, datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'AH', NULL, 0, 0, 0, NULL, NULL, NULL, 'Alterar - Horário', NULL, 4);
	
--------------------------------Produtos---------------------------------------------------------------------------------------------------------	

INSERT INTO produto (desativado, tipoproduto, desconto, 
					valorbasecalculo, valorfinal, nrdiasvigencia, 
					datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'PM', NULL, 0, 0, 0, NULL, NULL, NULL, 'PLANO', 5, 5);
INSERT INTO produto (desativado, tipoproduto, desconto, 
					valorbasecalculo, valorfinal, nrdiasvigencia, 
					datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'MA', NULL, 0, 0, 30, NULL, NULL, NULL, 'MATRICULA', 5, 6);
INSERT INTO produto (desativado, tipoproduto, desconto, 
					valorbasecalculo, valorfinal, nrdiasvigencia, 
					datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'RE', NULL, 0, 0, 20, NULL, NULL, NULL, 'REMATRICULA', 5, 7);
INSERT INTO produto (desativado, tipoproduto, desconto, 
					valorbasecalculo, valorfinal, nrdiasvigencia, 
					datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'RN', NULL, 0, 0, 0, NULL, NULL, NULL, 'RENOVACAO', 5, 8);
INSERT INTO produto (desativado, tipoproduto, desconto, 
					valorbasecalculo, valorfinal, nrdiasvigencia, 
					datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'DE', NULL, 0, 0, 0, NULL, NULL, NULL, 'DESCONTO EXTRA', 5, 9);
INSERT INTO produto (desativado, tipoproduto, desconto, 
					valorbasecalculo, valorfinal, nrdiasvigencia, 
					datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'SE', NULL, 0, 0, 0, NULL, NULL, NULL, 'RECEBER DEBITO', 5, 10);
INSERT INTO produto (desativado, tipoproduto, desconto, 
					valorbasecalculo, valorfinal, nrdiasvigencia, 
					datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'TR', NULL, 0, 15, 30, NULL, NULL, 'ID', 'TRANCAMENTO 30 DIAS', 5, 11);
INSERT INTO produto (desativado, tipoproduto, desconto, 
					valorbasecalculo, valorfinal, nrdiasvigencia,
					datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'SE', NULL, 0, 0, 0, NULL, NULL, '', 'CUSTO ADMINISTRATIVO DO CANCELAMENTO', 5, 12);
INSERT INTO produto (desativado, tipoproduto, desconto, 
					valorbasecalculo, valorfinal, nrdiasvigencia, 
					datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'SE', NULL, 0, 0, 90, NULL, NULL, 'ID', 'AVALIAÇÃO FÍSICA', 1, 13);
INSERT INTO produto (desativado, tipoproduto, desconto, 
					valorbasecalculo, valorfinal, nrdiasvigencia, 
					datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'AA', NULL, 0, 15, 1, NULL, NULL, 'ID', 'AULA AVULSA', 1, 14);
INSERT INTO produto (desativado, tipoproduto, desconto, 
					valorbasecalculo, valorfinal, nrdiasvigencia, 
					datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'DI', NULL, 0, 80, 10, NULL, NULL, 'ID', 'DIARIA 07 DIAS CORRIDOS', 5, 15);
INSERT INTO produto (desativado, tipoproduto, desconto, 
					valorbasecalculo, valorfinal, nrdiasvigencia, 
					datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'FR', NULL, 0, 0, 1, NULL, NULL, 'ID', '1 DIA DE AULA EXPERIMENTAL', 5, 16);
INSERT INTO produto (desativado, tipoproduto, desconto, 
					valorbasecalculo, valorfinal, nrdiasvigencia, 
					datafinalvigenciafixa, datainiciovigencia,
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'FR', NULL, 0, 0, 3, NULL, NULL, 'ID', '3 DIAS DE AULA EXPERIMENTAL', 5, 17);
INSERT INTO produto (desativado, tipoproduto, desconto, 
					valorbasecalculo, valorfinal, nrdiasvigencia, 
					datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'TR', NULL, 0, 20, 45, NULL, NULL, 'ID', 'TRANCAMENTO 45 DIAS', 5, 18);
INSERT INTO produto (desativado, tipoproduto, desconto,
					valorbasecalculo, valorfinal, nrdiasvigencia, 
					datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'TR', NULL, 0, 30, 60, NULL, NULL, 'ID', 'TRANCAMENTO 60 DIAS', 5, 19);
INSERT INTO produto (desativado, tipoproduto, desconto, 
					valorbasecalculo, valorfinal, nrdiasvigencia, 
					datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'PE', NULL, 0, 15, 0, NULL, NULL, '', 'LUVA ', 6, 20);
INSERT INTO produto (desativado, tipoproduto, desconto, 
					valorbasecalculo, valorfinal, nrdiasvigencia, 
					datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'PE', NULL, 0, 60, 0, NULL, NULL, '', 'LUVA BOXE', 6, 21);
INSERT INTO produto (desativado, tipoproduto, desconto, 
					valorbasecalculo, valorfinal, nrdiasvigencia, 
					datafinalvigenciafixa, datainiciovigencia, 
					tipovigencia, descricao, categoriaproduto, codigo) 
					VALUES (false, 'PE', NULL, 0, 25, 0, NULL, NULL, '', 'TOUCA AZUL', 6, 22);
SELECT setval('produto_codigo_seq', 22, true);

-- Taxa de cancelamento é o 12.
-- Nao necessita do sequencial ... Obs se colocar o DELETE entao devemos fazer outro script.
----------------------Pais----------------------------------------------------------
INSERT INTO pais (nome, codigo) VALUES ('BRASIL', 1);
SELECT setval('pais_codigo_seq', 1, true);

--------------------Estado - de -  Pais---------------------------------------------
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'GO', 'GOIÁS', 1);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'SP', 'SÃO PAULO', 2);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'RJ', 'RIO DE JANEIRO', 3);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'MG', 'MINAS GERAIS', 4);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'MT', 'MATO GROSSO', 5);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'RS', 'RIO GRANDE DO SUL', 6);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'AC', 'ACRE', 7);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'AL', 'ALAGOAS', 8);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'AP', 'AMAPÁ', 9);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'AM', 'AMAZONAS', 10);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'BA', 'BAHIA', 11);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'CE', 'CEARÁ', 12);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'DF', 'DISTRITO FEDERAL', 13);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'ES', 'ESPÍRITO SANTO', 14);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'RR', 'RORAIMA', 15);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'MA', 'MARANHÃO', 16);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'PA', 'PARÁ', 17);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'PB', 'PARAÍBA', 18);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'PR', 'PARANÁ', 19);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'PE', 'PERNAMBUCO', 20);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'PI', 'PIAUÍ', 21);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'RN', 'RIO GRANDE DO NORTE', 22);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'RO', 'RONDÔNIA', 23);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'TO', 'TOCANTINS', 24);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'SC', 'SANTA CATARINA', 25);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'SE', 'SERGIPE', 26);
INSERT INTO estado (pais, sigla, descricao, codigo) VALUES (1, 'MS', 'MATO GROSSO DO SUL', 27);
SELECT setval('estado_codigo_seq', 27, true);

--------------------------Ambientes--------------------------------------------------------
INSERT INTO ambiente (descricao, codigo) VALUES ('PISCINA', 1);
INSERT INTO ambiente (descricao, codigo) VALUES ('SALA 1', 2);
INSERT INTO ambiente (descricao, codigo) VALUES ('SALA 2', 3);
INSERT INTO ambiente (descricao, codigo) VALUES ('SALA 3', 4);
SELECT setval('ambiente_codigo_seq', 4, true);

------------------------Categorias----------------------------------------------------------
INSERT INTO categoria (nrconvitepermitido, tipocategoria, nome, codigo) VALUES (0, 'AL', 'ALUNO', 1);
INSERT INTO categoria (nrconvitepermitido, tipocategoria, nome, codigo) VALUES (0, 'AL', 'VISITANTE AULA EXPERIMENTAL', 2);
INSERT INTO categoria (nrconvitepermitido, tipocategoria, nome, codigo) VALUES (0, 'SO', 'SOCIO CLUBE', 3);
INSERT INTO categoria (nrconvitepermitido, tipocategoria, nome, codigo) VALUES (0, 'AL', 'CLIENTE DE PERSONAL INTERNO', 4);
INSERT INTO categoria (nrconvitepermitido, tipocategoria, nome, codigo) VALUES (0, 'AL', 'CLIENTE DE PERSONAL EXTERNO', 5);
SELECT setval('categoria_codigo_seq', 5, true);

------------------------Perguntas para Questionarios---------------------------------------
INSERT INTO pergunta (tipopergunta, descricao, codigo) VALUES ('ME', 'QUAL SEU OBJETIVO?', 1);
INSERT INTO pergunta (tipopergunta, descricao, codigo) VALUES ('SE', 'HÁ QUANTO TEMPO ESTÁ PENSANDO EM COMEÇAR ATIVIDADE FÍSICA?', 2);
INSERT INTO pergunta (tipopergunta, descricao, codigo) VALUES ('TE', 'O QUE TE FEZ PARAR DA ÚLTIMA VEZ?', 3);
INSERT INTO pergunta (tipopergunta, descricao, codigo) VALUES ('ME', 'O QUE TE IMPEDIU DE COMEÇAR ANTES?', 4);
INSERT INTO pergunta (tipopergunta, descricao, codigo) VALUES ('ME', 'ATIVIDADES DE INTERESSE: ', 5);
INSERT INTO pergunta (tipopergunta, descricao, codigo) VALUES ('SE', 'EM QUANTO TEMPO?', 6);
INSERT INTO pergunta (tipopergunta, descricao, codigo) VALUES ('ME', 'COMO CONHECEU A ACADEMIA?', 7);
SELECT setval('pergunta_codigo_seq', 7, true);

------------------------Possiveis respostas para as perguntas-----------------------
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('MENOS DE UM MES.', 2,1);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('MAIS OU MENOS 3 MESES', 2,2);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('MAIS OU MENOS 6 MESES', 2,3);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('MAIS OU MENOS 1 ANO', 2,4);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('A MAIS DE UM ANO', 2,5);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('TER UM HORÁRIO DEFINIDO', 4,6);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('FALTA DE DINHEIRO', 4,7);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('CONVERSAR COM ...', 4, 8);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('TRANSPORTE PARA CHEGAR A ACADEMIA', 4, 9);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('MOTIVAÇÃO', 4, 10);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('MUSCULAÇÃO', 5, 11);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('ABDOMINAL', 5, 12);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('PILATES', 5, 13);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('SPINNING', 5, 14);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('JUMP', 5, 15);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('LUTAS', 5, 16);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('RITMOS', 5, 17);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('EMAGRESCIMENTO', 1,18);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('AUMENTO DE MASSA ', 1,19);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('DEFINIÇÃO MUSCULAR', 1, 20);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('QUALIDADE DE VIDA', 1, 21);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('CONDICIONAMENTO', 1, 22);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('ALÍVIO DE STRESS', 1, 23);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('REABILITAÇÃO', 1, 24);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('LAZER', 1, 25);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('AMIGOS', 1, 26);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('3 MESES', 6, 27);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('6 MESES', 6, 28);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('9 MESES', 6, 29);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('12 MESES', 6, 30);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('MAIS', 6, 31);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('AMIGOS', 7, 32);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('PASSANDO EM FRENTE', 7, 33);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('EX-ALUNOS', 7, 34);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('MALA DIRETA', 7, 35);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('LISTA TELEFÔNICA', 7, 36);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('FOLDER', 7, 37);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('OUTDOOR', 7, 38);
INSERT INTO respostapergunta (descricaorespota, pergunta, codigo) VALUES ('INTERNET', 7, 39);
SELECT setval('respostapergunta_codigo_seq', 39, true);

----------------------------------------------- Questinario Perguntas------------------------------------------------
INSERT INTO questionariopergunta (pergunta, questionario, codigo) VALUES (1, 2, 1);
INSERT INTO questionariopergunta (pergunta, questionario, codigo) VALUES (2, 2, 2);
INSERT INTO questionariopergunta (pergunta, questionario, codigo) VALUES (3, 2, 3);
INSERT INTO questionariopergunta (pergunta, questionario, codigo) VALUES (4, 3, 4);
INSERT INTO questionariopergunta (pergunta, questionario, codigo) VALUES (1, 3, 5);
INSERT INTO questionariopergunta (pergunta, questionario, codigo) VALUES (2, 3, 6);
INSERT INTO questionariopergunta (pergunta, questionario, codigo) VALUES (5, 1, 7);
INSERT INTO questionariopergunta (pergunta, questionario, codigo) VALUES (1, 1, 8);
INSERT INTO questionariopergunta (pergunta, questionario, codigo) VALUES (6, 1, 9);
INSERT INTO questionariopergunta (pergunta, questionario, codigo) VALUES (2, 1, 10);
INSERT INTO questionariopergunta (pergunta, questionario, codigo) VALUES (4, 1, 11);
INSERT INTO questionariopergunta (pergunta, questionario, codigo) VALUES (7, 1, 12);
SELECT setval('questionariopergunta_codigo_seq', 12, true);

----------------------------------------------------------------------------------------------------------------
INSERT INTO empresa (nrdiasavencer, toleranciapagamento, qtdfaltapeso1, qtdfaltainiciopeso2,
		qtdfaltaterminopeso2, qtdfaltapeso3, fotorelatorio, foto, alturafotoempresa, 
		alturafotorelatorio, largurafotoempresa, largurafotorelatorio, carenciarenovacao,
		 mascaramatricula, nrdiasvigentequestionariovisita, nrdiasvigentequestionarioretorno, 
		 nrdiasvigentequestionariorematricula, permitecontratosconcomintante, permitesituacaoatestadocontrato, 
		 questionariorematricula, questionarioretorno, questionarioprimeiravisita, 
		 juroparcela, multa, fax, site, email, telcomercial3, telcomercial2, 
		 telcomercial1, inscestadual, cnpj, cep, estado, cidade, pais, 
		 complemento, numero, setor, endereco, razaosocial, nome, codigo, 
		 carencia, nrdiasprorata, permiteacessoalunosoutraempresa, somadv) 
		 VALUES (15, 5, 5, 6, 8, 9, '','', '224', '224', '183', '183', 10, '', 30, 30, 30, false, true, 2, 2, 1, 0, 0, '', '', '', '', '', '', '111111111111111', '11.111.111/1111-11', '', 0, 0, 1, '', '', '', 'ENDEREÇO', 'NOME DA EMPRESA', 'ACADEMIA NOME', 1, 7, 20, false, 0);
SELECT setval('empresa_codigo_seq', 1, true);

----------------------------------------------Configuração - LFM-23/12/2010------------------------------------------------------
DELETE FROM configuracaosistema; 
INSERT INTO configuracaosistema (nrdiasavencer, qtdfaltapeso1, qtdfaltainiciopeso2, qtdfaltaterminopeso2, qtdfaltapeso3, carenciarenovacao, mascaramatricula, multa, juroparcela, questionariorematricula, questionarioretorno, questionarioprimeiravisita, nrdiasvigentequestionariovisita, nrdiasvigentequestionarioretorno, nrdiasvigentequestionariorematricula, toleranciapagamento, emailcontapagdigital, tokencontapagdigital, tokencontasms, codigo, carencia, nrdiasprorata, cpfvalidar) VALUES (15, 1, 2, 3, 4, 10, 'xxxxxx', 0, 0, 2, 3, 1, 30, 30, 30, 0, NULL, NULL, NULL, 1, 7, 20, true);

----------------------------------------------Modalidade-------------------------------------------------------------------
INSERT INTO modalidade (modalidadedefault, utilizarturma, utilizarproduto, valormensal, ativo, nome, nrvezes, codigo) VALUES (false, false, true, 120, true, 'MUSCULAÇÃO', 6, 1);
INSERT INTO modalidade (modalidadedefault, utilizarturma, utilizarproduto, valormensal, ativo, nome, nrvezes, codigo) VALUES (false, false, false, 100, true, 'GINÁSTICA', 6, 2);
INSERT INTO modalidade (modalidadedefault, utilizarturma, utilizarproduto, valormensal, ativo, nome, nrvezes, codigo) VALUES (false, false, false, 135, true, 'PILATES', 6, 3);
INSERT INTO modalidade (modalidadedefault, utilizarturma, utilizarproduto, valormensal, ativo, nome, nrvezes, codigo) VALUES (false, false, true, 100, true, 'DANÇA', 3, 4);
INSERT INTO modalidade (modalidadedefault, utilizarturma, utilizarproduto, valormensal, ativo, nome, nrvezes, codigo) VALUES (false, false, true, 69, true, 'LUTAS', 3, 5);
INSERT INTO modalidade (modalidadedefault, utilizarturma, utilizarproduto, valormensal, ativo, nome, nrvezes, codigo) VALUES (false, true, true, 100, true, 'PISCINA', 4, 6);
INSERT INTO modalidade (modalidadedefault, utilizarturma, utilizarproduto, valormensal, ativo, nome, nrvezes, codigo) VALUES (true, false, false, 95, true, 'ERGOMETRIA', 7, 7);
INSERT INTO modalidade (modalidadedefault, utilizarturma, utilizarproduto, valormensal, ativo, nome, nrvezes, codigo) VALUES (false, true, true, 100, true, 'NATAÇÃO ADULTO', 2, 8);
INSERT INTO modalidade (modalidadedefault, utilizarturma, utilizarproduto, valormensal, ativo, nome, nrvezes, codigo) VALUES (false, true, true, 100, true, 'NATAÇÃO INFANTIL', 2, 9);
INSERT INTO modalidade (modalidadedefault, utilizarturma, utilizarproduto, valormensal, ativo, nome, nrvezes, codigo) VALUES (false, true, true, 100, true, 'KIDS', 2, 10);


SELECT setval('modalidade_codigo_seq', 10, true);
----------------------------------------------Modalidade Empresa------------------------------------------------------
INSERT INTO modalidadeempresa (empresa, modalidade, codigo) VALUES (1, 1, 1);
INSERT INTO modalidadeempresa (empresa, modalidade, codigo) VALUES (1, 2, 2);
INSERT INTO modalidadeempresa (empresa, modalidade, codigo) VALUES (1, 3, 3);
INSERT INTO modalidadeempresa (empresa, modalidade, codigo) VALUES (1, 4, 4);
INSERT INTO modalidadeempresa (empresa, modalidade, codigo) VALUES (1, 5, 5);
INSERT INTO modalidadeempresa (empresa, modalidade, codigo) VALUES (1, 6, 6);
INSERT INTO modalidadeempresa (empresa, modalidade, codigo) VALUES (1, 7, 7);
INSERT INTO modalidadeempresa (empresa, modalidade, codigo) VALUES (1, 8, 8);
INSERT INTO modalidadeempresa (empresa, modalidade, codigo) VALUES (1, 9, 9);
INSERT INTO modalidadeempresa (empresa, modalidade, codigo) VALUES (1, 10, 10);
SELECT setval('modalidadeempresa_codigo_seq', 10, true);

----------------------------------------------Composição--------------------------------------------------------------------------
INSERT INTO composicao (composicaodefault, composicaoadicional, precocomposicao, descricao, empresa, codigo) VALUES (false, false, 150, 'PACOTE MASTER  TOTAL', 1, 1);

----------------------------------------------Composição Modalidades---------------------------------------------------------------
INSERT INTO composicaomodalidade (valormensalcomposicao, precomodalidade, composicao, modalidade, codigo) VALUES (45, 120, 1, 1, 1);
INSERT INTO composicaomodalidade (valormensalcomposicao, precomodalidade, composicao, modalidade, codigo) VALUES (35, 100, 1, 2, 2);
INSERT INTO composicaomodalidade (valormensalcomposicao, precomodalidade, composicao, modalidade, codigo) VALUES (45, 135, 1, 3, 3);
INSERT INTO composicaomodalidade (valormensalcomposicao, precomodalidade, composicao, modalidade, codigo) VALUES (25, 100, 1, 4, 4);

SELECT setval('composicaomodalidade_codigo_seq', 4, true);

----------------------------------------------Formas de Pagamento  - Que serão usadas pela academia
INSERT INTO formapagamento (taxacartao, tipoformapagamento, conveniocobranca, descricao, codigo) VALUES (0, 'AV', NULL, 'DINHEIRO', 1);
INSERT INTO formapagamento (taxacartao, tipoformapagamento, conveniocobranca, descricao, codigo) VALUES (0, 'CA', NULL, 'CARTÃO DE CRÉDITO', 2);
INSERT INTO formapagamento (taxacartao, tipoformapagamento, conveniocobranca, descricao, codigo) VALUES (0, 'CD', NULL, 'CARTÃO DE DÉBITO', 3);
INSERT INTO formapagamento (taxacartao, tipoformapagamento, conveniocobranca, descricao, codigo) VALUES (0, 'CH', NULL, 'CHEQUE', 4);
INSERT INTO formapagamento (taxacartao, tipoformapagamento, conveniocobranca, descricao, codigo) VALUES (0, 'CC', NULL, 'CONTA CORRENTE DO CLIENTE', 5);
SELECT setval('formapagamento_codigo_seq', 5, true);

----------------------------------------------- Condições de pagamento
INSERT INTO condicaopagamento (intervaloentreparcela, percentualvalorentrada, condicaopagamentodefault, entrada, nrparcelas, descricao, codigo) VALUES (0, 0, false, false, 1, 'A VISTA: DINHEIRO ', 1);
INSERT INTO condicaopagamento (intervaloentreparcela, percentualvalorentrada, condicaopagamentodefault, entrada, nrparcelas, descricao, codigo) VALUES (30, 0, false, true, 2, 'EM 2 VEZES', 2);
INSERT INTO condicaopagamento (intervaloentreparcela, percentualvalorentrada, condicaopagamentodefault, entrada, nrparcelas, descricao, codigo) VALUES (30, 0, false, true, 3, 'EM 3 VEZES', 3);
INSERT INTO condicaopagamento (intervaloentreparcela, percentualvalorentrada, condicaopagamentodefault, entrada, nrparcelas, descricao, codigo) VALUES (30, 0, false, true, 4, 'EM 4 VEZES', 4);
INSERT INTO condicaopagamento (intervaloentreparcela, percentualvalorentrada, condicaopagamentodefault, entrada, nrparcelas, descricao, codigo) VALUES (30, 0, false, true, 5, 'EM 5 VEZES', 5);
INSERT INTO condicaopagamento (intervaloentreparcela, percentualvalorentrada, condicaopagamentodefault, entrada, nrparcelas, descricao, codigo) VALUES (30, 0, false, true, 6, 'EM 6 VEZES', 6);
INSERT INTO condicaopagamento (intervaloentreparcela, percentualvalorentrada, condicaopagamentodefault, entrada, nrparcelas, descricao, codigo) VALUES (30, 0, false, true, 7, 'EM 7 VEZES', 7);
INSERT INTO condicaopagamento (intervaloentreparcela, percentualvalorentrada, condicaopagamentodefault, entrada, nrparcelas, descricao, codigo) VALUES (30, 0, false, true, 8, 'EM 8 VEZES', 8);
INSERT INTO condicaopagamento (intervaloentreparcela, percentualvalorentrada, condicaopagamentodefault, entrada, nrparcelas, descricao, codigo) VALUES (30, 0, false, true, 9, 'EM 9 VEZES', 9);
INSERT INTO condicaopagamento (intervaloentreparcela, percentualvalorentrada, condicaopagamentodefault, entrada, nrparcelas, descricao, codigo) VALUES (30, 0, false, true, 10, 'EM 10 VEZES', 10);
INSERT INTO condicaopagamento (intervaloentreparcela, percentualvalorentrada, condicaopagamentodefault, entrada, nrparcelas, descricao, codigo) VALUES (30, 0, false, true, 11, 'EM 11 VEZES', 11);
INSERT INTO condicaopagamento (intervaloentreparcela, percentualvalorentrada, condicaopagamentodefault, entrada, nrparcelas, descricao, codigo) VALUES (30, 0, false, true, 12, 'EM 12 VEZES', 12);
INSERT INTO condicaopagamento (intervaloentreparcela, percentualvalorentrada, condicaopagamentodefault, entrada, nrparcelas, descricao, codigo) VALUES (0, 0, false, false, 1, 'A VISTA: CHEQUE OU CARTÃO', 13);

SELECT setval('condicaopagamento_codigo_seq', 13, true);

INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (0, 1, NULL, 2, 1);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (30, 2, NULL, 2, 2);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (0, 1, NULL, 3, 3);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (30, 2, NULL, 3, 4);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (60, 3, NULL, 3, 5);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (0, 1, NULL, 4, 6);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (30, 2, NULL, 4, 7);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (60, 3, NULL, 4, 8);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (90, 4, NULL, 4, 9);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (0, 1, NULL, 5, 10);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (30, 2, NULL, 5, 11);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (60, 3, NULL, 5, 12);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (90, 4, NULL, 5, 13);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (120, 5, NULL, 5, 14);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (0, 1, NULL, 6, 15);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (30, 2, NULL, 6, 16);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (60, 3, NULL, 6, 17);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (90, 4, NULL, 6, 18);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (120, 5, NULL, 6, 19);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (150, 6, NULL, 6, 20);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (0, 1, NULL, 7, 21);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (30, 2, NULL, 7, 22);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (60, 3, NULL, 7, 23);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (90, 4, NULL, 7, 24);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (120, 5, NULL, 7, 25);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (150, 6, NULL, 7, 26);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (180, 7, NULL, 7, 27);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (0, 1, NULL, 8, 28);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (30, 2, NULL, 8, 29);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (60, 3, NULL, 8, 30);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (90, 4, NULL, 8, 31);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (120, 5, NULL, 8, 32);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (150, 6, NULL, 8, 33);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (180, 7, NULL, 8, 34);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (210, 8, NULL, 8, 35);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (0, 1, NULL, 9, 36);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (30, 2, NULL, 9, 37);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (60, 3, NULL, 9, 38);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (90, 4, NULL, 9, 39);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (120, 5, NULL, 9, 40);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (150, 6, NULL, 9, 41);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (180, 7, NULL, 9, 42);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (210, 8, NULL, 9, 43);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (240, 9, NULL, 9, 44);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (0, 1, NULL, 10, 45);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (30, 2, NULL, 10, 46);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (60, 3, NULL, 10, 47);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (90, 4, NULL, 10, 48);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (120, 5, NULL, 10, 49);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (150, 6, NULL, 10, 50);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (180, 7, NULL, 10, 51);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (210, 8, NULL, 10, 52);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (240, 9, NULL, 10, 53);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (270, 10, NULL, 10, 54);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (0, 1, NULL, 11, 55);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (30, 2, NULL, 11, 56);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (60, 3, NULL, 11, 57);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (90, 4, NULL, 11, 58);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (120, 5, NULL, 11, 59);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (150, 6, NULL, 11, 60);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (180, 7, NULL, 11, 61);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (210, 8, NULL, 11, 62);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (240, 9, NULL, 11, 63);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (270, 10, NULL, 11, 64);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (300, 11, NULL, 11, 65);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (0, 1, NULL, 12, 66);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (30, 2, NULL, 12, 67);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (60, 3, NULL, 12, 68);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (90, 4, NULL, 12, 69);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (120, 5, NULL, 12, 70);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (150, 6, NULL, 12, 71);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (180, 7, NULL, 12, 72);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (210, 8, NULL, 12, 73);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (240, 9, NULL, 12, 74);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (270, 10, NULL, 12, 75);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (300, 11, NULL, 12, 76);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (330, 12, NULL, 12, 77);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (0, 1, NULL, 13, 78);
INSERT INTO condicaopagamentoparcela (nrdiasparcela, nrparcela, percentualparcela, condicaopagamento, codigo) VALUES (0, 1, NULL, 1, 79);
SELECT setval('condicaopagamentoparcela_codigo_seq', 78, true);

----------------------------------------------Tipos de Desconto-------------------------------------------------------------------------------
INSERT INTO desconto (tipoproduto, tipodesconto, valor, descricao, codigo) VALUES ('MA', 'PE', 100, '100% NA MATRICULA', 1);
INSERT INTO desconto (tipoproduto, tipodesconto, valor, descricao, codigo) VALUES ('PM', 'PE', 10, 'DESCONTO DE PLANO', 2);
SELECT setval('desconto_codigo_seq', 1, true);

----------------------------------------------Grau de Instrução--------------------------------------------------------------------------------
INSERT INTO grauinstrucao (descricao, codigo) VALUES ('SEM ESTUDO', 1);
INSERT INTO grauinstrucao (descricao, codigo) VALUES ('ENSINO FUNDAMENTAL', 2);
INSERT INTO grauinstrucao (descricao, codigo) VALUES ('ENSINO MÉDIO', 3);
INSERT INTO grauinstrucao (descricao, codigo) VALUES ('ENSINO SUPERIOR', 4);
INSERT INTO grauinstrucao (descricao, codigo) VALUES ('PÓS-GRADUAÇÃO', 5);
INSERT INTO grauinstrucao (descricao, codigo) VALUES ('MESTRADO', 6);
INSERT INTO grauinstrucao (descricao, codigo) VALUES ('DOUTORADO', 7);
INSERT INTO grauinstrucao (descricao, codigo) VALUES ('PÓS-DOUTORADO', 8);
SELECT setval('grauinstrucao_codigo_seq', 8, true);

----------------------------------------------Convenio de Desconto------------------------------------------------------------------------------
INSERT INTO conveniodesconto (pagarematricula, pagamatricula, dataautorizacao, responsavelautorizacao, descontoparcela, datafinalvigencia, datainiciovigencia, dataassinatura, descricao, codigo) VALUES (true, true, '2020-11-02 00:00:00', 1, NULL, '2020-11-02 00:00:00', '2011-11-02 00:00:00', '2011-11-02 00:00:00', 'EMPRESA XXXX', 1);
SELECT setval('conveniodesconto_codigo_seq', 1, true);

INSERT INTO conveniodescontoconfiguracao (conveniodesconto, tipodesconto, porcentagemdesconto, valordesconto, duracao, codigo) VALUES (1, 'PD', 5, 0, 3, 1);
INSERT INTO conveniodescontoconfiguracao (conveniodesconto, tipodesconto, porcentagemdesconto, valordesconto, duracao, codigo) VALUES (1, 'PD', 10, 0, 12, 2);
SELECT setval('conveniodescontoconfiguracao_codigo_seq', 2, true);

-----------------------------------------------Justificativas de Operacao-----------------------------------------------------------------------------
INSERT INTO justificativaoperacao (empresa, tipooperacao, descricao, codigo) VALUES (1, 'BO', 'PROMOÇÃO TRAGA UM AMIGO                           ', 1);
INSERT INTO justificativaoperacao (empresa, tipooperacao, descricao, codigo) VALUES (1, 'BO', 'AUTORIZAÇÃO DIRETORIA                             ', 2);
INSERT INTO justificativaoperacao (empresa, tipooperacao, descricao, codigo) VALUES (1, 'AT', 'ATESTADO DE LESÃO                                 ', 3);
INSERT INTO justificativaoperacao (empresa, tipooperacao, descricao, codigo) VALUES (1, 'AT', 'ATESTADO DE GRIPE                                 ', 4);
INSERT INTO justificativaoperacao (empresa, tipooperacao, descricao, codigo) VALUES (1, 'AT', 'ATESTADO DE RECOMENTAÇÃO MEDICA                   ', 5);
INSERT INTO justificativaoperacao (empresa, tipooperacao, descricao, codigo) VALUES (1, 'CA', 'MUDANÇA DE CIDADE                                 ', 6);
INSERT INTO justificativaoperacao (empresa, tipooperacao, descricao, codigo) VALUES (1, 'CA', 'SEM JUSTIFICATIVA                                 ', 7);
INSERT INTO justificativaoperacao (empresa, tipooperacao, descricao, codigo) VALUES (1, 'TR', 'TRANCAMENTO POR FALTA DE TEMPO                    ', 8);
INSERT INTO justificativaoperacao (empresa, tipooperacao, descricao, codigo) VALUES (1, 'TR', 'TRANCAMENTO POR VIAGEM LONGA                      ', 9);
INSERT INTO justificativaoperacao (empresa, tipooperacao, descricao, codigo) VALUES (1, 'TR', 'TRANCAMENTO POR FÉRIAS                            ', 10);
SELECT setval('justificativaoperacao_codigo_seq', 10, true);


------------------------------------------------Grupo (agrupamento de pessoas  um pessoa pode estar em mais de um grupo - permite definição de DESCONTOS ESPECIAIS  - As pessoas  de um mesmo grupo se conhecem - NECESSARIAMENTE)----------------------------------------------------------------------------------------------------
DELETE FROM GRUPO;
INSERT INTO grupo (tipodesconto, valordescontogrupo, percentualdescontogrupo, descricao, codigo) VALUES ('PE', 0, 10, 'AMIGOS DA FAMILIA', 1);
INSERT INTO grupo (tipodesconto, valordescontogrupo, percentualdescontogrupo, descricao, codigo) VALUES ('PE', 0, 3,  'GRUPO DE CORRIDA DO JOAQUIM', 2);
INSERT INTO grupo (tipodesconto, valordescontogrupo, percentualdescontogrupo, descricao, codigo) VALUES ('PE', 0, 0,  'ESCALADA DO SÁBADO', 3);
INSERT INTO grupo (tipodesconto, valordescontogrupo, percentualdescontogrupo, descricao, codigo) VALUES ('PE', 0, 0,  'FAMÍLIA SILVA, JOÃO', 4);
SELECT setval('grupo_codigo_seq', 4, true);

------------------------------------------------Classificacao (Classifica pessoas para facilitar contatos) - as Pessoas NAO se conhecem------------------------
DELETE FROM CLASSIFICACAO;
INSERT INTO classificacao (nome, codigo) VALUES ('ATLETA', 1);
INSERT INTO classificacao (nome, codigo) VALUES ('INTERATIVO', 2);
INSERT INTO classificacao (nome, codigo) VALUES ('ALTO RISCO DE SAIR E VOLTAR', 3);
INSERT INTO classificacao (nome, codigo) VALUES ('MULHER ATIVA', 4);
INSERT INTO classificacao (nome, codigo) VALUES ('ALUNO DE PERSONAL', 5);
INSERT INTO classificacao (nome, codigo) VALUES ('ALUNO DIRETIVO', 6);
INSERT INTO classificacao (nome, codigo) VALUES ('ALUNO INFLUENTE', 7);
INSERT INTO classificacao (nome, codigo) VALUES ('ALUNO ESTÁVEL', 8);
INSERT INTO classificacao (nome, codigo) VALUES ('ALUNO CONFORMIDADE', 9);
INSERT INTO classificacao (nome, codigo) VALUES ('VIAJA MUITO', 10);
INSERT INTO classificacao (nome, codigo) VALUES ('MELHOR IDADE', 11);
INSERT INTO classificacao (nome, codigo) VALUES ('FILHO VEM COM FAMILIA', 12);
INSERT INTO classificacao (nome, codigo) VALUES ('ESTUDANTE VEM COM COLEGAS', 13);
INSERT INTO classificacao (nome, codigo) VALUES ('CLIENTE CULTO', 14);

SELECT setval('classificacao_codigo_seq', 14, true);

----------------------------------------------- Paretesco-----------------------------------------
INSERT INTO parentesco (idadelimitedependencia, descricao, codigo) VALUES (150, 'CÔNJUGE', 1);
INSERT INTO parentesco (idadelimitedependencia, descricao, codigo) VALUES (150, 'FILHO', 2);
INSERT INTO parentesco (idadelimitedependencia, descricao, codigo) VALUES (150, 'FILHA', 3);
INSERT INTO parentesco (idadelimitedependencia, descricao, codigo) VALUES (150, 'PAI', 4);
INSERT INTO parentesco (idadelimitedependencia, descricao, codigo) VALUES (150, 'MÃE', 5);
SELECT setval('parentesco_codigo_seq', 5, true);

-----------------------------------------------Profissao------------------------------------------
INSERT INTO profissao (descricao, codigo) VALUES ('RECEPCIONISTA', 1);
INSERT INTO profissao (descricao, codigo) VALUES ('CONSULTOR DE VENDAS', 2);
INSERT INTO profissao (descricao, codigo) VALUES ('PROFESSOR DE EDUCAÇÃO FÍSICA', 3);
INSERT INTO profissao (descricao, codigo) VALUES ('ADMINISTRADOR', 4);
INSERT INTO profissao (descricao, codigo) VALUES ('MÉDICO', 5);
INSERT INTO profissao (descricao, codigo) VALUES ('ADVOGADO', 6);
INSERT INTO profissao (descricao, codigo) VALUES ('VENDEDOR', 7);
SELECT setval('profissao_codigo_seq', 7, true);

----------------------------------------------- Horário--------------------------------------------------------------------
INSERT INTO horario (horariodefault, domingo, segunda, terca, quarta, quinta, sexta, sabado, livre, descricao, codigo) VALUES (false, true, true, true, true, true, true, true, false, 'LIVRE: 05:30 AS 22:00', 1);
INSERT INTO horario (horariodefault, domingo, segunda, terca, quarta, quinta, sexta, sabado, livre, descricao, codigo) VALUES (false, true, true, true, true, true, true, true, false, 'EXECUTIVO: 11:00 AS 16:00', 2);
SELECT setval('horario_codigo_seq', 2, true);

INSERT INTO horariodisponibilidade (noturno, vespertino, matutino, desenhartodos, hora2330, hora2300, hora2230, hora2200, hora2130, hora2100, hora2030, hora2000, hora1930, hora1900, hora1830, hora1800, hora1730, hora1700, hora1630, hora1600, hora1530, hora1500, hora1430, hora1400, hora1330, hora1300, hora1230, hora1200, hora1130, hora1100, hora1030, hora1000, hora0930, hora0900, hora0830, hora0800, hora0730, hora0700, hora0630, hora0600, hora0530, hora0500, hora0430, hora0400, hora0330, hora0300, hora0230, hora0200, hora0130, hora0100, hora0030, hora0000, horario, identificador, codigo) VALUES (false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, 2, '', 1);
INSERT INTO horariodisponibilidade (noturno, vespertino, matutino, desenhartodos, hora2330, hora2300, hora2230, hora2200, hora2130, hora2100, hora2030, hora2000, hora1930, hora1900, hora1830, hora1800, hora1730, hora1700, hora1630, hora1600, hora1530, hora1500, hora1430, hora1400, hora1330, hora1300, hora1230, hora1200, hora1130, hora1100, hora1030, hora1000, hora0930, hora0900, hora0830, hora0800, hora0730, hora0700, hora0630, hora0600, hora0530, hora0500, hora0430, hora0400, hora0330, hora0300, hora0230, hora0200, hora0130, hora0100, hora0030, hora0000, horario, identificador, codigo) VALUES (false, false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, 2, 'Domingo', 2);
INSERT INTO horariodisponibilidade (noturno, vespertino, matutino, desenhartodos, hora2330, hora2300, hora2230, hora2200, hora2130, hora2100, hora2030, hora2000, hora1930, hora1900, hora1830, hora1800, hora1730, hora1700, hora1630, hora1600, hora1530, hora1500, hora1430, hora1400, hora1330, hora1300, hora1230, hora1200, hora1130, hora1100, hora1030, hora1000, hora0930, hora0900, hora0830, hora0800, hora0730, hora0700, hora0630, hora0600, hora0530, hora0500, hora0430, hora0400, hora0330, hora0300, hora0230, hora0200, hora0130, hora0100, hora0030, hora0000, horario, identificador, codigo) VALUES (true, true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, true, true, true, true, true, true, true, true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, 2, 'Segunda', 3);
INSERT INTO horariodisponibilidade (noturno, vespertino, matutino, desenhartodos, hora2330, hora2300, hora2230, hora2200, hora2130, hora2100, hora2030, hora2000, hora1930, hora1900, hora1830, hora1800, hora1730, hora1700, hora1630, hora1600, hora1530, hora1500, hora1430, hora1400, hora1330, hora1300, hora1230, hora1200, hora1130, hora1100, hora1030, hora1000, hora0930, hora0900, hora0830, hora0800, hora0730, hora0700, hora0630, hora0600, hora0530, hora0500, hora0430, hora0400, hora0330, hora0300, hora0230, hora0200, hora0130, hora0100, hora0030, hora0000, horario, identificador, codigo) VALUES (true, true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, true, true, true, true, true, true, true, true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, 2, 'Terça', 4);
INSERT INTO horariodisponibilidade (noturno, vespertino, matutino, desenhartodos, hora2330, hora2300, hora2230, hora2200, hora2130, hora2100, hora2030, hora2000, hora1930, hora1900, hora1830, hora1800, hora1730, hora1700, hora1630, hora1600, hora1530, hora1500, hora1430, hora1400, hora1330, hora1300, hora1230, hora1200, hora1130, hora1100, hora1030, hora1000, hora0930, hora0900, hora0830, hora0800, hora0730, hora0700, hora0630, hora0600, hora0530, hora0500, hora0430, hora0400, hora0330, hora0300, hora0230, hora0200, hora0130, hora0100, hora0030, hora0000, horario, identificador, codigo) VALUES (true, true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, true, true, true, true, true, true, true, true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, 2, 'Quarta', 5);
INSERT INTO horariodisponibilidade (noturno, vespertino, matutino, desenhartodos, hora2330, hora2300, hora2230, hora2200, hora2130, hora2100, hora2030, hora2000, hora1930, hora1900, hora1830, hora1800, hora1730, hora1700, hora1630, hora1600, hora1530, hora1500, hora1430, hora1400, hora1330, hora1300, hora1230, hora1200, hora1130, hora1100, hora1030, hora1000, hora0930, hora0900, hora0830, hora0800, hora0730, hora0700, hora0630, hora0600, hora0530, hora0500, hora0430, hora0400, hora0330, hora0300, hora0230, hora0200, hora0130, hora0100, hora0030, hora0000, horario, identificador, codigo) VALUES (true, true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, true, true, true, true, true, true, true, true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, 2, 'Quinta', 6);
INSERT INTO horariodisponibilidade (noturno, vespertino, matutino, desenhartodos, hora2330, hora2300, hora2230, hora2200, hora2130, hora2100, hora2030, hora2000, hora1930, hora1900, hora1830, hora1800, hora1730, hora1700, hora1630, hora1600, hora1530, hora1500, hora1430, hora1400, hora1330, hora1300, hora1230, hora1200, hora1130, hora1100, hora1030, hora1000, hora0930, hora0900, hora0830, hora0800, hora0730, hora0700, hora0630, hora0600, hora0530, hora0500, hora0430, hora0400, hora0330, hora0300, hora0230, hora0200, hora0130, hora0100, hora0030, hora0000, horario, identificador, codigo) VALUES (true, true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, true, true, true, true, true, true, true, true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, 2, 'Sexta', 7);
INSERT INTO horariodisponibilidade (noturno, vespertino, matutino, desenhartodos, hora2330, hora2300, hora2230, hora2200, hora2130, hora2100, hora2030, hora2000, hora1930, hora1900, hora1830, hora1800, hora1730, hora1700, hora1630, hora1600, hora1530, hora1500, hora1430, hora1400, hora1330, hora1300, hora1230, hora1200, hora1130, hora1100, hora1030, hora1000, hora0930, hora0900, hora0830, hora0800, hora0730, hora0700, hora0630, hora0600, hora0530, hora0500, hora0430, hora0400, hora0330, hora0300, hora0230, hora0200, hora0130, hora0100, hora0030, hora0000, horario, identificador, codigo) VALUES (true, true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, false, false, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, 2, 'Sábado', 8);
INSERT INTO horariodisponibilidade (noturno, vespertino, matutino, desenhartodos, hora2330, hora2300, hora2230, hora2200, hora2130, hora2100, hora2030, hora2000, hora1930, hora1900, hora1830, hora1800, hora1730, hora1700, hora1630, hora1600, hora1530, hora1500, hora1430, hora1400, hora1330, hora1300, hora1230, hora1200, hora1130, hora1100, hora1030, hora1000, hora0930, hora0900, hora0830, hora0800, hora0730, hora0700, hora0630, hora0600, hora0530, hora0500, hora0430, hora0400, hora0330, hora0300, hora0230, hora0200, hora0130, hora0100, hora0030, hora0000, horario, identificador, codigo) VALUES (false, false, false, false, false, false, false, false, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, false, 1, '', 9);
INSERT INTO horariodisponibilidade (noturno, vespertino, matutino, desenhartodos, hora2330, hora2300, hora2230, hora2200, hora2130, hora2100, hora2030, hora2000, hora1930, hora1900, hora1830, hora1800, hora1730, hora1700, hora1630, hora1600, hora1530, hora1500, hora1430, hora1400, hora1330, hora1300, hora1230, hora1200, hora1130, hora1100, hora1030, hora1000, hora0930, hora0900, hora0830, hora0800, hora0730, hora0700, hora0630, hora0600, hora0530, hora0500, hora0430, hora0400, hora0330, hora0300, hora0230, hora0200, hora0130, hora0100, hora0030, hora0000, horario, identificador, codigo) VALUES (false, false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, 1, 'Domingo', 10);
INSERT INTO horariodisponibilidade (noturno, vespertino, matutino, desenhartodos, hora2330, hora2300, hora2230, hora2200, hora2130, hora2100, hora2030, hora2000, hora1930, hora1900, hora1830, hora1800, hora1730, hora1700, hora1630, hora1600, hora1530, hora1500, hora1430, hora1400, hora1330, hora1300, hora1230, hora1200, hora1130, hora1100, hora1030, hora1000, hora0930, hora0900, hora0830, hora0800, hora0730, hora0700, hora0630, hora0600, hora0530, hora0500, hora0430, hora0400, hora0330, hora0300, hora0230, hora0200, hora0130, hora0100, hora0030, hora0000, horario, identificador, codigo) VALUES (false, false, false, true, false, false, false, false, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, false, 1, 'Segunda', 11);
INSERT INTO horariodisponibilidade (noturno, vespertino, matutino, desenhartodos, hora2330, hora2300, hora2230, hora2200, hora2130, hora2100, hora2030, hora2000, hora1930, hora1900, hora1830, hora1800, hora1730, hora1700, hora1630, hora1600, hora1530, hora1500, hora1430, hora1400, hora1330, hora1300, hora1230, hora1200, hora1130, hora1100, hora1030, hora1000, hora0930, hora0900, hora0830, hora0800, hora0730, hora0700, hora0630, hora0600, hora0530, hora0500, hora0430, hora0400, hora0330, hora0300, hora0230, hora0200, hora0130, hora0100, hora0030, hora0000, horario, identificador, codigo) VALUES (false, false, false, true, false, false, false, false, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, false, 1, 'Terça', 12);
INSERT INTO horariodisponibilidade (noturno, vespertino, matutino, desenhartodos, hora2330, hora2300, hora2230, hora2200, hora2130, hora2100, hora2030, hora2000, hora1930, hora1900, hora1830, hora1800, hora1730, hora1700, hora1630, hora1600, hora1530, hora1500, hora1430, hora1400, hora1330, hora1300, hora1230, hora1200, hora1130, hora1100, hora1030, hora1000, hora0930, hora0900, hora0830, hora0800, hora0730, hora0700, hora0630, hora0600, hora0530, hora0500, hora0430, hora0400, hora0330, hora0300, hora0230, hora0200, hora0130, hora0100, hora0030, hora0000, horario, identificador, codigo) VALUES (false, false, false, true, false, false, false, false, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, false, 1, 'Quarta', 13);
INSERT INTO horariodisponibilidade (noturno, vespertino, matutino, desenhartodos, hora2330, hora2300, hora2230, hora2200, hora2130, hora2100, hora2030, hora2000, hora1930, hora1900, hora1830, hora1800, hora1730, hora1700, hora1630, hora1600, hora1530, hora1500, hora1430, hora1400, hora1330, hora1300, hora1230, hora1200, hora1130, hora1100, hora1030, hora1000, hora0930, hora0900, hora0830, hora0800, hora0730, hora0700, hora0630, hora0600, hora0530, hora0500, hora0430, hora0400, hora0330, hora0300, hora0230, hora0200, hora0130, hora0100, hora0030, hora0000, horario, identificador, codigo) VALUES (false, false, false, true, false, false, false, false, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, false, 1, 'Quinta', 14);
INSERT INTO horariodisponibilidade (noturno, vespertino, matutino, desenhartodos, hora2330, hora2300, hora2230, hora2200, hora2130, hora2100, hora2030, hora2000, hora1930, hora1900, hora1830, hora1800, hora1730, hora1700, hora1630, hora1600, hora1530, hora1500, hora1430, hora1400, hora1330, hora1300, hora1230, hora1200, hora1130, hora1100, hora1030, hora1000, hora0930, hora0900, hora0830, hora0800, hora0730, hora0700, hora0630, hora0600, hora0530, hora0500, hora0430, hora0400, hora0330, hora0300, hora0230, hora0200, hora0130, hora0100, hora0030, hora0000, horario, identificador, codigo) VALUES (false, false, false, true, false, false, false, false, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, false, 1, 'Sexta', 15);
INSERT INTO horariodisponibilidade (noturno, vespertino, matutino, desenhartodos, hora2330, hora2300, hora2230, hora2200, hora2130, hora2100, hora2030, hora2000, hora1930, hora1900, hora1830, hora1800, hora1730, hora1700, hora1630, hora1600, hora1530, hora1500, hora1430, hora1400, hora1330, hora1300, hora1230, hora1200, hora1130, hora1100, hora1030, hora1000, hora0930, hora0900, hora0830, hora0800, hora0730, hora0700, hora0630, hora0600, hora0530, hora0500, hora0430, hora0400, hora0330, hora0300, hora0230, hora0200, hora0130, hora0100, hora0030, hora0000, horario, identificador, codigo) VALUES (false, false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, false, 1, 'Sábado', 16);

----------------------------------------------- Pessoa - Colaborador - Cliente - Usuario------------------------------------------------------------------------------
INSERT INTO pessoa (webpage, foto, grauinstrucao, sexo, naturalidade, nacionalidade, estadocivil, pais, estado, cidade, rguf, rgorgao, rg, cfp, nomemae, nomepai, datanasc, nome, datacadastro, profissao, codigo) VALUES ('WWW.PACTOSOLUCOES.COM.BR', NULL, NULL, '', '', 'BRASILEIRA', '', null, null, null, '', '', '', '', '', '', '1987-11-02 00:00:00', 'PACTO - MÉTODO DE GESTÃO', '2010-02-26 00:00:00', 4, 1);
INSERT INTO pessoa (webpage, foto, grauinstrucao, sexo, naturalidade, nacionalidade, estadocivil, pais, estado, cidade, rguf, rgorgao, rg, cfp, nomemae, nomepai, datanasc, nome, datacadastro, profissao, codigo) VALUES ('', NULL, NULL, 'M', '', '', '', null, null, null, '', '', '', '', '', '', '1987-11-02 00:00:00', 'AA_PRIMEIRO CLIENTE', '2011-01-01', 4, 2);
INSERT INTO pessoa (webpage, foto, grauinstrucao, sexo, naturalidade, nacionalidade, estadocivil, pais, estado, cidade, rguf, rgorgao, rg, cfp, nomemae, nomepai, datanasc, nome, datacadastro, profissao, codigo) VALUES ('', NULL, NULL, '', '', '', '', null, NULL, NULL, '', '', '', '', '', '', '2011-01-01 00:00:00', 'NOME DO EMPRESARIO', '1987-11-02 00:00:00', 4, 3);
SELECT setval('pessoa_codigo_seq', 2, true);

INSERT INTO colaborador (funcionario, codacesso, codacessoalternativo, situacao, pessoa, codigo, empresa) VALUES (true, '5000010001', '', 'AT', 1, 1, 1);
INSERT INTO colaborador (funcionario, codacesso, codacessoalternativo, situacao, pessoa, codigo, empresa) VALUES (false, '5000030001', '', 'AT', 3, 2, 1);
SELECT setval('colaborador_codigo_seq', 2, true);

INSERT INTO tipocolaborador (codigo, colaborador, descricao) VALUES (1, 1, 'CO');
INSERT INTO tipocolaborador (codigo, colaborador, descricao) VALUES (2, 2, 'PR');

INSERT INTO usuario (administrador, cliente, colaborador, tipousuario, senha, username, nome) VALUES (false, NULL, 2, 'CE', '5a1673b7a73edf6f6cf7c68a0dc5dc45712a120d5765a356ff38725f07ebfd55', 'PACTOBR', 'NOME DO EMPRESARIO');
INSERT INTO usuarioperfilacesso (empresa, perfilacesso, usuario) VALUES (1, 1, 2);

------------------------------------------------Nivel da Turma--------------------------------------------------------------------------------------------------
DELETE FROM nivelturma;
INSERT INTO nivelturma (descricao, codigo) VALUES ('INICIANTE', 1);
INSERT INTO nivelturma (descricao, codigo) VALUES ('INTERMEDIÁRIO', 2);
INSERT INTO nivelturma (descricao, codigo) VALUES ('AVANÇADO', 3);
INSERT INTO nivelturma (descricao, codigo) VALUES ('SN', 4);
SELECT setval('nivelturma_codigo_seq', 4, true);
-----------------------------------------------Turma-------------------------------------------------------------------------------------------------------------
INSERT INTO turma (bloquearmatriculasacimalimite, empresa, idademaxima, idademaximameses, idademinima, idademinimameses, datafinalvigencia, datainicialvigencia, modalidade, identificador, descricao, codigo) VALUES (true, 1, 50, 0, 0, 0, '2020-02-26 00:00:00', '2010-02-26 00:00:00', 5, 'NINF', 'NATAÇÃO', 1);

INSERT INTO horarioturma (diasemananumero, nrmaximoaluno, identificadorturma, situacao, diasemana, nivelturma, ambiente, professor, horafinal, horainicial, turma, codigo) VALUES (2, 20, 'NINF', 'AT', 'SG', 4, 2, 1, '08:50', '08:00', 1, 1);
INSERT INTO horarioturma (diasemananumero, nrmaximoaluno, identificadorturma, situacao, diasemana, nivelturma, ambiente, professor, horafinal, horainicial, turma, codigo) VALUES (4, 20, 'NINF', 'AT', 'QA', 4, 2, 1, '08:50', '08:00', 1, 2);
INSERT INTO horarioturma (diasemananumero, nrmaximoaluno, identificadorturma, situacao, diasemana, nivelturma, ambiente, professor, horafinal, horainicial, turma, codigo) VALUES (6, 20, 'NINF', 'AT', 'SX', 4, 2, 1, '08:50', '08:00', 1, 3);
INSERT INTO horarioturma (diasemananumero, nrmaximoaluno, identificadorturma, situacao, diasemana, nivelturma, ambiente, professor, horafinal, horainicial, turma, codigo) VALUES (3, 20, 'NINF', 'AT', 'TR', 4, 2, 1, '18:00', '14:00', 1, 4);
INSERT INTO horarioturma (diasemananumero, nrmaximoaluno, identificadorturma, situacao, diasemana, nivelturma, ambiente, professor, horafinal, horainicial, turma, codigo) VALUES (5, 20, 'NINF', 'AT', 'QI', 4, 2, 1, '18:00', '14:00', 1, 5);
INSERT INTO horarioturma (diasemananumero, nrmaximoaluno, identificadorturma, situacao, diasemana, nivelturma, ambiente, professor, horafinal, horainicial, turma, codigo) VALUES (2, 20, 'NINF', 'AT', 'SG', 4, 2, 1, '21:00', '18:00', 1, 6);
INSERT INTO horarioturma (diasemananumero, nrmaximoaluno, identificadorturma, situacao, diasemana, nivelturma, ambiente, professor, horafinal, horainicial, turma, codigo) VALUES (4, 20, 'NINF', 'AT', 'QA', 4, 2, 1, '21:00', '18:00', 1, 7);
INSERT INTO horarioturma (diasemananumero, nrmaximoaluno, identificadorturma, situacao, diasemana, nivelturma, ambiente, professor, horafinal, horainicial, turma, codigo) VALUES (6, 20, 'NINF', 'AT', 'SX', 4, 2, 1, '21:00', '18:00', 1, 8);
SELECT setval('horarioturma_codigo_seq', 8, true);


---------  Plano textopadrao  --------------------------------
INSERT INTO planotextopadrao (texto, situacao, responsaveldefinicao, datadefinicao, descricao, imagemlogo, codigo) VALUES ('<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>Untitled document</title>
</head>
<body>
<p class="MsoNormal" style="MARGIN: 0cm 0cm 0pt"><span style="font-family: Calibri; font-size: small;">
<table class="MsoNormalTable" style="BORDER-COLLAPSE: collapse; mso-table-layout-alt: fixed; mso-yfti-tbllook: 1184; mso-padding-alt: 0cm 0cm 0cm 0cm" border="0" cellspacing="0" cellpadding="0" width="966">
<tbody>
<tr style="mso-yfti-irow: 0; mso-yfti-firstrow: yes">
<td style="background-color: transparent; width: 354.4pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="473" valign="top">
<p class="MsoNormal" style="TEXT-INDENT: 0.9pt; MARGIN: 0cm 4pt 0pt 3.1pt; mso-margin-top-alt: auto"><strong><span style="text-decoration: underline;"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 12pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">Academia - Vida Ativa</span></span></strong></p>
</td>
<td style="background-color: transparent; width: 22.85pt; border: #f0f0f0; padding: 0cm;" width="30" valign="bottom">
<p class="MsoNormal" style="MARGIN: 4pt"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 5.5pt; mso-fareast-font-family: ''Times New Roman''; mso-fareast-language: PT-BR">&nbsp;</span></p>
</td>
<td style="background-color: transparent; width: 347.25pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="463" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><strong><span style="text-decoration: underline;"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 12pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">Academia - Vida Ativa</span></span></strong></p>
</td>
</tr>
<tr style="height: 15pt; mso-yfti-irow: 1;">
<td style="background-color: transparent; width: 354.4pt; height: 15pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="473" valign="top">
<p class="MsoNormal" style="TEXT-ALIGN: right; MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto" align="right"><strong><span style="text-decoration: underline;"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">RECIBO N&ordm; [(20){}codigo_Recibo]</span></span></strong></p>
</td>
<td style="background-color: transparent; width: 22.85pt; height: 15pt; border: #f0f0f0; padding: 0cm;" width="30" valign="bottom">
<p class="MsoNormal" style="MARGIN: 4pt"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 5.5pt; mso-fareast-font-family: ''Times New Roman''; mso-fareast-language: PT-BR">&nbsp;</span></p>
</td>
<td style="background-color: transparent; width: 347.25pt; height: 15pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="463" valign="top">
<p class="MsoNormal" style="TEXT-ALIGN: right; MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto" align="right"><strong><span style="text-decoration: underline;"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">RECIBO N&ordm; [(20){}codigo_Recibo]</span></span></strong></p>
</td>
</tr>
<tr style="mso-yfti-irow: 2">
<td style="background-color: transparent; width: 354.4pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="473" valign="top">
<p class="MsoNormal" style="TEXT-ALIGN: right; MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto" align="right"><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">R$:</span></strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR"> </span><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">[(10){}valorTotal_Recibo]</span></strong></p>
</td>
<td style="background-color: transparent; width: 22.85pt; border: #f0f0f0; padding: 0cm;" width="30" valign="bottom">
<p class="MsoNormal" style="MARGIN: 4pt"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 5.5pt; mso-fareast-font-family: ''Times New Roman''; mso-fareast-language: PT-BR">&nbsp;</span></p>
</td>
<td style="background-color: transparent; width: 347.25pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="463" valign="top">
<p class="MsoNormal" style="TEXT-ALIGN: right; MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto" align="right"><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">R$:</span></strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR"> </span><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">[(10){}valorTotal_Recibo]</span></strong></p>
</td>
</tr>
<tr style="height: 12.75pt; mso-yfti-irow: 3;">
<td style="background-color: transparent; width: 354.4pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="473" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">Matricula...: </span><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">[(20){}Matricula_Cliente]</span></strong></p>
</td>
<td style="background-color: transparent; width: 22.85pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" width="30" valign="bottom">
<p class="MsoNormal" style="MARGIN: 4pt"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 5.5pt; mso-fareast-font-family: ''Times New Roman''; mso-fareast-language: PT-BR">&nbsp;</span></p>
</td>
<td style="background-color: transparent; width: 347.25pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="463" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">Matricula...: </span><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">[(20){}Matricula_Cliente]</span></strong></p>
</td>
</tr>
<tr style="height: 12.75pt; mso-yfti-irow: 4;">
<td style="background-color: transparent; width: 354.4pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="473" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">Recebemos de:</span><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt"> [(60){}pessoaPagador_Recibo]</span></strong></p>
</td>
<td style="background-color: transparent; width: 22.85pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" width="30" valign="bottom">
<p class="MsoNormal" style="MARGIN: 4pt"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 5.5pt; mso-fareast-font-family: ''Times New Roman''; mso-fareast-language: PT-BR">&nbsp;</span></p>
</td>
<td style="background-color: transparent; width: 347.25pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="463" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">Recebemos de:</span><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt"> [(60){}pessoaPagador_Recibo]</span></strong></p>
</td>
</tr>
<tr style="height: 12.75pt; mso-yfti-irow: 5;">
<td style="background-color: transparent; width: 354.4pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="473" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">A quantia de: </span><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">[(200){}valorPorExtenso_Recibo]</span></strong></p>
</td>
<td style="background-color: transparent; width: 22.85pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" width="30" valign="bottom">
<p class="MsoNormal" style="MARGIN: 4pt"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 5.5pt; mso-fareast-font-family: ''Times New Roman''; mso-fareast-language: PT-BR">&nbsp;</span></p>
</td>
<td style="background-color: transparent; width: 347.25pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="463" valign="top">
<p class="MsoNormal" style="TEXT-INDENT: 1.35pt; MARGIN: 0cm 4pt 0pt 2.65pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">A quantia de: </span><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">[(200){}valorPorExtenso_Recibo]</span></strong></p>
</td>
</tr>
<tr style="height: 12.75pt; mso-yfti-irow: 6;">
<td style="background-color: transparent; width: 354.4pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="473" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">Contrato: n&ordm; [(10){}contrato_Recibo] &nbsp; Modalidades: [(10){}NomeModalidades_Contrato]</span></strong></p>
</td>
<td style="background-color: transparent; width: 22.85pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" width="30" valign="bottom">
<p class="MsoNormal" style="MARGIN: 4pt"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 5.5pt; mso-fareast-font-family: ''Times New Roman''; mso-fareast-language: PT-BR">&nbsp;</span></p>
</td>
<td style="background-color: transparent; width: 347.25pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="463" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">Contrato: n&ordm; [(10){}contrato_Recibo] &nbsp; Modalidades:&nbsp;[(10){}NomeModalidades_Contrato]</span></strong></p>
</td>
</tr>
<tr style="height: 12.75pt; mso-yfti-irow: 7;">
<td style="background-color: transparent; width: 354.4pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="473" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">PLANO: [(50){}Descricao_Plano]<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>Dura&ccedil;&atilde;o: [(10){}Duracao_Contrato] Mes(es)</span></strong></p>
</td>
<td style="background-color: transparent; width: 22.85pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" width="30" valign="bottom">
<p class="MsoNormal" style="MARGIN: 4pt"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 5.5pt; mso-fareast-font-family: ''Times New Roman''; mso-fareast-language: PT-BR">&nbsp;</span></p>
</td>
<td style="background-color: transparent; width: 347.25pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="463" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">PLANO: [(50){}Descricao_Plano]<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>Dura&ccedil;&atilde;o: [(10){}Duracao_Contrato] Mes(es)</span></strong></p>
</td>
</tr>
<tr style="height: 12.75pt; mso-yfti-irow: 8;">
<td style="background-color: transparent; width: 354.4pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="473" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt"><span style="mso-spacerun: yes"><strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</strong>&nbsp; </span>Inicio:<strong><span style="font-size: small;"> [(20){}VigenciaDe_Contrato]</span></strong> T&eacute;rmino:<strong><span style="font-size: small;"> [(20){}VigenciaAte_Contrato]</span></strong></span></p>
</td>
<td style="background-color: transparent; width: 22.85pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" width="30" valign="bottom">
<p class="MsoNormal" style="MARGIN: 4pt"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 5.5pt; mso-fareast-font-family: ''Times New Roman''; mso-fareast-language: PT-BR">&nbsp;</span></p>
</td>
<td style="background-color: transparent; width: 347.25pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="463" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt"><span style="mso-spacerun: yes"><strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</strong>&nbsp; Inicio:<span style="font-size: small;"><strong> [(20){}VigenciaDe_Contrato]</strong></span> T&eacute;rmino:<span style="font-size: small;"><strong> [(20){}VigenciaAte_Contrato]</strong></span></span></span></p>
</td>
</tr>
<tr style="height: 11.2pt; mso-yfti-irow: 9;">
<td style="background-color: transparent; width: 354.4pt; height: 11.2pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="473" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto; mso-line-height-alt: .5pt"><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">Proveniente de:</span></strong></p>
</td>
<td style="background-color: transparent; width: 22.85pt; height: 11.2pt; border: #f0f0f0; padding: 0cm;" width="30" valign="bottom">
<p class="MsoNormal" style="MARGIN: 4pt; mso-line-height-alt: .5pt"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 5.5pt; mso-fareast-font-family: ''Times New Roman''; mso-fareast-language: PT-BR">&nbsp;</span></p>
</td>
<td style="background-color: transparent; width: 347.25pt; height: 11.2pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="463" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto; mso-line-height-alt: .5pt"><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">Proveniente de:</span></strong></p>
</td>
</tr>
<tr style="height: 9.1pt; mso-yfti-irow: 10;">
<td style="width: 354.4pt; background: #d9d9d9; height: 9.1pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="473" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 0cm 0pt"><em><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">-<span style="mso-spacerun: yes">&nbsp; </span>Descri&ccedil;&atilde;o &ndash; Venc. &ndash; Valor</span></em></p>
</td>
<td style="background-color: transparent; width: 22.85pt; height: 9.1pt; border: #f0f0f0; padding: 0cm;" width="30" valign="bottom">
<p class="MsoNormal" style="MARGIN: 0cm 0cm 0pt"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 5.5pt; mso-fareast-font-family: ''Times New Roman''; mso-fareast-language: PT-BR">&nbsp;</span></p>
</td>
<td style="width: 347.25pt; background: #d9d9d9; height: 9.1pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="463" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 0cm 0pt"><em><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">-<span style="mso-spacerun: yes">&nbsp; </span>Descri&ccedil;&atilde;o &ndash; Venc. &ndash; Valor</span></em></p>
</td>
</tr>
<tr style="height: 24.75pt; mso-yfti-irow: 11;">
<td style="background-color: transparent; width: 354.4pt; height: 24.75pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="473" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">[Descri&ccedil;&atilde;o: (60){- }Descricao_MovParcela, Data vencimento: (20){ - }DataVencimento_MovParcela, Valor: (15){ - }ValorParcela_MovParcela]</span></p>
</td>
<td style="background-color: transparent; width: 22.85pt; height: 24.75pt; border: #f0f0f0; padding: 0cm;" width="30" valign="bottom">
<p class="MsoNormal" style="MARGIN: 4pt"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 5.5pt; mso-fareast-font-family: ''Times New Roman''; mso-fareast-language: PT-BR">&nbsp;</span></p>
</td>
<td style="background-color: transparent; width: 347.25pt; height: 24.75pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="463" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">[Descri&ccedil;&atilde;o: (60){- }Descricao_MovParcela, Data vencimento: (20){ - }DataVencimento_MovParcela, Valor: (15){ - }ValorParcela_MovParcela]</span></p>
</td>
</tr>
<tr style="height: 12.75pt; mso-yfti-irow: 12;">
<td style="background-color: transparent; width: 354.4pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="473" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">Da seguinte forma:</span></strong></p>
</td>
<td style="background-color: transparent; width: 22.85pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" width="30" valign="bottom">
<p class="MsoNormal" style="MARGIN: 4pt"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 5.5pt; mso-fareast-font-family: ''Times New Roman''; mso-fareast-language: PT-BR">&nbsp;</span></p>
</td>
<td style="background-color: transparent; width: 347.25pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="463" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">Da seguinte forma:</span></strong></p>
</td>
</tr>
<tr style="height: 33.75pt; mso-yfti-irow: 13;">
<td style="background-color: transparent; width: 354.4pt; height: 33.75pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="473" valign="top"><span style="font-size: x-small;"><span style="font-size: xx-small;"><span style="font-size: x-small;"><span style="font-size: small;"><span style="font-size: xx-small;"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 8pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 9.0pt">
<p class="MsoNormal" style="TEXT-ALIGN: right; MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto" align="right"><span style="font-family: Arial, sans-serif; "><span style="font-size: xx-small;">[(60){- }tipoFormaPagamento_MovPagamento, (60){:<span style="mso-spacerun: yes">&nbsp;&nbsp;</span>}R$ valor_MovPagamento, (5){<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>Banco:<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>}banco_Cheque, (20){<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>Agencia:<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;&nbsp;</span>}agencia_Cheque, (20){<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>N.:<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;&nbsp;</span>}numero_Cheque, (20){<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>CC:<span style="mso-spacerun: yes">&nbsp;&nbsp;</span>}contaCorrente_Cheque,(20){&nbsp;<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>Vlr.:<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>}valor_Cheque]</span></span></p>
</span></span></span></span></span></span>
<p class="MsoNormal" style="TEXT-ALIGN: right; MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto" align="right"><span style="font-size: xx-small;">&nbsp;</span></p>
</td>
<td style="background-color: transparent; width: 22.85pt; height: 33.75pt; border: #f0f0f0; padding: 0cm;" width="30" valign="bottom">
<p class="MsoNormal" style="MARGIN: 4pt"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 5.5pt; mso-fareast-font-family: ''Times New Roman''; mso-fareast-language: PT-BR">&nbsp;</span></p>
</td>
<td style="background-color: transparent; width: 347.25pt; height: 33.75pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="463" valign="top">
<p class="MsoNormal" style="TEXT-ALIGN: right; MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto" align="right"><span style="font-family: Arial, sans-serif; "><span style="font-size: xx-small;">[(60){- }tipoFormaPagamento_MovPagamento, (60){:<span style="mso-spacerun: yes">&nbsp;&nbsp;</span>}R$ valor_MovPagamento, (5){<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>Banco:<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>}banco_Cheque, (20){<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>Agencia:<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;&nbsp;</span>}agencia_Cheque, (20){<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>N.:<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;&nbsp;</span>}numero_Cheque, (20){<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>CC:<span style="mso-spacerun: yes">&nbsp;&nbsp;</span>}contaCorrente_Cheque,(20){&nbsp;<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>Vlr.:<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>}valor_Cheque]</span></span></p>
</td>
</tr>
<tr style="height: 12.75pt; mso-yfti-irow: 14;">
<td style="background-color: transparent; width: 156.25pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" width="208" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">----------------------------------</span></p>
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">Resp.: </span><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">[(20){}responsavelLancamento_Recibo] </span></strong></p>
</td>
<td style="background-color: transparent; width: 1cm; height: 12.75pt; border: #f0f0f0; padding: 0cm;" width="38" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">&nbsp;</span></p>
</td>
<td style="background-color: transparent; width: 169.8pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" width="226" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">-------------------------------------------</span></p>
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">Cliente:</span><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt"> [(60){}pessoaPagador_Recibo]</span></strong></p>
</td>
<td style="background-color: transparent; width: 22.85pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" width="30" valign="bottom">
<p class="MsoNormal" style="MARGIN: 4pt"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 5.5pt; mso-fareast-font-family: ''Times New Roman''; mso-fareast-language: PT-BR">&nbsp;</span></p>
</td>
<td style="background-color: transparent; width: 170.35pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" width="227" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">--------------------------------------------</span></p>
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">Resp.: </span><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">[(20){}responsavelLancamento_Recibo] </span></strong></p>
</td>
<td style="background-color: transparent; width: 21.3pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" width="28" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">&nbsp;</span></p>
</td>
<td style="background-color: transparent; width: 155.6pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" width="207" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">----------------------------------------------Cliente:</span><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt"> [(60){}pessoaPagador_Recibo]</span></strong></p>
</td>
</tr>
<tr style="height: 12.75pt; mso-yfti-irow: 15;">
<td style="background-color: transparent; width: 354.4pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="473" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">Data pagamento: </span><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">[(20){}data_Recibo]</span></strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR"> </span></p>
</td>
<td style="background-color: transparent; width: 22.85pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" width="30" valign="bottom">
<p class="MsoNormal" style="MARGIN: 4pt"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 5.5pt; mso-fareast-font-family: ''Times New Roman''; mso-fareast-language: PT-BR">&nbsp;</span></p>
</td>
<td style="background-color: transparent; width: 347.25pt; height: 12.75pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="463" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">Data pagamento: </span><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">[(20){}data_Recibo]</span></strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR"> </span></p>
</td>
</tr>
<tr style="height: 0.5pt; mso-yfti-irow: 16; mso-yfti-lastrow: yes;">
<td style="background-color: transparent; width: 354.4pt; height: 0.5pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="473" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto; mso-line-height-alt: .5pt"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">Data Impress&atilde;o :</span><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt"> </span></strong><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">[(20){}dataImpressao_Recibo]</span></strong></p>
</td>
<td style="background-color: transparent; width: 22.85pt; height: 0.5pt; border: #f0f0f0; padding: 0cm;" width="30" valign="bottom">
<p class="MsoNormal" style="MARGIN: 4pt; mso-line-height-alt: .5pt"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 5.5pt; mso-fareast-font-family: ''Times New Roman''; mso-fareast-language: PT-BR">&nbsp;</span></p>
</td>
<td style="background-color: transparent; width: 347.25pt; height: 0.5pt; border: #f0f0f0; padding: 0cm;" colspan="3" width="463" valign="top">
<p class="MsoNormal" style="MARGIN: 0cm 4pt 0pt; mso-margin-top-alt: auto; mso-line-height-alt: .5pt"><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR">Data Impress&atilde;o : </span><strong><span style="FONT-FAMILY: ''Arial'',''sans-serif''; COLOR: black; FONT-SIZE: 9pt; mso-fareast-font-family: Arial; mso-fareast-language: PT-BR; mso-bidi-font-size: 11.0pt">[(20){}dataImpressao_Recibo]</span></strong></p>
</td>
</tr>
</tbody>
</table>
</span></p>
</body>
</html>', 'AT', 1, '2010-03-02 00:00:00', 'RECIBO', NULL, 2);
INSERT INTO planotextopadrao (texto, situacao, responsaveldefinicao, datadefinicao, descricao, imagemlogo, codigo) VALUES ('<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>Untitled document</title>
</head>
<body>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: right;"><img id="logo" style="border: 0px initial initial;" src="fotos/logoContrato.jpg?id=2193" alt="" width="100" height="57" /></p>
<p class="MsoNormal" style="TEXT-ALIGN: center; MARGIN: 0cm 0cm 0pt; mso-layout-grid-align: none" align="center">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal; text-align: center;"><strong><span style="color: #000000;"><span style="text-decoration: underline;">CONTRATO DE PRESTA&Ccedil;&Atilde;O DE SERVI&Ccedil;OS</span></span></strong></p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;"><strong><span style="color: #000000;"><span style="text-decoration: underline;"><br /></span></span></strong></p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">[(10){N&deg; matr&iacute;cula:}Matricula_Cliente] Modalidade:______________ [(10){Dura&ccedil;&atilde;o meses:}Duracao_Contrato] [(20){Hor&aacute;rios:}]</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">[(50){Nome do aluno:}] &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left;"><span style="font-size: 27px; font-weight: normal; font-family: Arial, Verdana, sans-serif;"><span style="font-size: 11px;">[(50){</span></span>Respons&aacute;vel<span style="font-size: 27px; font-weight: normal; font-family: Arial, Verdana, sans-serif;"><span style="font-size: 11px;">::}]</span></span></p>
<p>&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">[(20){RG:}Rg_Cliente]&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">[(40){Endere&ccedil;o:}Endereco_Cliente] - [(10){CEP:}CEP_Cliente]<br />[(50){Cidade:}Cidade_Empresa]</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;[(10){Telefone:}Telefone_Cliente]</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">Ades&atilde;o R$____________ Plano: [(50){}Descricao_Plano]______________ Forma de pgto:______________________</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">CONTRATADA: &nbsp;[(50){}RazaoSocial_Empresa] , CNPJ: &nbsp;[(18){}Cnpj_Empresa] , com sede &agrave; &nbsp;[(50){}Endereco_Empresa] &nbsp;[(50){ - &nbsp;}Complemento_Empresa] - &nbsp;[(50){}Cidade_Empresa] - [(50){}Estado_Empresa] [(5){ - N. &nbsp;}Numero_Empresa] &nbsp;[(10){ - CEP: }Cep_Empresa] (ACADEMIA) neste ato representada por seu diretor ou funcion&aacute;rio abaixo assinado.</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">Os contratantes t&ecirc;m pactuado o que estipula as cl&aacute;usulas seguintes:</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">PRIMEIRA: O objeto deste contrato &eacute; a presta&ccedil;&atilde;o de servi&ccedil;os especializados, na &aacute;rea de prepara&ccedil;&atilde;o f&iacute;sica, pela CONTRATADA ao(&agrave;) CONTRATANTE, na modalidade XX, em suas depend&ecirc;ncias anteriormente descritas, de acordo com as condi&ccedil;&otilde;es estabelecidas no regulamento da CONTRATADA, que integra este contrato e que o(a) CONTRATANTE declara ter lido e anu&iacute;do.</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">SEGUNDA: S&atilde;o obriga&ccedil;&otilde;es do CONTRATANTE:</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">I) Submeter-se previamente &agrave; avalia&ccedil;&atilde;o f&iacute;sica e &agrave; prescri&ccedil;&atilde;o de s&eacute;rie dos exerc&iacute;cios;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">II) Obedecer rigorosamente &agrave;s orient&ccedil;&otilde;es t&eacute;cnicas recebidas quando da avalia&ccedil;&atilde;o f&iacute;sica e da prescri&ccedil;&atilde;o de exerc&iacute;cios;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">III) Freq&uuml;entar as aulas para as quais estiver inscrito(a), nos hor&aacute;rios estabelecidos pela CONTRATADA;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">IV) Usar roupas adequadas para a pr&aacute;tica de atividades f&iacute;sicas, sendo proibido o uso de sapatos, chinelos, sand&aacute;lias, short ou cal&ccedil;a jeans, bem como o comparecimento &agrave;s salas de aula de p&eacute;s descal&ccedil;os e sem camisa;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">V)Obedecer rigorosamente o regulamento da CONTRATADA mencionado na Cl&aacute;usula 1&ordf;;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">VI) Manter conduta ilibada e compat&iacute;vel com a moral e os bons costumes, nas depend&ecirc;ncias da CONTRATADA;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">VII) Zelar pelo bom uso e conserva&ccedil;&atilde;o dos equipamentos e instala&ccedil;&otilde;es da CONTRATADA, responsabilizando-se pela indeniza&ccedil;&atilde;o de qualquer dano a que der causa, exceto se decorrente do uso natural.</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">TERCEIRA: O presente contrato ter&aacute; a dura&ccedil;&atilde;o de [(10){}Duracao_Contrato] meses, tendo seu periodo de vigendia de [(20){}VigenciaDe_Contrato] at&eacute; [(20){}VigenciaAte_Contrato].</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">Modalidades:</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">[(50){ - &nbsp;}Nome_Modalidade]</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">Na(s) turma(s) de:</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">[(20){}Identificador_Turma, (20){Turma de:} Descricao_Turma]</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">QUARTA: Pelos servi&ccedil;os descritos na cl&aacute;usula 1&ordf;, o(a) CONTRATANTE pagar&aacute; a import&acirc;ncia total de R$ [(15){}ValorFinal_Contrato] da seguinte forma:</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">[(60){- }Descricao_MovParcela,(20){ - }DataVencimento_MovParcela,(15){ - &nbsp;}ValorParcela_MovParcela]</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">QUINTA: O plano dever&aacute; ser utilizado de modo ininterrupto, n&atilde;o cabendo, portanto, o trancamento durante a vig&ecirc;ncia do contrato.</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&sect; 1&ordm; - Caso o(a) CONTRATANTE necessite ausentar-se da Academia por motivo de for&ccedil;a maior, inclusive por quest&otilde;es de sa&uacute;de, poder&aacute; utilizar a car&ecirc;ncia ininterrupta de at&eacute; 15 (quinze) dias no plano trimestral, 30 (trinta) dias no plano semestral e 45 (quarenta e cinco) dias no plano anual, desde que comunique a circunstancia &agrave; CONTRATADA com no m&iacute;nimo 24 (vinte e quatro) horas de anteced&ecirc;ncia. Os dias equivalentes ao per&iacute;odo de aus&ecirc;ncia ser&atilde;o acrescidos ao final do contrato.</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">OBS.: as aus&ecirc;ncias por problemas de sa&uacute;de, mesmo comprovadas por atestado m&eacute;dico, dever&atilde;o enquadrar-se no per&iacute;odo de car&ecirc;ncia previsto neste contrato.</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&sect; 2&ordm; - N&atilde;o ser&aacute; admitida a transfer&ecirc;ncia de servi&ccedil;os referentes a valores pagos por per&iacute;odos futuros ou de um aluno para o outro, a n&atilde;o ser que perten&ccedil;&atilde;o &agrave; mesma fam&iacute;lia;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&sect; 3&ordm; - N&atilde;o ser&aacute; permitida a utiliza&ccedil;&atilde;o de car&ecirc;ncia caso o(a) CONTRATANTE deixe de comunicar a interrup&ccedil;&atilde;o do per&iacute;odo &agrave; CONTRATADA com a anteced&ecirc;ncia prevista no par&aacute;grafo 1&ordm;. Os dias de car&ecirc;ncia ficar&atilde;o automaticamente reduzidos se a sua utiliza&ccedil;&atilde;o ultrapassar o per&iacute;odo que faltar para o encerramento do contrato.</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">SEXTA: A responsabilidade da CONTRATADA com rela&ccedil;&atilde;o &agrave; seguran&ccedil;a de alunos menores de idade limita-se ao tempo de dura&ccedil;&atilde;o das aulas, enquanto estiverem aos cuidados dos professores t&atilde;o somente nas depend&ecirc;ncias da academia. &Eacute; atribui&ccedil;&atilde;o dos pais, respons&aacute;veis ou acompanhantes dos menores zelarem pela sua seguran&ccedil;a antes e depois das aulas, bem como no trajeto resid&ecirc;ncia/academia/resid&ecirc;ncia. No ato da matr&iacute;cula, os respons&aacute;veis firmar&atilde;o termo de compromisso neste sentido.</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">S&Eacute;TIMA: &Agrave; CONTRATADA reserva-se o direito de encerrar ou remanejar turmas, mediante pr&eacute;vio aviso aos interessados. Nesse caso, ser&atilde;o oferecidos ao(&agrave;) CONTRATANTE outras op&ccedil;&otilde;es de turmas e hor&aacute;rios ou, na hip&oacute;tese de n&atilde;o aceita&ccedil;&atilde;o, o ressarcimento do valor restante do plano.</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">OITAVA: O presente contrato poder&aacute; ser rescindido a pedido do(a) CONTRATANTE (POR ESCRITO), a qualquer tempo, desde que o contrato esteja em plena vig&ecirc;ncia e sem situa&ccedil;&atilde;o de regularidade, observadas as seguintes condi&ccedil;&otilde;es:</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">I) ao solicitar a rescis&atilde;o contratual o CONTRATANTE ter&aacute; seu plano convertido para o plano mensal, perdendo o direito a todo e qualquer desconto ou benef&iacute;cio relativo ao plano anteriormente escolhido (per&iacute;odo de car&ecirc;ncia, brindes, descontos, etc);</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">II) o montante a ser devolvido ao(&agrave;) CONTRATANTE, referente &agrave; parte remanescente do contrato, ser&aacute; calculado tomando-se por base o valor do plano mensal constante da tabela de pre&ccedil;os praticados pela Academia, vigente na data do pedido de rescis&atilde;o;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">III) Considerando que o(a) CONTRATANTE deixar&aacute; de cumprir, em sua integralidade, o contrato assinado, ser&aacute; cobrada uma multa rescis&oacute;ria equivalente &agrave; 20% (vinte porcento) sobre o valor residual do plano escolhido, independente das raz&otilde;es que embasaram o pedido de rescis&atilde;o;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">IV) para fins de devolu&ccedil;&atilde;o de valores, o per&iacute;odo a ser restitu&iacute;do ser&aacute; contado proporcionalmente a partir da data da solicita&ccedil;&atilde;o da rescis&atilde;o pelo(a) CONTRATANTE;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">V) os valores de Matr&iacute;cula e Avalia&ccedil;&atilde;o F&iacute;sica ou Fisioter&aacute;pica pagos pelo(a) CONTRATANTE n&atilde;o ser&atilde;o restitu&iacute;dos, por se tratar de remunera&ccedil;&atilde;o de servi&ccedil;os j&aacute; prestados;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">VI) os prazos de car&ecirc;ncia previstos no par&aacute;grafo 1&ordm; da cl&aacute;usula 5&ordf; eventualmente n&atilde;o utilizados, n&atilde;o poder&atilde;o ser compensados no momento da rescis&atilde;o;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">VII) eventuais valores a que o(a) CONTRATANTE tenha direito, ser&atilde;o devolvidos no prazo de at&eacute; 90 (noventa) dias a contar da data do pedido da rescis&atilde;o.</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">NONA: &Agrave; CONTRATADA &eacute; reservado o direito de rescis&atilde;o unilateral do presente contrato de presta&ccedil;&atilde;o de servi&ccedil;os, caso sejam descumpridas quaisquer cl&aacute;usulas contratuais pelo(a) CONTRATANTE, sem preju&iacute;zo da aplica&ccedil;&atilde;o de eventuais san&ccedil;&otilde;es, inclusive pecuni&aacute;rias, decorrentes de danos causados &agrave; CONTRATADA.</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">D&Eacute;CIMA: As quest&otilde;es omissas ser&atilde;o resolvidas de forma amistosa e preferencialmente sob consenso do(a) CONTRATANTE e da Administra&ccedil;&atilde;o da CONTRATADA.</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">D&Eacute;CIMA PRIMEIRA: Fica eleito o foro da comarca de Goi&acirc;nia-GO para dirimir qualquer demanda acerca deste contrato.</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;E por estarem de comum acordo, firmam o presente contrato em duas vias de igual teor e forma, com as testemunhas abaixo assinadas, para que se produzam os efeitos legais e jur&iacute;dicos.</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">[(50){}Cidade_Empresa] - [(50){}Estado_Empresa], ___/___/____.</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">___________________________</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">[(50){CONTRATANTE:&nbsp;}Nome_Cliente]</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">&nbsp;</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">___________________________</p>
<p class="MsoNormal" style="margin-top: 0cm; margin-right: 0cm; margin-bottom: 0pt; margin-left: 0cm; text-align: left; font-family: Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal;">[(50){}&nbsp;Respons&aacute;vel] - [(50){}Nome_Empresa]</p>
<p>&nbsp;</p>
</body>
</html>', 'AT', 1, '2010-02-26 00:00:00', 'CONTRATO 2011',null,1);

SELECT setval('planotextopadrao_codigo_seq', 2, true);

---------------- Tags de data texto padrao -----------------------------------------------------------------------------------------------------------------

INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(20){}codigo_Recibo]', 2, 1);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(20){}codigo_Recibo]', 2, 2);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(10){}valorTotal_Recibo]', 2, 3);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(10){}valorTotal_Recibo]', 2, 4);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(20){}Matricula_Cliente]', 2, 5);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(20){}Matricula_Cliente]', 2, 6);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(60){}pessoaPagador_Recibo]', 2, 7);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(60){}pessoaPagador_Recibo]', 2, 8);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(200){}valorPorExtenso_Recibo]', 2, 9);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(200){}valorPorExtenso_Recibo]', 2, 10);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(10){}contrato_Recibo]', 2, 11);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(10){}NomeModalidades_Contrato]', 2, 12);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(10){}contrato_Recibo]', 2, 13);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(10){}NomeModalidades_Contrato]', 2, 14);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(50){}Descricao_Plano]', 2, 15);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(10){}Duracao_Contrato]', 2, 16);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(50){}Descricao_Plano]', 2, 17);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(10){}Duracao_Contrato]', 2, 18);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(20){}VigenciaDe_Contrato]', 2, 19);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(20){}VigenciaAte_Contrato]', 2, 20);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(20){}VigenciaDe_Contrato]', 2, 21);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(20){}VigenciaAte_Contrato]', 2, 22);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[Descri&ccedil;&atilde;o: (60){- }Descricao_MovParcela, Data vencimento: (20){ - }DataVencimento_MovParcela, Valor: (15){ - }ValorParcela_MovParcela]', 2, 23);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[Descri&ccedil;&atilde;o: (60){- }Descricao_MovParcela, Data vencimento: (20){ - }DataVencimento_MovParcela, Valor: (15){ - }ValorParcela_MovParcela]', 2, 24);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(60){- }tipoFormaPagamento_MovPagamento, (60){:<span style="mso-spacerun: yes">&nbsp;&nbsp;</span>}R$ valor_MovPagamento, (5){<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>Banco:<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>}banco_Cheque, (20){<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>Agencia:<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;&nbsp;</span>}agencia_Cheque, (20){<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>N.:<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;&nbsp;</span>}numero_Cheque, (20){<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>CC:<span style="mso-spacerun: yes">&nbsp;&nbsp;</span>}contaCorrente_Cheque,(20){&nbsp;<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>Vlr.:<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>}valor_Cheque]', 2, 25);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(60){- }tipoFormaPagamento_MovPagamento, (60){:<span style="mso-spacerun: yes">&nbsp;&nbsp;</span>}R$ valor_MovPagamento, (5){<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>Banco:<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>}banco_Cheque, (20){<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>Agencia:<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;&nbsp;</span>}agencia_Cheque, (20){<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>N.:<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;&nbsp;</span>}numero_Cheque, (20){<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>CC:<span style="mso-spacerun: yes">&nbsp;&nbsp;</span>}contaCorrente_Cheque,(20){&nbsp;<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;</span>Vlr.:<span style="mso-spacerun: yes">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>}valor_Cheque]', 2, 26);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(20){}responsavelLancamento_Recibo]', 2, 27);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(60){}pessoaPagador_Recibo]', 2, 28);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(20){}responsavelLancamento_Recibo]', 2, 29);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(60){}pessoaPagador_Recibo]', 2, 30);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(20){}data_Recibo]', 2, 31);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(20){}data_Recibo]', 2, 32);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(20){}dataImpressao_Recibo]', 2, 33);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(20){}dataImpressao_Recibo]', 2, 34);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(10){N&deg; matr&iacute;cula:}Matricula_Cliente]', 1, 35);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(10){Dura&ccedil;&atilde;o meses:}Duracao_Contrato]', 1, 36);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(20){Hor&aacute;rios:}]', 1, 37);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(50){Nome do aluno:}]', 1, 38);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(50){</span></span>Respons&aacute;vel<span style="font-size: 27px; font-weight: normal; font-family: Arial, Verdana, sans-serif;"><span style="font-size: 11px;">::}]', 1, 39);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(20){RG:}Rg_Cliente]', 1, 40);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(40){Endere&ccedil;o:}Endereco_Cliente]', 1, 41);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(10){CEP:}CEP_Cliente]', 1, 42);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(50){Cidade:}Cidade_Empresa]', 1, 43);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(10){Telefone:}Telefone_Cliente [(50){E-mail:}Email_Cliente]', 1, 44);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(50){}Descricao_Plano]', 1, 45);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(50){}RazaoSocial_Empresa]', 1, 46);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(18){}Cnpj_Empresa]', 1, 47);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(50){}Endereco_Empresa]', 1, 48);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(50){ - &nbsp;}Complemento_Empresa]', 1, 49);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(50){}Cidade_Empresa]', 1, 50);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(50){}Estado_Empresa]', 1, 51);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(5){ - N. &nbsp;}Numero_Empresa]', 1, 52);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(10){ - CEP: }Cep_Empresa]', 1, 53);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(10){}Duracao_Contrato]', 1, 54);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(20){}VigenciaDe_Contrato]', 1, 55);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(20){}VigenciaAte_Contrato]', 1, 56);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(50){ - &nbsp;}Nome_Modalidade]', 1, 57);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(20){}Identificador_Turma, (20){Turma de:} Descricao_Turma]', 1, 58);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(15){}ValorFinal_Contrato]', 1, 59);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(60){- }Descricao_MovParcela,(20){ - }DataVencimento_MovParcela,(15){ - &nbsp;}ValorParcela_MovParcela]', 1, 60);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(50){}Cidade_Empresa]', 1, 61);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(50){}Estado_Empresa]', 1, 62);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(50){CONTRATANTE:&nbsp;}Nome_Cliente]', 1, 63);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(50){}&nbsp;Respons&aacute;vel]', 1, 64);
INSERT INTO planotextopadraotag (tag, planotextopadrao, codigo) VALUES ('[(50){}Nome_Empresa]', 1, 65);
SELECT setval('planotextopadraotag_codigo_seq', 65, true);

----------------------------------------------- Plano-----------------------------------------------------------------------------------------
INSERT INTO plano (recibotextopadrao, permitepagarcomboleto, percentualmultacancelamento, produtotaxacancelamento, planotextopadrao, produtopadraogerarparcelascontrato, bolsa, ingressoate, vigenciaate, vigenciade, empresa, descricao, codigo, prorataobrigatorio, diasvencimentoprorata) VALUES (2, false, 10, 18, 1, 5, false, '2020-02-26 00:00:00', '2020-02-26 00:00:00', '2010-02-26 00:00:00', 1, 'PACTO 2011: MÉTODO DE GESTÃO', 1, false, '');
SELECT setval('plano_codigo_seq', 1, true);

INSERT INTO planocomposicao (composicao, plano, codigo) VALUES (1, 1, 1);
SELECT setval('planocomposicao_codigo_seq', 1, true);

INSERT INTO planomodalidade (listavezessemana, modalidade, plano, codigo) VALUES ('6 Vezes - R$ 120,00, ', 1, 1, 1);
INSERT INTO planomodalidade (listavezessemana, modalidade, plano, codigo) VALUES ('6 Vezes - R$ 100,00, ', 2, 1, 2);
INSERT INTO planomodalidade (listavezessemana, modalidade, plano, codigo) VALUES ('6 Vezes - R$ 135,00, ', 3, 1, 3);
INSERT INTO planomodalidade (listavezessemana, modalidade, plano, codigo) VALUES ('3 Vezes - R$ 100,00, ', 6, 1, 4);
INSERT INTO planomodalidade (listavezessemana, modalidade, plano, codigo) VALUES ('1 Vezes - R$ 75,00, 2 Vezes - R$ 80,00, 3 Vezes - R$ 95,00, ', 5, 1, 5);
SELECT setval('planomodalidade_codigo_seq', 5, true);

INSERT INTO planomodalidadevezessemana 
	(nrvezes, tipooperacao, tipovalor, valorespecifico, percentualdesconto, planomodalidade, codigo) 
	VALUES (6, '', '', 0, 0, 1, 1);
INSERT INTO planomodalidadevezessemana 
	(nrvezes, tipooperacao, tipovalor, valorespecifico, percentualdesconto, planomodalidade, codigo) 
	VALUES (6, '', '', 0, 0, 2, 2);
INSERT INTO planomodalidadevezessemana 
	(nrvezes, tipooperacao, tipovalor, valorespecifico, percentualdesconto, planomodalidade, codigo) 
	VALUES (6, '', '', 0, 0, 3, 3);
INSERT INTO planomodalidadevezessemana 
	(nrvezes, tipooperacao, tipovalor, valorespecifico, percentualdesconto, planomodalidade, codigo) 
	VALUES (3, '', '', 0, 0, 4, 4);
INSERT INTO planomodalidadevezessemana 
	(nrvezes, tipooperacao, tipovalor, valorespecifico, percentualdesconto, planomodalidade, codigo) 
	VALUES (1, 'RE', 'PD', 0, 25, 5, 5);
INSERT INTO planomodalidadevezessemana 
	(nrvezes, tipooperacao, tipovalor, valorespecifico, percentualdesconto, planomodalidade, codigo) 
	VALUES (2, 'RE', 'PD', 0, 20, 5, 6);
INSERT INTO planomodalidadevezessemana 
	(nrvezes, tipooperacao, tipovalor, valorespecifico, percentualdesconto, planomodalidade, codigo) 
	VALUES (3, 'RE', 'PD', 0, 5, 5, 7);

SELECT setval('planomodalidadevezessemana_codigo_seq', 7, true);


INSERT INTO planoduracao (valordesejadomensal, valordesejadoparcela, valordesejado, tipooperacao, tipovalor, valorespecifico, percentualdesconto, nrmaximoparcelascondpagamento, plano, numeromeses, carencia, codigo) VALUES (150, 150, 0, 'AC', 'PD', 0, 0, 1, 1, 1, 0, 1);
INSERT INTO planoduracao (valordesejadomensal, valordesejadoparcela, valordesejado, tipooperacao, tipovalor, valorespecifico, percentualdesconto, nrmaximoparcelascondpagamento, plano, numeromeses, carencia, codigo) VALUES (120, 120, 20, 'RE', 'PD', 0, 20, 3, 1, 3, 15, 2);
INSERT INTO planoduracao (valordesejadomensal, valordesejadoparcela, valordesejado, tipooperacao, tipovalor, valorespecifico, percentualdesconto, nrmaximoparcelascondpagamento, plano, numeromeses, carencia, codigo) VALUES (112.5, 112.5, 25, 'RE', 'PD', 0, 25, 6, 1, 6, 25, 3);
INSERT INTO planoduracao (valordesejadomensal, valordesejadoparcela, valordesejado, tipooperacao, tipovalor, valorespecifico, percentualdesconto, nrmaximoparcelascondpagamento, plano, numeromeses, carencia, codigo) VALUES (108, 108, 28, 'RE', 'PD', 0, 28, 9, 1, 9, 35, 4);
INSERT INTO planoduracao (valordesejadomensal, valordesejadoparcela, valordesejado, tipooperacao, tipovalor, valorespecifico, percentualdesconto, nrmaximoparcelascondpagamento, plano, numeromeses, carencia, codigo) VALUES (105, 105, 30, 'RE', 'PD', 0, 30, 12, 1, 12, 45, 5);
INSERT INTO planoduracao (valordesejadomensal, valordesejadoparcela, valordesejado, tipooperacao, tipovalor, valorespecifico, percentualdesconto, nrmaximoparcelascondpagamento, plano, numeromeses, carencia, codigo) VALUES (96, 96, 36, 'RE', 'PD', 0, 36, 15, 1, 15, 60, 6);
SELECT setval('planoduracao_codigo_seq', 6, true);


INSERT INTO planocondicaopagamento 
	(percentualdesconto, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 1, 1, 1, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 1, 2, 2, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 2, 2, 3, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 3, 2, 4, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 9, 9, 4, 5, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (5, 1, 1, 5, 6, 'RE', 'PD', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 1, 13, 5, 7, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 2, 2, 5, 8, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 3, 3, 5, 9, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 4, 4, 5, 10, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 5, 5, 5, 11, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 6, 6, 5, 12, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 7, 7, 5, 13, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 8, 8, 5, 14, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 9, 9, 5, 15, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 10, 10, 5, 16, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 11, 11, 5, 17, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 12, 12, 5, 18, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (5, 1, 1, 6, 19, 'RE', 'PD', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 1, 13, 6, 20, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 2, 2, 6, 21, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 3, 3, 6, 22, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 4, 4, 6, 23, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 5, 5, 6, 24, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 6, 6, 6, 25, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 7, 7, 6, 26, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 8, 8, 6, 27, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 9, 9, 6, 28, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 10, 10, 6, 29, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 11, 11, 6, 30, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 12, 12, 6, 31, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (5, 1, 1, 1, 32, 'RE', 'PD', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 1, 13, 1, 33, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (5, 1, 1, 2, 34, 'RE', 'PD', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 1, 13, 2, 35, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 2, 2, 2, 36, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 3, 3, 2, 37, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (5, 1, 1, 3, 38, 'RE', 'PD', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 1, 13, 3, 39, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 2, 2, 3, 40, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 3, 3, 3, 41, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 4, 4, 3, 42, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 5, 5, 3, 43, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 6, 6, 3, 44, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (5, 1, 1, 4, 45, 'RE', 'PD', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 1, 13, 4, 46, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 2, 2, 4, 47, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 3, 3, 4, 48, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 4, 4, 4, 49, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 5, 5, 4, 50, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 6, 6, 4, 51, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 7, 7, 4, 52, '', '', 0);
INSERT INTO planocondicaopagamento 
	(percentualdesconto, qtdparcela, condicaopagamento, planoduracao, codigo, tipooperacao, tipovalor, valorespecifico) 
	VALUES (0, 8, 8, 4, 53, '', '', 0);
SELECT setval('planocondicaopagamento_codigo_seq', 49, true);

INSERT INTO planohorario (tipooperacao, tipovalor, valorespecifico, percentualdesconto, plano, horario, codigo) VALUES ('RE', 'PD', 0, 20, 1, 2, 1);
INSERT INTO planohorario (tipooperacao, tipovalor, valorespecifico, percentualdesconto, plano, horario, codigo) VALUES ('', '', 0, 0, 1, 1, 2);
SELECT setval('planohorario_codigo_seq', 1, true);

INSERT INTO planoprodutosugerido (obrigatorio, produto, valorproduto, plano, codigo) VALUES (true, 6, 0, 1, 1);
INSERT INTO planoprodutosugerido (obrigatorio, produto, valorproduto, plano, codigo) VALUES (false, 8, 0, 1, 2);
INSERT INTO planoprodutosugerido (obrigatorio, produto, valorproduto, plano, codigo) VALUES (false, 7, 0, 1, 3);
INSERT INTO planoprodutosugerido (obrigatorio, produto, valorproduto, plano, codigo) VALUES (false, 13, 50, 1, 4);
SELECT setval('planoprodutosugerido_codigo_seq', 4, true);

----------------------------------------------- Local de Acesso------------------------------------------------------------------------
INSERT INTO localacesso (descricao, codigo, nomecomputador, empresa, tempoentreacessos, servidorimpressoes, portaservidorimp) VALUES ('PACTO - ACESSO', 1, 'ZILLYONWEB01', 1, 0, 'ZILLYONWEB01', 5100);
INSERT INTO localacesso (descricao, codigo, nomecomputador, empresa, tempoentreacessos, servidorimpressoes, portaservidorimp) VALUES ('RECEPCAO EMPRESA', 2, 'ZILLYONWEB01', 1, 0, 'ZILLYONWEB01', 5100);

SELECT setval('localacesso_codigo_seq', 2, true);

INSERT INTO coletor 
	(codigo, descricao, modelo, numserie, portacomunicacao, 
	modotransmissao, veloctransmissao, resolucaodpi, releentrada, 
	temporeleentrada, relesaida, temporelesaida, msgdisplay, 
	sentidoacesso, aguardagiro, arquivoprograma, padraocadastro, 
	localacesso, sensorentrada, sensorsaida, numeroterminal) 
	VALUES (1, 'ENTRADA', 'MODELO_COLETOR_TRIXSTANDARD', 
	'{9BA4FF57-9B43-5343-A570-92F433E3A275}', '1',
	 'MODOTRANSMISSAO_COLETOR_AUTO', 9600, 512, 1, 
	 5000, 2, 5000, 'SEJAM BEM VINDO', 'SENTIDOACESSO_COLETOR_ENTRADA', false, NULL, false, 1, 0, 1, 2);
INSERT INTO coletor (codigo, descricao, modelo, numserie, portacomunicacao, modotransmissao, veloctransmissao, resolucaodpi, releentrada, temporeleentrada, relesaida, temporelesaida, msgdisplay, sentidoacesso, aguardagiro, arquivoprograma, padraocadastro, localacesso, sensorentrada, sensorsaida, numeroterminal) VALUES (2, 'CATRACA', 'MODELO_COLETOR_TRIXSTANDARD', '', '1', 'MODOTRANSMISSAO_COLETOR_AUTO', 9600, 512, 1, 5000, 2, 5000, '', 'SENTIDOACESSO_COLETOR_INDIFERENTE', true, NULL, false, 2, 0, 1, 1);

SELECT setval('coletor_codigo_seq', 2, true);

----------------------------------------------Bancos.  --- Pendencia ( O codigo do Banco tem que ser ChaveUnica ).-------------------------------------------------------------------------
INSERT INTO banco (codigobanco,nome) VALUES (1,'BANCO DO BRASIL');
INSERT INTO banco (codigobanco,nome) VALUES (3,'BANCO DA AMAZONIA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (4,'BANCO DO NORDESTE DO BRASIL S.');
INSERT INTO banco (codigobanco,nome) VALUES (8,'BANCO SANTANDER MERIDIONAL S.A');
INSERT INTO banco (codigobanco,nome) VALUES (20,'BANCO DO ESTADO DE ALAGOAS S.A');
INSERT INTO banco (codigobanco,nome) VALUES (21,'BANESTES S.A BANCO DO ESTADO D');
INSERT INTO banco (codigobanco,nome) VALUES (22,'CREDIREAL');
INSERT INTO banco (codigobanco,nome) VALUES (24,'BANCO DE PERNAMBUCO S.A.-BANDE');
INSERT INTO banco (codigobanco,nome) VALUES (25,'BANCO ALFA S/A');
INSERT INTO banco (codigobanco,nome) VALUES (26,'BANCO DO ESTADO DO ACRE S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (27,'BANCO DO ESTADO DE SANTA CATAR');
INSERT INTO banco (codigobanco,nome) VALUES (28,'BANEB');
INSERT INTO banco (codigobanco,nome) VALUES (29,'BANCO BANERJ S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (30,'PARAIBAN BANCO DO ESTADO DA PA');
INSERT INTO banco (codigobanco,nome) VALUES (31,'BANCO DO ESTADO DE GOIAS S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (32,'BANCO DO ESTADO DO MATO GROSSO');
INSERT INTO banco (codigobanco,nome) VALUES (33,'BANCO DO ESTADO DE SAO PAULO S');
INSERT INTO banco (codigobanco,nome) VALUES (34,'BANCO DO ESTADO DO AMAZONAS S.');
INSERT INTO banco (codigobanco,nome) VALUES (35,'BANCO DO ESTADO DO CEARA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (36,'BANCO DO ESTADO DO MARANHAO S.');
INSERT INTO banco (codigobanco,nome) VALUES (37,'BANCO DO ESTADO DO PARA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (38,'BANCO BANESTADO S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (39,'BANCO DO ESTADO DO PIAUI S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (40,'BANCO CARGILL S.A');
INSERT INTO banco (codigobanco,nome) VALUES (41,'BANCO DO ESTADO DO RIO GRANDE');
INSERT INTO banco (codigobanco,nome) VALUES (42,'BANCO J. SAFRA S.A');
INSERT INTO banco (codigobanco,nome) VALUES (44,'BANCO BVA SA');
INSERT INTO banco (codigobanco,nome) VALUES (45,'BANCO OPPORTUNITY S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (47,'BANCO DO ESTADO DE SERGIPE S.A');
INSERT INTO banco (codigobanco,nome) VALUES (48,'BANCO BEMGE S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (59,'BANCO DO ESTADO DE RONDONIA S.');
INSERT INTO banco (codigobanco,nome) VALUES (61,'BANCO ABB SA');
INSERT INTO banco (codigobanco,nome) VALUES (62,'BANCO1.NET S.A');
INSERT INTO banco (codigobanco,nome) VALUES (63,'IBIBANK S.A BANCO MULTIPLO');
INSERT INTO banco (codigobanco,nome) VALUES (64,'GOLDMAN SACHS DO BRASIL-BANCO');
INSERT INTO banco (codigobanco,nome) VALUES (65,'BANCO PATAGON S.A');
INSERT INTO banco (codigobanco,nome) VALUES (66,'BANCO MORGAN STANLEY DEAN WITT');
INSERT INTO banco (codigobanco,nome) VALUES (67,'BANCO BANEB SA');
INSERT INTO banco (codigobanco,nome) VALUES (70,'BRB - BANCO DE BRASILIA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (104,'CAIXA ECONOMICA FEDERAL');
INSERT INTO banco (codigobanco,nome) VALUES (106,'BANCO ITABANCO S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (107,'BANCO BBM S.A');
INSERT INTO banco (codigobanco,nome) VALUES (109,'CREDIBANCO S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (116,'BANCO BNL DO BRASIL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (148,'BANK OF AMERICA - BRASIL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (151,'BANCO NOSSA CAIXA S.A');
INSERT INTO banco (codigobanco,nome) VALUES (153,'CAIXA ECONOMICA ESTADUAL DO RI');
INSERT INTO banco (codigobanco,nome) VALUES (165,'BANCO NORCHEM S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (166,'BANCO INTER-ATLANTICO S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (168,'HSBC INVESTMENT BANK BRASIL S.');
INSERT INTO banco (codigobanco,nome) VALUES (175,'CONTINENTAL BANCO S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (184,'BANCO BBA - CREDITANSTALT S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (199,'BANCO FINANCIAL PORTUGUES');
INSERT INTO banco (codigobanco,nome) VALUES (200,'BANCO FICRISA AXELRUD S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (201,'BANCO AXIAL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (204,'BANCO INTER AMERICAN EXPRESS S');
INSERT INTO banco (codigobanco,nome) VALUES (205,'BANCO SUL AMERICA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (206,'BANCO MARTINELLI S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (208,'BANCO PACTUAL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (210,'DRESDNER BANK LATEINAMERIKA AK');
INSERT INTO banco (codigobanco,nome) VALUES (211,'BANCO SISTEMA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (212,'BANCO MATONE S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (213,'BANCO ARBI S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (214,'BANCO DIBENS S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (215,'BANCO AMERICA DO SUL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (216,'BANCO REGIONAL MALCON S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (217,'BANCO JOHN DEERE S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (218,'BANCO BONSUCESSO S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (219,'BANCO ZOGBI');
INSERT INTO banco (codigobanco,nome) VALUES (220,'BANCO CREFISUL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (221,'BANCO CHASE FLEMING S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (222,'BANCO CREDIT LYONNAIS BRASIL S');
INSERT INTO banco (codigobanco,nome) VALUES (224,'BANCO FIBRA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (225,'BANCO BRASCAN S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (228,'BANCO ICATU S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (229,'BANCO CRUZEIRO DO SUL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (230,'BANCO BANDEIRANTES S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (231,'BANCO BOAVISTA INTERATLANTICO');
INSERT INTO banco (codigobanco,nome) VALUES (232,'BANCO INTERPART S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (233,'BANCO GE CAPITAL S.A');
INSERT INTO banco (codigobanco,nome) VALUES (234,'BANCO LAVRA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (235,'BANCO LIBERAL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (236,'BANCO CAMBIAL SA');
INSERT INTO banco (codigobanco,nome) VALUES (237,'BANCO BRADESCO S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (239,'BANCO BANCRED S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (240,'BANCO DE CREDITO REAL DE MINAS');
INSERT INTO banco (codigobanco,nome) VALUES (241,'BANCO CLASSICO S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (242,'BANCO EUROINVEST S.A. EUROBANC');
INSERT INTO banco (codigobanco,nome) VALUES (243,'BANCO STOCK MAXIMA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (244,'BANCO CIDADE S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (245,'BANCO EMPRESARIAL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (246,'BANCO ABC-BRASIL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (247,'UBS WARBURG S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (249,'BANCO INVESTCRED S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (250,'BANCO SCHAHIN');
INSERT INTO banco (codigobanco,nome) VALUES (252,'BANCO FININVEST S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (254,'PARANA BANCO S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (255,'MILBANCO S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (256,'BANCO GULFINVEST S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (258,'BANCO INDUSCRED S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (262,'BANCO BOREAL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (263,'BANCO CACIQUE S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (265,'BANCO FATOR S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (266,'BANCO CEDULA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (267,'BANCO BBM-COM.C.IMOB.CFI S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (275,'BANCO ABN AMRO REAL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (277,'BANCO PLANIBANC S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (282,'BANCO BRASILEIRO COMERCIAL S.A');
INSERT INTO banco (codigobanco,nome) VALUES (291,'BANCO DE CREDITO NACIONAL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (294,'BCR');
INSERT INTO banco (codigobanco,nome) VALUES (300,'BANCO DE LA NACION ARGENTINA');
INSERT INTO banco (codigobanco,nome) VALUES (302,'BANCO DO PROGRESSO S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (303,'BANCO HNF S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (304,'BANCO PONTUAL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (318,'BANCO BMG S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (320,'BANCO INDUSTRIAL E COMERCIAL S');
INSERT INTO banco (codigobanco,nome) VALUES (341,'BANCO ITAU S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (346,'BANCO BFB');
INSERT INTO banco (codigobanco,nome) VALUES (347,'BANCO SUDAMERIS BRASIL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (351,'BANCO BOZANO, SIMONSEN S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (353,'BANCO SANTANDER BRASIL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (356,'BANCO ABN AMRO S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (366,'BANCO SOCIETE GENERALE BRASIL');
INSERT INTO banco (codigobanco,nome) VALUES (369,'BANCO DIGIBANCO S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (370,'BANCO EUROPEU PARA AMERICA LAT');
INSERT INTO banco (codigobanco,nome) VALUES (372,'BANCO ITAMARATI S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (375,'BANCO FENICIA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (376,'BANCO CHASE MANHATTAN S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (388,'BANCO BMD S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (389,'BANCO MERCANTIL DO BRASIL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (392,'BANCO MERCANTIL DE SAO PAULO S');
INSERT INTO banco (codigobanco,nome) VALUES (394,'BANCO BMC S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (399,'HSBC BANK BRASIL S.A.-BANCO MU');
INSERT INTO banco (codigobanco,nome) VALUES (409,'UNIBANCO - UNIAO DE BANCOS BRA');
INSERT INTO banco (codigobanco,nome) VALUES (412,'BANCO CAPITAL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (415,'BANCO NACIONAL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (420,'BANORTE - BANCO NACIONAL DO NO');
INSERT INTO banco (codigobanco,nome) VALUES (422,'BANCO SAFRA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (424,'BANCO SANTANDER NOROESTE S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (434,'BANFORT - BANCO FORTALEZA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (453,'BANCO RURAL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (456,'BANCO DE TOKYO MITSUBISHI BRAS');
INSERT INTO banco (codigobanco,nome) VALUES (464,'BANCO SUMITOMO MITSUI BRASILEI');
INSERT INTO banco (codigobanco,nome) VALUES (472,'LLOYDS BANK PLC');
INSERT INTO banco (codigobanco,nome) VALUES (473,'BANCO FINANCIAL PORTUGUES');
INSERT INTO banco (codigobanco,nome) VALUES (479,'BANKBOSTON BANCO MULTIPLO S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (480,'BANCO WACHOVIA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (487,'DEUTSCHE BANK S. A. - BANCO AL');
INSERT INTO banco (codigobanco,nome) VALUES (488,'MORGAN GUARANTY TRUST COMPANY');
INSERT INTO banco (codigobanco,nome) VALUES (489,'BANCO FRANCES INTERNACIONAL-BR');
INSERT INTO banco (codigobanco,nome) VALUES (492,'ING BANK N.V.');
INSERT INTO banco (codigobanco,nome) VALUES (493,'BANCO UNION - BRASIL S.A');
INSERT INTO banco (codigobanco,nome) VALUES (494,'BANCO DE LA REPUBLICA ORIENTAL');
INSERT INTO banco (codigobanco,nome) VALUES (495,'BANCO DE LA PROVINCIA DE BUENO');
INSERT INTO banco (codigobanco,nome) VALUES (496,'BANCO UNO-E BRASIL S.A');
INSERT INTO banco (codigobanco,nome) VALUES (498,'CENTRO HISPANO BANCO');
INSERT INTO banco (codigobanco,nome) VALUES (499,'BANCO IOCHPE S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (501,'BANCO BRASILEIRO IRAQUIANO S.A');
INSERT INTO banco (codigobanco,nome) VALUES (502,'BANCO SANTANDER DE NEGOCIOS S.');
INSERT INTO banco (codigobanco,nome) VALUES (504,'BANCO MULTIPLIC S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (505,'BANCO CREDIT SUISSE FIRST BOST');
INSERT INTO banco (codigobanco,nome) VALUES (600,'BANCO LUSO BRASILEIRO S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (602,'BANCO PATENTE S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (604,'BANCO INDUSTRIAL DO BRASIL S.');
INSERT INTO banco (codigobanco,nome) VALUES (607,'BANCO SANTOS NEVES S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (610,'BANCO VR S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (611,'BANCO PAULISTA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (612,'BANCO GUANABARA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (613,'BANCO PECUNIA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (618,'BANCO TENDENCIA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (621,'BANCO APLICAP S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (623,'BANCO PANAMERICANO S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (624,'BANCO GENERAL MOTORS S.A');
INSERT INTO banco (codigobanco,nome) VALUES (625,'BANCO ARAUCARIA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (626,'BANCO FICSA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (627,'BANCO DESTAK S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (628,'BANCO CRITERIUM S. A.');
INSERT INTO banco (codigobanco,nome) VALUES (630,'BANCO INTERCAP S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (633,'BANCO RENDIMENTO S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (634,'BANCO TRIANGULO S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (635,'BANCO DO ESTADO AMAPA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (637,'BANCO SOFISA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (638,'BANCO PROSPER S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (640,'BANCO CREDITO METROPOLITANO S/');
INSERT INTO banco (codigobanco,nome) VALUES (641,'BANCO BILBAO VIZCAYA ARGENTARI');
INSERT INTO banco (codigobanco,nome) VALUES (643,'BANCO PINE S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (645,'BANCO DO ESTADO DE RORAIMA S.A');
INSERT INTO banco (codigobanco,nome) VALUES (647,'BANCO MARKA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (649,'BANCO DIMENSAO S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (650,'BANCO PEBB S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (652,'BANCO FRANCES E BRASILEIRO SA');
INSERT INTO banco (codigobanco,nome) VALUES (653,'BANCO INDUSVAL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (654,'BANCO A.J. RENNER S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (655,'BANCO VOTORANTIM S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (656,'BANCO MATRIX S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (657,'BANCO TECNICORP S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (658,'BANCO PORTO REAL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (702,'BANCO SANTOS S. A.');
INSERT INTO banco (codigobanco,nome) VALUES (707,'BANCO DAYCOVAL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (711,'BANCO VETOR S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (715,'BANCO VEGA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (718,'BANCO OPERADOR S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (719,'BANCO BANIF PRIMUS S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (720,'BANCO MAXINVEST S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (721,'BANCO CREDIBEL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (722,'BANCO INTERIOR DE SAO PAULO S.');
INSERT INTO banco (codigobanco,nome) VALUES (725,'BANCO FINANSINOS S. A.');
INSERT INTO banco (codigobanco,nome) VALUES (728,'BANCO FITAL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (729,'BANCO FONTE CINDAM S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (732,'BANCO MINAS S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (733,'BANCO DAS NACOES S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (734,'BANCO GERDAU S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (735,'BANCO POTTENCIAL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (737,'BANCO THECA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (738,'BANCO MORADA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (739,'BANCO BGN S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (740,'BANCO BARCLAYS E GALICIA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (741,'BANCO RIBEIRAO PRETO S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (742,'BANCO EQUATORIAL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (743,'BANCO EMBLEMA S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (744,'BANKBOSTON N.A.');
INSERT INTO banco (codigobanco,nome) VALUES (745,'BANCO CITIBANK S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (746,'BANCO MODAL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (747,'BANCO RABOBANK INTERNATIONAL B');
INSERT INTO banco (codigobanco,nome) VALUES (748,'BANCO COOPERATIVO SICREDI S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (749,'BR BANCO MERCANTIL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (750,'HSBC REPUBLIC BANK BRASIL S.A-');
INSERT INTO banco (codigobanco,nome) VALUES (751,'DRESDNER BANK BRASIL S.A. BANC');
INSERT INTO banco (codigobanco,nome) VALUES (752,'BANCO BANQUE NATIONALE DE PARI');
INSERT INTO banco (codigobanco,nome) VALUES (753,'BANCO COMERCIAL URUGUAI S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (755,'BANCO MERRILL LYNCH S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (756,'BANCO COOPERATIVO DO BRASIL S.');
INSERT INTO banco (codigobanco,nome) VALUES (757,'BANCO KEB DO BRASIL S.A.');
INSERT INTO banco (codigobanco,nome) VALUES (800,'BCR BANCO DE CREDITO REAL S.A');
----------------------------------------Cidades--------------------------------------------------------------------------
INSERT INTO cidade (pais, estado, nome, nomesemacento, codigo) VALUES (1, 1, 'GOIÂNIA', 'GOIANIA', 1);
INSERT INTO cidade (pais, estado, nome, nomesemacento, codigo) VALUES (1, 1, 'BELO HORIZONTE', 'BELO HORIZONTE', 2);
INSERT INTO cidade (pais, estado, nome, nomesemacento, codigo) VALUES (1, 2, 'SÃO PAULO', 'SAO PAULO', 3);
INSERT INTO cidade (pais, estado, nome, nomesemacento, codigo) VALUES (1, 3, 'RIO DE JANEIRO', 'RIO DE JANEIRO', 4);
INSERT INTO cidade (pais, estado, nome, nomesemacento, codigo) VALUES (1, 17, 'BELÉM', 'BELEM', 5);
INSERT INTO cidade (pais, estado, nome, nomesemacento, codigo) VALUES (1, 20, 'RECIFE', 'RECIFE', 6);
INSERT INTO cidade (pais, estado, nome, nomesemacento, codigo) VALUES (1, 13, 'BRASÍLIA', 'BRASILIA', 7);
INSERT INTO cidade (pais, estado, nome, nomesemacento, codigo) VALUES (1, 1, 'TAGUATINGA', 'TAGUATINGA', 8);
INSERT INTO cidade (pais, estado, nome, nomesemacento, codigo) VALUES (1, 13, 'CRUZEIRO', 'CRUZEIRO', 9);
INSERT INTO cidade (pais, estado, nome, nomesemacento, codigo) VALUES (1, 5, 'CUIABÁ', 'CUIABA', 10);
INSERT INTO cidade (pais, estado, nome, nomesemacento, codigo) VALUES (1, 27, 'CAMPO GRANDE', 'CAMPO GRANDE', 11);
INSERT INTO cidade (pais, estado, nome, nomesemacento, codigo) VALUES (1, 24, 'PALMAS', 'PALMAS', 12);
INSERT INTO cidade (pais, estado, nome, nomesemacento, codigo) VALUES (1, 4, 'UBERLÂNDIA', 'UBERLANDIA', 13);
INSERT INTO cidade (pais, estado, nome, nomesemacento, codigo) VALUES (1, 4, 'UBERABA', 'UBERABA', 14);
INSERT INTO cidade (pais, estado, nome, nomesemacento, codigo) VALUES (1, 4, 'LAVRAS', 'LAVRAS', 15);

---------------------------------- Novo contrato com turmas - AAPrimeiroCliente ----------------------------------------
------------------------------------------Cliente----------------------------------------------------------------------
INSERT INTO cliente (freepass, responsavelfreepass, codigomatricula, identificadorparacobranca, contadigito, conta, agenciadigito, agencia, banco, codacesso, codacessoalternativo, categoria, matricula, situacao, pessoa, codigo, empresa, uadata) VALUES (NULL, NULL, 1, '', '', '', '', '', '', '1000010007', '', NULL, '000001', 'AT', 2, 1, 1, NULL);

INSERT INTO endereco (pessoa, tipoendereco, cep, bairro, numero, complemento, endereco, enderecocorrespondencia, codigo) VALUES (2, 'RE', '74.275-280', 'JARDIM AMÉRICA', '1', 'COMPLEMENTO', 'RUA C 200', false, 1);
INSERT INTO email (pessoa, emailcorrespondencia, email, codigo) VALUES (2, false, '<EMAIL>', 1);

------------------------------------------Contrato----------------------------------------------------------------------
INSERT INTO contrato (bolsa, naopermitirrenovacaorematriculadecontratoanteriores, valordescontoespecifico, valordescontoporcentagem, nomemodalidades, somaproduto, contratoresponsavelrenovacaomatricula, contratoresponsavelrematriculamatricula, datalancamento, datamatricula, dataprevistarenovar, datarenovarrealizada, dataprevistarematricula, datarematricularealizada, situacaocontrato, situacaorenovacao, situacaorematricula, contratobaseadorenovacao, contratobaseadorematricula, pagarcomboleto, responsavelcontrato, observacao, responsavelliberacaocondicaopagamento, valorfinal, valorbasecalculo, vigenciaate, vigenciaateajustada, vigenciade, estendecoberturafamiliares, situacao, plano, pessoa, consultor, empresa, codigo, conveniodesconto, dividirprodutosnasparcelas, desconto, tipodesconto, valordesconto, diavencimentoprorata) VALUES (false, false, 0, 0, 'PPGM', 0, 0, 0, '2011-03-25 10:30:06.587', '2011-03-25 00:00:00', '2012-03-24 00:00:00', NULL, '2012-03-24 00:00:00', NULL, 'MA', '', '', 0, 0, false, 1, 'O CLIENTE FOI DISPENSADO DE FAZER UMA NOVA AVALIAÇÃO FÍSICA, AUTORIZADO PELA DIRETORIA.', NULL, 1890, 1890, '2012-03-24 00:00:00', '2012-03-24 00:00:00', '2011-03-25 00:00:00', false, 'AT', 1, 2, 1, 1, 1, NULL, false, NULL, '', NULL, 0);

INSERT INTO contratocomposicao (composicao, contrato, codigo) VALUES (1, 1, 1);

INSERT INTO contratocondicaopagamento (percentualdesconto, condicaopagamento, codigo, tipooperacao, tipovalor, valorespecifico, contrato) VALUES (0, 12, 1, '', '', 0, 1);

INSERT INTO contratoduracao (valordesejadomensal, valordesejadoparcela, valordesejado, tipooperacao, tipovalor, valorespecifico, percentualdesconto, nrmaximoparcelascondpagamento, contrato, numeromeses, carencia, codigo) VALUES (105, 105, 30, 'RE', 'PD', 0, 30, 12, 1, 12, 45, 1);

INSERT INTO contratohorario (tipooperacao, tipovalor, valorespecifico, percentualdesconto, contrato, horario, codigo) VALUES ('', '', 0, 0, 1, 1, 1);

INSERT INTO contratomodalidade (vezessemana, valormodalidade, valorfinalmodalidade, valorbasecalculo, modalidade, contrato, codigo) VALUES (3, 100, 100, NULL, 6, 1, 1);
INSERT INTO contratomodalidade (vezessemana, valormodalidade, valorfinalmodalidade, valorbasecalculo, modalidade, contrato, codigo) VALUES (6, 45, 45, NULL, 3, 1, 2);
INSERT INTO contratomodalidade (vezessemana, valormodalidade, valorfinalmodalidade, valorbasecalculo, modalidade, contrato, codigo) VALUES (6, 35, 35, NULL, 2, 1, 3);
INSERT INTO contratomodalidade (vezessemana, valormodalidade, valorfinalmodalidade, valorbasecalculo, modalidade, contrato, codigo) VALUES (6, 45, 45, NULL, 1, 1, 4);

INSERT INTO contratomodalidadeturma (turma, contratomodalidade, codigo) VALUES (1, 1, 1);

INSERT INTO contratomodalidadehorarioturma (horarioturma, contratomodalidadeturma, codigo) VALUES (1, 1, 1);
INSERT INTO contratomodalidadehorarioturma (horarioturma, contratomodalidadeturma, codigo) VALUES (3, 1, 2);
INSERT INTO contratomodalidadehorarioturma (horarioturma, contratomodalidadeturma, codigo) VALUES (4, 1, 3);

INSERT INTO contratomodalidadevezessemana (nrvezes, tipooperacao, tipovalor, valorespecifico, percentualdesconto, contratomodalidade, codigo) VALUES (3, '', '', 0, 0, 1, 1);
INSERT INTO contratomodalidadevezessemana (nrvezes, tipooperacao, tipovalor, valorespecifico, percentualdesconto, contratomodalidade, codigo) VALUES (6, '', '', 0, 0, 2, 2);
INSERT INTO contratomodalidadevezessemana (nrvezes, tipooperacao, tipovalor, valorespecifico, percentualdesconto, contratomodalidade, codigo) VALUES (6, '', '', 0, 0, 3, 3);
INSERT INTO contratomodalidadevezessemana (nrvezes, tipooperacao, tipovalor, valorespecifico, percentualdesconto, contratomodalidade, codigo) VALUES (6, '', '', 0, 0, 4, 4);

INSERT INTO contratoplanoprodutosugerido (codigo, valorfinalproduto, contrato, planoprodutosugerido) VALUES (1, 0, 1, 1);

INSERT INTO contratotextopadrao (planotextopadrao, contrato, codigo) VALUES (1, 1, 1);

INSERT INTO questionariocliente (data, consultor, cliente, questionario, codigo, observacao) VALUES ('2011-02-03 00:00:00', 1, 1, 1, 1, '');
INSERT INTO questionariocliente (data, consultor, cliente, questionario, codigo, observacao) VALUES ('2011-03-10 00:00:00', 1, 1, 2, 2, '');

INSERT INTO historicocontrato (datafinalsituacao, tipohistorico, datainiciosituacao, situacaorelativahistorico, dataregistro, responsavelliberacaomudancahistorico, responsavelregistro, descricao, contrato, codigo) VALUES ('2012-03-24 00:00:00', 'MA', '2011-03-25 00:00:00', '', '2011-03-25 00:00:00', NULL, 1, 'MATRICULADO', 1, 1);

INSERT INTO historicovinculo (codigo, dataregistro, tipohistoricovinculo, tipocolaborador, cliente, colaborador) VALUES (1, '2011-03-25 10:28:35.28', 'EN', 'CO', 1, 1);

INSERT INTO matriculaalunohorarioturma (codigo, empresa, pessoa, contrato, datainiciomatricula, datafimmatricula, horarioturma, modalidade, turma) VALUES (1, 1, 2, 1, '2011-03-25', '2012-03-24', 1,1,1);
INSERT INTO matriculaalunohorarioturma (codigo, empresa, pessoa, contrato, datainiciomatricula, datafimmatricula, horarioturma, modalidade, turma) VALUES (2, 1, 2, 1, '2011-03-25', '2012-03-24', 3,1,1);
INSERT INTO matriculaalunohorarioturma (codigo, empresa, pessoa, contrato, datainiciomatricula, datafimmatricula, horarioturma, modalidade, turma) VALUES (3, 1, 2, 1, '2011-03-25', '2012-03-24', 4,1,1);

INSERT INTO operadoracartao (descricao, codigooperadora, codigointegracao, codigo) VALUES ('VISA ELECTRON (DÉBITO)', 901, 1, 1);
INSERT INTO operadoracartao (descricao, codigooperadora, codigointegracao, codigo) VALUES ('VISA (CRÉDITO)', 902, 1, 2);
INSERT INTO operadoracartao (descricao, codigooperadora, codigointegracao, codigo) VALUES ('MASTERCARD MAESTRO (DÉBITO)', 903, 2, 3);
INSERT INTO operadoracartao (descricao, codigooperadora, codigointegracao, codigo) VALUES ('MASTERCARD (CRÉDITO)', 904, 2, 4);

INSERT INTO recibopagamento (codigo, valortotal, pessoapagador, nomepessoapagador, responsavellancamento, contrato, data) VALUES (1, 1890, 2, 'AA_PRIMEIRO CLIENTE', 1, 1, '2011-03-25 10:36:05.466');

INSERT INTO movpagamento (responsavelpagamento, nrparcelacartaocredito, movpagamentoescolhida, operadoracartao, nomepagador, formapagamento, valor, datalancamento, datapagamento, pessoa, codigo, dataquitacao, conveniocobranca, recibopagamento) VALUES (1, 0, true, NULL, 'AA_PRIMEIRO CLIENTE', 1, 157.5, '2011-03-25 10:34:42.89', '2011-03-25 10:34:42.89', 2, 1, '2011-03-25', NULL, 1);
INSERT INTO movpagamento (responsavelpagamento, nrparcelacartaocredito, movpagamentoescolhida, operadoracartao, nomepagador, formapagamento, valor, datalancamento, datapagamento, pessoa, codigo, dataquitacao, conveniocobranca, recibopagamento) VALUES (1, 0, true, NULL, 'AA_PRIMEIRO CLIENTE', 4, 1102.5, '2011-03-25 10:34:42.89', '2011-03-25 10:34:42.89', 2, 2, '2011-03-25', NULL, 1);
INSERT INTO movpagamento (responsavelpagamento, nrparcelacartaocredito, movpagamentoescolhida, operadoracartao, nomepagador, formapagamento, valor, datalancamento, datapagamento, pessoa, codigo, dataquitacao, conveniocobranca, recibopagamento) VALUES (1, 3, true, 2, 'AA_PRIMEIRO CLIENTE', 2, 472.5, '2011-03-25 10:34:42.89', '2011-03-25 10:34:42.89', 2, 3, '2011-03-25', NULL, 1);
INSERT INTO movpagamento (responsavelpagamento, nrparcelacartaocredito, movpagamentoescolhida, operadoracartao, nomepagador, formapagamento, valor, datalancamento, datapagamento, pessoa, codigo, dataquitacao, conveniocobranca, recibopagamento) VALUES (1, 0, true, 1, 'AA_PRIMEIRO CLIENTE', 3, 157.5, '2011-03-25 10:34:42.89', '2011-03-27 00:00:00', 2, 4, '2011-03-25', NULL, 1);



INSERT INTO movparcela (descricao, imprimirboletoparcela, conveniocobranca, utilizaconvenio, vendaavulsa, aulaavulsadiaria, percentualjuro, percentualmulta, responsavel, valorparcela, contrato, situacao, datavencimento, dataregistro, codigo) VALUES ('PARCELA 1', false, NULL, false, NULL, NULL, 0, 0, 1, 157.5, 1, 'PG', '2011-03-25 00:00:00', '2011-03-25 00:00:00', 1);
INSERT INTO movparcela (descricao, imprimirboletoparcela, conveniocobranca, utilizaconvenio, vendaavulsa, aulaavulsadiaria, percentualjuro, percentualmulta, responsavel, valorparcela, contrato, situacao, datavencimento, dataregistro, codigo) VALUES ('PARCELA 2', false, NULL, false, NULL, NULL, 0, 0, 1, 157.5, 1, 'PG', '2011-04-25 00:00:00', '2011-03-25 00:00:00', 2);
INSERT INTO movparcela (descricao, imprimirboletoparcela, conveniocobranca, utilizaconvenio, vendaavulsa, aulaavulsadiaria, percentualjuro, percentualmulta, responsavel, valorparcela, contrato, situacao, datavencimento, dataregistro, codigo) VALUES ('PARCELA 3', false, NULL, false, NULL, NULL, 0, 0, 1, 157.5, 1, 'PG', '2011-05-25 00:00:00', '2011-03-25 00:00:00', 3);
INSERT INTO movparcela (descricao, imprimirboletoparcela, conveniocobranca, utilizaconvenio, vendaavulsa, aulaavulsadiaria, percentualjuro, percentualmulta, responsavel, valorparcela, contrato, situacao, datavencimento, dataregistro, codigo) VALUES ('PARCELA 4', false, NULL, false, NULL, NULL, 0, 0, 1, 157.5, 1, 'PG', '2011-06-25 00:00:00', '2011-03-25 00:00:00', 4);
INSERT INTO movparcela (descricao, imprimirboletoparcela, conveniocobranca, utilizaconvenio, vendaavulsa, aulaavulsadiaria, percentualjuro, percentualmulta, responsavel, valorparcela, contrato, situacao, datavencimento, dataregistro, codigo) VALUES ('PARCELA 5', false, NULL, false, NULL, NULL, 0, 0, 1, 157.5, 1, 'PG', '2011-07-25 00:00:00', '2011-03-25 00:00:00', 5);
INSERT INTO movparcela (descricao, imprimirboletoparcela, conveniocobranca, utilizaconvenio, vendaavulsa, aulaavulsadiaria, percentualjuro, percentualmulta, responsavel, valorparcela, contrato, situacao, datavencimento, dataregistro, codigo) VALUES ('PARCELA 6', false, NULL, false, NULL, NULL, 0, 0, 1, 157.5, 1, 'PG', '2011-08-25 00:00:00', '2011-03-25 00:00:00', 6);
INSERT INTO movparcela (descricao, imprimirboletoparcela, conveniocobranca, utilizaconvenio, vendaavulsa, aulaavulsadiaria, percentualjuro, percentualmulta, responsavel, valorparcela, contrato, situacao, datavencimento, dataregistro, codigo) VALUES ('PARCELA 7', false, NULL, false, NULL, NULL, 0, 0, 1, 157.5, 1, 'PG', '2011-09-25 00:00:00', '2011-03-25 00:00:00', 7);
INSERT INTO movparcela (descricao, imprimirboletoparcela, conveniocobranca, utilizaconvenio, vendaavulsa, aulaavulsadiaria, percentualjuro, percentualmulta, responsavel, valorparcela, contrato, situacao, datavencimento, dataregistro, codigo) VALUES ('PARCELA 8', false, NULL, false, NULL, NULL, 0, 0, 1, 157.5, 1, 'PG', '2011-10-25 00:00:00', '2011-03-25 00:00:00', 8);
INSERT INTO movparcela (descricao, imprimirboletoparcela, conveniocobranca, utilizaconvenio, vendaavulsa, aulaavulsadiaria, percentualjuro, percentualmulta, responsavel, valorparcela, contrato, situacao, datavencimento, dataregistro, codigo) VALUES ('PARCELA 9', false, NULL, false, NULL, NULL, 0, 0, 1, 157.5, 1, 'PG', '2011-11-25 00:00:00', '2011-03-25 00:00:00', 9);
INSERT INTO movparcela (descricao, imprimirboletoparcela, conveniocobranca, utilizaconvenio, vendaavulsa, aulaavulsadiaria, percentualjuro, percentualmulta, responsavel, valorparcela, contrato, situacao, datavencimento, dataregistro, codigo) VALUES ('PARCELA 10', false, NULL, false, NULL, NULL, 0, 0, 1, 157.5, 1, 'PG', '2011-12-25 00:00:00', '2011-03-25 00:00:00', 10);
INSERT INTO movparcela (descricao, imprimirboletoparcela, conveniocobranca, utilizaconvenio, vendaavulsa, aulaavulsadiaria, percentualjuro, percentualmulta, responsavel, valorparcela, contrato, situacao, datavencimento, dataregistro, codigo) VALUES ('PARCELA 11', false, NULL, false, NULL, NULL, 0, 0, 1, 157.5, 1, 'PG', '2012-01-25 00:00:00', '2011-03-25 00:00:00', 11);
INSERT INTO movparcela (descricao, imprimirboletoparcela, conveniocobranca, utilizaconvenio, vendaavulsa, aulaavulsadiaria, percentualjuro, percentualmulta, responsavel, valorparcela, contrato, situacao, datavencimento, dataregistro, codigo) VALUES ('PARCELA 12', false, NULL, false, NULL, NULL, 0, 0, 1, 157.5, 1, 'PG', '2012-02-25 00:00:00', '2011-03-25 00:00:00', 12);


INSERT INTO movproduto (situacao, quitado, datafinalvigencia, datainiciovigencia, apresentarmovproduto, anoreferencia, mesreferencia, responsavellancamento, datalancamento, totalfinal, valordesconto, precounitario, quantidade, descricao, empresa, pessoa, contrato, produto, codigo) VALUES ('PG', true, NULL, NULL, false, 2011, '03/2011', 1, '2011-03-25 00:00:00', 0, 0, 0, 1, 'MATRICULA', 1, 2, 1, 6, 13);
INSERT INTO movproduto (situacao, quitado, datafinalvigencia, datainiciovigencia, apresentarmovproduto, anoreferencia, mesreferencia, responsavellancamento, datalancamento, totalfinal, valordesconto, precounitario, quantidade, descricao, empresa, pessoa, contrato, produto, codigo) VALUES ('PG', true, '2012-03-18 00:00:00', '2011-03-25 00:00:00', true, 2011, '03/2011', 1, '2011-03-25 00:00:00', 157.5, 0, 157.5, 1, 'PACTO 2011: MÉTODO DE GESTÃO - 03/2011', 1, 2, 1, 5, 1);
INSERT INTO movproduto (situacao, quitado, datafinalvigencia, datainiciovigencia, apresentarmovproduto, anoreferencia, mesreferencia, responsavellancamento, datalancamento, totalfinal, valordesconto, precounitario, quantidade, descricao, empresa, pessoa, contrato, produto, codigo) VALUES ('PG', true, '2012-03-18 00:00:00', '2011-03-25 00:00:00', true, 2011, '04/2011', 1, '2011-03-25 00:00:00', 157.5, 0, 157.5, 1, 'PACTO 2011: MÉTODO DE GESTÃO - 04/2011', 1, 2, 1, 5, 2);
INSERT INTO movproduto (situacao, quitado, datafinalvigencia, datainiciovigencia, apresentarmovproduto, anoreferencia, mesreferencia, responsavellancamento, datalancamento, totalfinal, valordesconto, precounitario, quantidade, descricao, empresa, pessoa, contrato, produto, codigo) VALUES ('PG', true, '2012-03-18 00:00:00', '2011-03-25 00:00:00', true, 2011, '05/2011', 1, '2011-03-25 00:00:00', 157.5, 0, 157.5, 1, 'PACTO 2011: MÉTODO DE GESTÃO - 05/2011', 1, 2, 1, 5, 3);
INSERT INTO movproduto (situacao, quitado, datafinalvigencia, datainiciovigencia, apresentarmovproduto, anoreferencia, mesreferencia, responsavellancamento, datalancamento, totalfinal, valordesconto, precounitario, quantidade, descricao, empresa, pessoa, contrato, produto, codigo) VALUES ('PG', true, '2012-03-18 00:00:00', '2011-03-25 00:00:00', true, 2011, '06/2011', 1, '2011-03-25 00:00:00', 157.5, 0, 157.5, 1, 'PACTO 2011: MÉTODO DE GESTÃO - 06/2011', 1, 2, 1, 5, 4);
INSERT INTO movproduto (situacao, quitado, datafinalvigencia, datainiciovigencia, apresentarmovproduto, anoreferencia, mesreferencia, responsavellancamento, datalancamento, totalfinal, valordesconto, precounitario, quantidade, descricao, empresa, pessoa, contrato, produto, codigo) VALUES ('PG', true, '2012-03-18 00:00:00', '2011-03-25 00:00:00', true, 2011, '07/2011', 1, '2011-03-25 00:00:00', 157.5, 0, 157.5, 1, 'PACTO 2011: MÉTODO DE GESTÃO - 07/2011', 1, 2, 1, 5, 5);
INSERT INTO movproduto (situacao, quitado, datafinalvigencia, datainiciovigencia, apresentarmovproduto, anoreferencia, mesreferencia, responsavellancamento, datalancamento, totalfinal, valordesconto, precounitario, quantidade, descricao, empresa, pessoa, contrato, produto, codigo) VALUES ('PG', true, '2012-03-18 00:00:00', '2011-03-25 00:00:00', true, 2011, '08/2011', 1, '2011-03-25 00:00:00', 157.5, 0, 157.5, 1, 'PACTO 2011: MÉTODO DE GESTÃO - 08/2011', 1, 2, 1, 5, 6);
INSERT INTO movproduto (situacao, quitado, datafinalvigencia, datainiciovigencia, apresentarmovproduto, anoreferencia, mesreferencia, responsavellancamento, datalancamento, totalfinal, valordesconto, precounitario, quantidade, descricao, empresa, pessoa, contrato, produto, codigo) VALUES ('PG', true, '2012-03-18 00:00:00', '2011-03-25 00:00:00', true, 2011, '09/2011', 1, '2011-03-25 00:00:00', 157.5, 0, 157.5, 1, 'PACTO 2011: MÉTODO DE GESTÃO - 09/2011', 1, 2, 1, 5, 7);
INSERT INTO movproduto (situacao, quitado, datafinalvigencia, datainiciovigencia, apresentarmovproduto, anoreferencia, mesreferencia, responsavellancamento, datalancamento, totalfinal, valordesconto, precounitario, quantidade, descricao, empresa, pessoa, contrato, produto, codigo) VALUES ('PG', true, '2012-03-18 00:00:00', '2011-03-25 00:00:00', true, 2011, '10/2011', 1, '2011-03-25 00:00:00', 157.5, 0, 157.5, 1, 'PACTO 2011: MÉTODO DE GESTÃƒO - 10/2011', 1, 2, 1, 5, 8);
INSERT INTO movproduto (situacao, quitado, datafinalvigencia, datainiciovigencia, apresentarmovproduto, anoreferencia, mesreferencia, responsavellancamento, datalancamento, totalfinal, valordesconto, precounitario, quantidade, descricao, empresa, pessoa, contrato, produto, codigo) VALUES ('PG', true, '2012-03-18 00:00:00', '2011-03-25 00:00:00', true, 2011, '11/2011', 1, '2011-03-25 00:00:00', 157.5, 0, 157.5, 1, 'PACTO 2011: MÉTODO DE GESTÃO - 11/2011', 1, 2, 1, 5, 9);
INSERT INTO movproduto (situacao, quitado, datafinalvigencia, datainiciovigencia, apresentarmovproduto, anoreferencia, mesreferencia, responsavellancamento, datalancamento, totalfinal, valordesconto, precounitario, quantidade, descricao, empresa, pessoa, contrato, produto, codigo) VALUES ('PG', true, '2012-03-18 00:00:00', '2011-03-25 00:00:00', true, 2012, '12/2011', 1, '2011-03-25 00:00:00', 157.5, 0, 157.5, 1, 'PACTO 2011: MÉTODO DE GESTÃO - 12/2011', 1, 2, 1, 5, 10);
INSERT INTO movproduto (situacao, quitado, datafinalvigencia, datainiciovigencia, apresentarmovproduto, anoreferencia, mesreferencia, responsavellancamento, datalancamento, totalfinal, valordesconto, precounitario, quantidade, descricao, empresa, pessoa, contrato, produto, codigo) VALUES ('PG', true, '2012-03-18 00:00:00', '2011-03-25 00:00:00', true, 2012, '01/2012', 1, '2011-03-25 00:00:00', 157.5, 0, 157.5, 1, 'PACTO 2011: MÉTODO DE GESTÃO - 01/2012', 1, 2, 1, 5, 11);
INSERT INTO movproduto (situacao, quitado, datafinalvigencia, datainiciovigencia, apresentarmovproduto, anoreferencia, mesreferencia, responsavellancamento, datalancamento, totalfinal, valordesconto, precounitario, quantidade, descricao, empresa, pessoa, contrato, produto, codigo) VALUES ('PG', true, '2012-03-18 00:00:00', '2011-03-25 00:00:00', true, 2012, '02/2012', 1, '2011-03-25 00:00:00', 157.5, 0, 157.5, 1, 'PACTO 2011: MÉTODO DE GESTÃO - 02/2012', 1, 2, 1, 5, 12);

INSERT INTO movprodutoparcela (valorpago, movparcela, recibopagamento, movproduto, codigo) VALUES (0, 1, 1, 13, 1);
INSERT INTO movprodutoparcela (valorpago, movparcela, recibopagamento, movproduto, codigo) VALUES (157.5, 1, 1, 1, 2);
INSERT INTO movprodutoparcela (valorpago, movparcela, recibopagamento, movproduto, codigo) VALUES (157.5, 2, 1, 2, 3);
INSERT INTO movprodutoparcela (valorpago, movparcela, recibopagamento, movproduto, codigo) VALUES (157.5, 3, 1, 3, 4);
INSERT INTO movprodutoparcela (valorpago, movparcela, recibopagamento, movproduto, codigo) VALUES (157.5, 4, 1, 4, 5);
INSERT INTO movprodutoparcela (valorpago, movparcela, recibopagamento, movproduto, codigo) VALUES (157.5, 5, 1, 5, 6);
INSERT INTO movprodutoparcela (valorpago, movparcela, recibopagamento, movproduto, codigo) VALUES (157.5, 6, 1, 6, 7);
INSERT INTO movprodutoparcela (valorpago, movparcela, recibopagamento, movproduto, codigo) VALUES (157.5, 7, 1, 7, 8);
INSERT INTO movprodutoparcela (valorpago, movparcela, recibopagamento, movproduto, codigo) VALUES (157.5, 8, 1, 8, 9);
INSERT INTO movprodutoparcela (valorpago, movparcela, recibopagamento, movproduto, codigo) VALUES (157.5, 9, 1, 9, 10);
INSERT INTO movprodutoparcela (valorpago, movparcela, recibopagamento, movproduto, codigo) VALUES (157.5, 10, 1, 10, 11);
INSERT INTO movprodutoparcela (valorpago, movparcela, recibopagamento, movproduto, codigo) VALUES (157.5, 11, 1, 11, 12);
INSERT INTO movprodutoparcela (valorpago, movparcela, recibopagamento, movproduto, codigo) VALUES (157.5, 12, 1, 12, 13);

INSERT INTO pagamentomovparcela (valorpago, recibopagamento, movparcela, movpagamento, codigo) VALUES (157.5, 1, 1, 1, 1);
INSERT INTO pagamentomovparcela (valorpago, recibopagamento, movparcela, movpagamento, codigo) VALUES (157.5, 1, 2, 2, 2);
INSERT INTO pagamentomovparcela (valorpago, recibopagamento, movparcela, movpagamento, codigo) VALUES (157.5, 1, 3, 2, 3);
INSERT INTO pagamentomovparcela (valorpago, recibopagamento, movparcela, movpagamento, codigo) VALUES (157.5, 1, 4, 2, 4);
INSERT INTO pagamentomovparcela (valorpago, recibopagamento, movparcela, movpagamento, codigo) VALUES (157.5, 1, 5, 2, 5);
INSERT INTO pagamentomovparcela (valorpago, recibopagamento, movparcela, movpagamento, codigo) VALUES (157.5, 1, 6, 2, 6);
INSERT INTO pagamentomovparcela (valorpago, recibopagamento, movparcela, movpagamento, codigo) VALUES (157.5, 1, 7, 2, 7);
INSERT INTO pagamentomovparcela (valorpago, recibopagamento, movparcela, movpagamento, codigo) VALUES (157.5, 1, 8, 2, 8);
INSERT INTO pagamentomovparcela (valorpago, recibopagamento, movparcela, movpagamento, codigo) VALUES (157.5, 1, 9, 3, 9);
INSERT INTO pagamentomovparcela (valorpago, recibopagamento, movparcela, movpagamento, codigo) VALUES (157.5, 1, 10, 3, 10);
INSERT INTO pagamentomovparcela (valorpago, recibopagamento, movparcela, movpagamento, codigo) VALUES (157.5, 1, 11, 3, 11);
INSERT INTO pagamentomovparcela (valorpago, recibopagamento, movparcela, movpagamento, codigo) VALUES (157.5, 1, 12, 4, 12);

INSERT INTO rotatividadeanaliticodw (situacao, peso, contrato, cliente, empresa, mes, ano, dia, codigo, fonecliente) VALUES ('MA', 1, 1, 1, 1, 3, 2011, '2011-03-25 10:30:21.836', 1, 'Celular: (62)9603-0540');

INSERT INTO situacaoclientesinteticodw (codigo, dia, codigocliente, matricula, nomecliente, datanascimento, idade, profissao, colaboradores, codigocontrato, situacao, duracaocontratomeses, mnemonicocontrato, nomeplano, valorfaturadocontrato, valorpagocontrato, valorparcabertocontrato, saldocontacorrentecliente, datavigenciade, datavigenciaate, datavigenciaateajustada, datalancamentocontrato, datarenovacaocontrato, datarematriculacontrato, dataultimobv, datamatricula, dataultimarematricula, diasassiduidadeultrematriculaatehoje, diasacessosemanapassada, diasfaltasemacesso, dataultimoacesso, faseatualcrm, dataultimocontatocrm, responsavelultimocontatocrm, codigoultimocontatocrm, situacaocontrato, tipoperiodoacesso, datainicioperiodoacesso, datafimperiodoacesso, diasacessosemana2, diasacessosemana3, diasacessosemana4) VALUES (1, '2011-03-25 00:00:00', 1, 1, 'AA_PRIMEIRO CLIENTE', '1987-11-02 00:00:00', 23, 'ADMINISTRADOR', 'CO-PACTO - MÉTODO DE GESTÃO/', 1, 'AT', 0, 'PPGM', '', 1890, 0, 1890, 0, '2011-03-25 00:00:00', '2012-03-24 00:00:00', '2012-03-24 00:00:00', '2011-03-25 10:30:06.587', NULL, NULL, '2011-03-10 00:00:00', '2011-03-25 00:00:00', NULL, 0, 0, 0, NULL, '', NULL, '', 0, 'NO', 'CA', '2011-03-25 00:00:00', '2012-03-24 00:00:00', 0, 0, 0);

INSERT INTO situacaocontratoanaliticodw (codigo, dia, situacao, empresa, plano, cliente, contrato, fonecliente, emailcliente, modalidadecliente, enderecocliente) VALUES (1, '2011-03-25 00:00:00', 'NO', 1, NULL, 1, NULL, 'Celular: (62)9603-0540', '<EMAIL>', '', 'RUA C 200 JARDIM AMÉRICA');

INSERT INTO situacaocontratosinteticodw (codigo, dia, situacao, empresa, peso, vinculocarteira, plano) VALUES (1, '2011-03-25 00:00:00', 'VI', 1, 0, '1', '');
INSERT INTO situacaocontratosinteticodw (codigo, dia, situacao, empresa, peso, vinculocarteira, plano) VALUES (2, '2011-03-25 10:30:21.896', 'NO', 1, 1, '1', '1');

INSERT INTO telefone (pessoa, tipotelefone, numero, codigo) VALUES (2, 'CO', '(62)3251-5820', 3);
INSERT INTO telefone (pessoa, tipotelefone, numero, codigo) VALUES (2, 'CE', '(62)9603-0540', 4);

------------------------------------Configuracoes do CRM e outros cadastros-----------------------------------------------------------------
INSERT INTO evento (codigo, descricao, status) VALUES (1, 'ANIVERSARIANTES DO MÊS', 'AT');
INSERT INTO evento (codigo, descricao, status) VALUES (2, 'AULÃO DE SABADO', 'AT');
INSERT INTO evento (codigo, descricao, status) VALUES (3, 'CARNAVAL', 'AT');
INSERT INTO evento (codigo, descricao, status) VALUES (4, 'FERIAS', 'AT');

INSERT INTO objecao (codigo, descricao, grupo, comentario, tipogrupo) VALUES (1, 'FALTA DE TEMPO', 'VISITANTES', '', 'OB');
INSERT INTO objecao (codigo, descricao, grupo, comentario, tipogrupo) VALUES (2, 'VAI FALAR COM FAMILIA', 'VISITANTES', '', 'OB');
INSERT INTO objecao (codigo, descricao, grupo, comentario, tipogrupo) VALUES (3, 'MAL ATENDIMENTO', 'EX-CLIENTES', '', 'OB');

INSERT INTO grupocolaborador (gerente, descricao, codigo, tipogrupo, situacaogrupo) VALUES (2, 'VENDAS', 1, 'CO', 'AT');
INSERT INTO grupocolaborador (gerente, descricao, codigo, tipogrupo, situacaogrupo) VALUES (2, 'PERSONAL', 3, 'PI', 'AT');
INSERT INTO grupocolaborador (gerente, descricao, codigo, tipogrupo, situacaogrupo) VALUES (2, 'PROFESSORES', 2, 'PR', 'AT');

INSERT INTO configuracaosistemacrm (codigo, remetentepadrao, emailpadrao, mailserver, login, senha, abertosabado, abertodomingo, nrfaltaplanomensal, nrfaltaplanotrimestral, nrfaltaplanoacimasemestral, nrdiasparaclientepreverenovacao, nrdiasparaclientepreveperda, nrrisco, conexaosegura) VALUES (1, 'Ex: o nome da academia', '', '', '<EMAIL>', '', true, false, 4, 8, 16, 10, 10, 6, false);

INSERT INTO configuracaodiasposvenda (codigo, configuracaosistemacrm, nrdia, descricao) VALUES (1, 1, 1, 'BEM VINDO - MAIS INFORMAÇÕES DA ACADEMIA');
INSERT INTO configuracaodiasposvenda (codigo, configuracaosistemacrm, nrdia, descricao) VALUES (2, 1, 15, 'ORIENTAÇÃO - AVALIAÇÃO FÍSICA E INDICAÇÃO');
INSERT INTO configuracaodiasposvenda (codigo, configuracaosistemacrm, nrdia, descricao) VALUES (3, 1, 30, 'PRIMEIROS RESUTADOS - METAS E OBJETIVOS');
INSERT INTO configuracaodiasposvenda (codigo, configuracaosistemacrm, nrdia, descricao) VALUES (4, 1, 45, 'INTEGRAÇÃO');
INSERT INTO configuracaodiasposvenda (codigo, configuracaosistemacrm, nrdia, descricao) VALUES (5, 1, 60, 'VARIAR PROGRAMAÇÃO');
INSERT INTO configuracaodiasposvenda (codigo, configuracaosistemacrm, nrdia, descricao) VALUES (6, 1, 80, 'AMIGO OU CONVIDADO');
INSERT INTO configuracaodiasposvenda (codigo, configuracaosistemacrm, nrdia, descricao) VALUES (7, 1, 100, 'ENVOLVIMENTO - ATIVIDADES EXTRAS - DESAFIOS');
INSERT INTO configuracaodiasposvenda (codigo, configuracaosistemacrm, nrdia, descricao) VALUES (8, 1, 150, 'REORIENTAÇÃO ( NOVAS ATIVIDADES, COMPETIÇÃO )');


-------------------------------------logs-----------------------------------------------------

INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Duração do Plano - Plano Condição Pagamento', '1', '1', '', '0', '1', '2010-12-23 18:21:17.621', 'Administrador', 'ALTERAÇÃO', 1);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Duração do Plano - Plano Condição Pagamento', '1', '2', '', '0', '1', '2010-12-23 18:21:17.621', 'Administrador', 'ALTERAÇÃO', 2);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Duração do Plano - Plano Condição Pagamento', '1', '3', '', '0', '2', '2010-12-23 18:21:17.621', 'Administrador', 'ALTERAÇÃO', 3);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Duração do Plano - Plano Condição Pagamento', '1', '4', '', '0', '3', '2010-12-23 18:21:17.621', 'Administrador', 'ALTERAÇÃO', 4);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOHORARIO', 'Plano - Horários do Plano', '1', '', '* Horário:', '1', '2', '2010-12-23 18:21:17.621', 'Administrador', 'ALTERAÇÃO', 5);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Produtos Sugeridos', '1', '4', '', 'Campo(s) 
Código: 4
Obrigatório: false
 false
Produto: 6
Valor do Produto para esse Plano: 30.0
', '', '2010-12-23 18:21:17.631', 'Administrador', 'INCLUSÃO', 6);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Produtos Sugeridos', '1', '3', '', 'Campo(s) 
Código: 3
Obrigatório: false
 false
Produto: 9
Valor do Produto para esse Plano: -5.0
', '', '2010-12-23 18:21:17.631', 'Administrador', 'EXCLUSÃO', 7);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('COLABORADOR', 'Colaborador', '1', '', '', '', '', '2011-02-03 13:21:27.39', 'Administrador', 'ALTERAÇÃO', 8);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('COLABORADOR', 'Colaborador', '1', '', '', '', '', '2011-02-03 13:21:27.39', 'Administrador', 'ALTERAÇÃO', 9);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('COLABORADOR', 'Colaborador', '1', '', '', '', '', '2011-02-03 13:21:27.39', 'Administrador', 'ALTERAÇÃO', 10);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('COLABORADOR', 'Colaborador', '1', '', '', '', '', '2011-02-03 13:21:27.39', 'Administrador', 'ALTERAÇÃO', 11);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('COLABORADOR', 'Colaborador', '1', '', '', '', '', '2011-02-03 13:21:27.39', 'Administrador', 'ALTERAÇÃO', 12);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('COLABORADOR', 'Colaborador', '1', '', '', '', '', '2011-02-03 13:21:27.39', 'Administrador', 'ALTERAÇÃO', 13);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('COLABORADOR - TIPOCOLABORADOR', '', '1', '1', '', 'Campo(s) 
 1
 CO
', '', '2011-02-03 13:21:27.427', 'Administrador', 'INCLUSÃO', 14);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('COLABORADOR', 'Pessoa', '1', '', 'Nome:', 'QUALQUER PROFESSOR', 'PACTO - MÉTODO DE GESTÃO', '2011-02-03 13:21:27.499', 'Administrador', 'ALTERAÇÃO', 15);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('COLABORADOR', 'Pessoa', '1', '', 'Nacionalidade:', '', 'BRASILEIRA', '2011-02-03 13:21:27.499', 'Administrador', 'ALTERAÇÃO', 16);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('COLABORADOR', 'Pessoa', '1', '', 'Web Page:', '', 'WWW.PACTOSOLUCOES.COM.BR', '2011-02-03 13:21:27.499', 'Administrador', 'ALTERAÇÃO', 17);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('COLABORADOR', 'Pessoa', '1', '', '', '0', '1', '2011-02-03 13:21:27.499', 'Administrador', 'ALTERAÇÃO', 18);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('COLABORADOR', 'Pessoa', '1', '', 'Profissão:', '0', '4', '2011-02-03 13:21:27.499', 'Administrador', 'ALTERAÇÃO', 19);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('COLABORADOR', 'Pessoa', '1', '', 'Cidade:', '', '1', '2011-02-03 13:21:27.499', 'Administrador', 'ALTERAÇÃO', 20);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('COLABORADOR', 'Pessoa', '1', '', 'País:', '', '1', '2011-02-03 13:21:27.499', 'Administrador', 'ALTERAÇÃO', 21);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('COLABORADOR', 'Pessoa', '1', '', 'Grau de Instrução:', '', '0', '2011-02-03 13:21:27.499', 'Administrador', 'ALTERAÇÃO', 22);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('COLABORADOR', 'Telefone:', '1', '1', '', 'Campo(s) 
Código: 1
Número: (62)3251-5820
Tipo do Telefone: CO
', '', '2011-02-03 13:22:08.079', 'Administrador', 'INCLUSÃO',23);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('COLABORADOR', 'Telefone:', '1', '2', '', 'Campo(s) 
Código: 2
Número: (62)9603-0540
Tipo do Telefone: CE
', '', '2011-02-03 13:22:08.079', 'Administrador', 'INCLUSÃO',24);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '1', '', '* Valor Mensal:', '95.0', '120.0', '2011-02-03 14:24:12.747', 'Administrador', 'ALTERAÇÃO', 25);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '1', '', 'Modalidade Padrão:', 'Sim', 'Não', '2011-02-03 14:24:12.747', 'Administrador', 'ALTERAÇÃO', 26);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE - PRODUTOSUGERIDO', 'Produto Sugerido', '1', '1', '', 'Campo(s) 
Código: 1
Obrigatório: false
Produto: 20
', '', '2011-02-03 14:24:12.747', 'Administrador', 'INCLUSÃO', 27);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '1', '', '* Número de Vezes por Semana:', '7', '6', '2011-02-03 14:24:24.607', 'Administrador', 'ALTERAÇÃO', 28);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '2', '', '* Número de Vezes por Semana:', '7', '6', '2011-02-03 14:25:08.412', 'Administrador', 'ALTERAÇÃO', 29);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '2', '', '* Nome:', 'GINASTICA', 'GINÁSTICA', '2011-02-03 14:25:08.412', 'Administrador', 'ALTERAÇÃO', 30);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '2', '', 'Modalidade Padrão:', 'Sim', 'Não', '2011-02-03 14:25:08.412', 'Administrador', 'ALTERAÇÃO', 31);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '2', '', '* Valor Mensal:', '95.0', '100.0', '2011-02-03 14:25:21.423', 'Administrador', 'ALTERAÇÃO', 32);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '3', '', '* Número de Vezes por Semana:', '7', '6', '2011-02-03 14:25:53.303', 'Administrador', 'ALTERAÇÃO', 33);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '3', '', '* Nome:', 'ERGOMETRIA', 'PILATES', '2011-02-03 14:25:53.303', 'Administrador', 'ALTERAÇÃO', 34);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '3', '', '* Valor Mensal:', '95.0', '135.0', '2011-02-03 14:25:53.303', 'Administrador', 'ALTERAÇÃO', 35);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '3', '', 'Modalidade Padrão:', 'Sim', 'Não', '2011-02-03 14:25:53.303', 'Administrador', 'ALTERAÇÃO', 36);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '6', '', '* Número de Vezes por Semana:', '2', '3', '2011-02-03 14:26:23.528', 'Administrador', 'ALTERAÇÃO', 37);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '6', '', '* Nome:', 'KIDS', 'DANÇA ', '2011-02-03 14:26:23.528', 'Administrador', 'ALTERAÇÃO', 38);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '6', '', 'Utilizar Turma:', 'Sim', 'Não', '2011-02-03 14:26:23.528', 'Administrador', 'ALTERAÇÃO', 39);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '4', '', '* Número de Vezes por Semana:', '2', '3', '2011-02-03 14:27:10.699', 'Administrador', 'ALTERAÇÃO', 40);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '4', '', '* Nome:', 'NATAÇÃO ADULTO', 'LUTAS', '2011-02-03 14:27:10.699', 'Administrador', 'ALTERAÇÃO', 41);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '4', '', '* Valor Mensal:', '100.0', '69.0', '2011-02-03 14:27:10.699', 'Administrador', 'ALTERAÇÃO', 42);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '4', '', 'Utilizar Turma:', 'Sim', 'Não', '2011-02-03 14:27:10.699', 'Administrador', 'ALTERAÇÃO', 43);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE - PRODUTOSUGERIDO', 'Produto Sugerido', '4', '2', '', 'Campo(s) 
Código: 2
Obrigatório: false
Produto: 21
', '', '2011-02-03 14:28:05.147', 'Administrador', 'INCLUSÃO', 44);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO', 'Plano', '1', '', 'Permite Acesso Horário Turma:', 'Sim', 'Não', '2011-02-03 14:34:24.179', 'Administrador', 'ALTERAÇÃO', 45);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO', 'Plano', '1', '', '* Texto Recibo:', '1', '2', '2011-02-03 14:34:24.179', 'Administrador', 'ALTERAÇÃO', 46);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOCOMPOSICAO', 'Pacote do Plano', '1', '2', '', 'Campo(s) 
Código: 2
* Pacote: 1
', '', '2011-02-03 14:34:25.109', 'Administrador', 'INCLUSÃO', 47);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOCOMPOSICAO', 'Pacote do Plano', '1', '1', '', 'Campo(s) 
Código: 1
* Pacote: 1
', '', '2011-02-03 14:34:25.109', 'Administrador', 'EXCLUSÃO', 48);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOMODALIDADE', 'Plano Modalidade', '1', '5', '', 'Campo(s) 
Código: 5
* Modalidade: 1
 [negocio.comuns.plano.PlanoModalidadeVezesSemanaVO@1e26f16]
', '', '2011-02-03 14:34:25.109', 'Administrador', 'INCLUSÃO', 49);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOMODALIDADE', 'Plano Modalidade', '1', '6', '', 'Campo(s) 
Código: 6
* Modalidade: 2
 [negocio.comuns.plano.PlanoModalidadeVezesSemanaVO@eb2f24]
', '', '2011-02-03 14:34:25.109', 'Administrador', 'INCLUSÃO', 50);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOMODALIDADE', 'Plano Modalidade', '1', '7', '', 'Campo(s) 
Código: 7
* Modalidade: 3
 [negocio.comuns.plano.PlanoModalidadeVezesSemanaVO@642281]
', '', '2011-02-03 14:34:25.109', 'Administrador', 'INCLUSÃO', 51);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOMODALIDADE', 'Plano Modalidade', '1', '8', '', 'Campo(s) 
Código: 8
* Modalidade: 6
 [negocio.comuns.plano.PlanoModalidadeVezesSemanaVO@154fa9]
', '', '2011-02-03 14:34:25.109', 'Administrador', 'INCLUSÃO', 52);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOMODALIDADE', 'Plano Modalidade', '1', '1', '', 'Campo(s) 
Código: 1
* Modalidade: 1
 [negocio.comuns.plano.PlanoModalidadeVezesSemanaVO@1210c66]
', '', '2011-02-03 14:34:25.109', 'Administrador', 'EXCLUSÃO', 53);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOMODALIDADE', 'Plano Modalidade', '1', '2', '', 'Campo(s) 
Código: 2
* Modalidade: 2
 [negocio.comuns.plano.PlanoModalidadeVezesSemanaVO@b50553]
', '', '2011-02-03 14:34:25.109', 'Administrador', 'EXCLUSÃO', 54);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOMODALIDADE', 'Plano Modalidade', '1', '3', '', 'Campo(s) 
Código: 3
* Modalidade: 3
 [negocio.comuns.plano.PlanoModalidadeVezesSemanaVO@1fa04b6]
', '', '2011-02-03 14:34:25.109', 'Administrador', 'EXCLUSÃO', 55);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOMODALIDADE', 'Plano Modalidade', '1', '4', '', 'Campo(s) 
Código: 4
* Modalidade: 6
 [negocio.comuns.plano.PlanoModalidadeVezesSemanaVO@14b6b97, negocio.comuns.plano.PlanoModalidadeVezesSemanaVO@84b139]
', '', '2011-02-03 14:34:25.109', 'Administrador', 'EXCLUSÃO', 56);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '5', '', '* Número de Vezes por Semana:', '2', '3', '2011-02-03 14:36:09.341', 'Administrador', 'ALTERAÇÃO', 57);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '5', '', '* Nome:', 'NATACAO INFANTIL', 'PISCINA', '2011-02-03 14:36:09.341', 'Administrador', 'ALTERAÇÃO', 58);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('MODALIDADE', 'Modalidade', '5', '', '* Número de Vezes por Semana:', '3', '4', '2011-02-03 14:36:15.221', 'Administrador', 'ALTERAÇÃO', 59);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANOMODALIDADE - PLANOMODALIDADEVEZESSEMANA', 'Plano Modalidade Vezes Semana', '1', '10', '', 'Campo(s) 
Código: 10
* Número de Vezes Por Semana: 6
Forma de Cálculo: 
* Percentual: 0.0
* Valor: 0.0
Tipo de Operação: 
', '', '2011-02-03 14:38:58.436', 'Administrador', 'INCLUSÃO', 60);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANOMODALIDADE - PLANOMODALIDADEVEZESSEMANA', 'Plano Modalidade Vezes Semana', '1', '6', '', 'Campo(s) 
Código: 6
* Número de Vezes Por Semana: 6
Forma de Cálculo: 
* Percentual: 0.0
* Valor: 0.0
Tipo de Operação: 
', '', '2011-02-03 14:38:58.436', 'Administrador', 'EXCLUSÃO', 61);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOMODALIDADE', 'Plano - Plano Modalidade', '1', '', '* Modalidade:', '2', '1', '2011-02-03 14:38:58.436', 'Administrador', 'ALTERAÇÃO', 62);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANOMODALIDADE - PLANOMODALIDADEVEZESSEMANA', 'Plano Modalidade Vezes Semana', '1', '10', '', 'Campo(s) 
Código: 10
* Número de Vezes Por Semana: 6
Forma de Cálculo: 
* Percentual: 0.0
* Valor: 0.0
Tipo de Operação: 
', '', '2011-02-03 14:38:58.436', 'Administrador', 'INCLUSÃO', 63);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANOMODALIDADE - PLANOMODALIDADEVEZESSEMANA', 'Plano Modalidade Vezes Semana', '1', '7', '', 'Campo(s) 
Código: 7
* Número de Vezes Por Semana: 6
Forma de Cálculo: 
* Percentual: 0.0
* Valor: 0.0
Tipo de Operação: 
', '', '2011-02-03 14:38:58.436', 'Administrador', 'EXCLUSÃO', 64);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOMODALIDADE', 'Plano - Plano Modalidade', '1', '', '* Modalidade:', '3', '1', '2011-02-03 14:38:58.436', 'Administrador', 'ALTERAÇÃO', 65);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANOMODALIDADE - PLANOMODALIDADEVEZESSEMANA', 'Plano Modalidade Vezes Semana', '1', '10', '', 'Campo(s) 
Código: 10
* Número de Vezes Por Semana: 6
Forma de Cálculo: 
* Percentual: 0.0
* Valor: 0.0
Tipo de Operação: 
', '', '2011-02-03 14:38:58.436', 'Administrador', 'INCLUSÃO', 66);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANOMODALIDADE - PLANOMODALIDADEVEZESSEMANA', 'Plano Modalidade Vezes Semana', '1', '8', '', 'Campo(s) 
Código: 8
* Número de Vezes Por Semana: 6
Forma de Cálculo: 
* Percentual: 0.0
* Valor: 0.0
Tipo de Operação: 
', '', '2011-02-03 14:38:58.436', 'Administrador', 'EXCLUSÃO', 67);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOMODALIDADE', 'Plano - Plano Modalidade', '1', '', '* Modalidade:', '6', '1', '2011-02-03 14:38:58.436', 'Administrador', 'ALTERAÇÃO', 68);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANOMODALIDADE - PLANOMODALIDADEVEZESSEMANA', 'Plano Modalidade Vezes Semana', '1', '10', '', 'Campo(s) 
Código: 10
* Número de Vezes Por Semana: 6
Forma de Cálculo: 
* Percentual: 0.0
* Valor: 0.0
Tipo de Operação: 
', '', '2011-02-03 14:38:58.436', 'Administrador', 'INCLUSÃO', 69);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANOMODALIDADE - PLANOMODALIDADEVEZESSEMANA', 'Plano Modalidade Vezes Semana', '1', '9', '', 'Campo(s) 
Código: 9
* Número de Vezes Por Semana: 3
Forma de Cálculo: 
* Percentual: 0.0
* Valor: 0.0
Tipo de Operação: 
', '', '2011-02-03 14:38:58.436', 'Administrador', 'EXCLUSÃO', 70);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANOMODALIDADE - PLANOMODALIDADEVEZESSEMANA', 'Plano Modalidade Vezes Semana', '1', '11', '', 'Campo(s) 
Código: 11
* Número de Vezes Por Semana: 6
Forma de Cálculo: 
* Percentual: 0.0
* Valor: 0.0
Tipo de Operação: 
', '', '2011-02-03 14:38:58.436', 'Administrador', 'INCLUSÃO', 71);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANOMODALIDADE - PLANOMODALIDADEVEZESSEMANA', 'Plano Modalidade Vezes Semana', '1', '7', '', 'Campo(s) 
Código: 7
* Número de Vezes Por Semana: 6
Forma de Cálculo: 
* Percentual: 0.0
* Valor: 0.0
Tipo de Operação: 
', '', '2011-02-03 14:38:58.436', 'Administrador', 'EXCLUSÃO', 72);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOMODALIDADE', 'Plano - Plano Modalidade', '1', '', '* Modalidade:', '3', '2', '2011-02-03 14:38:58.436', 'Administrador', 'ALTERAÇÃO', 73);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANOMODALIDADE - PLANOMODALIDADEVEZESSEMANA', 'Plano Modalidade Vezes Semana', '1', '11', '', 'Campo(s) 
Código: 11
* Número de Vezes Por Semana: 6
Forma de Cálculo: 
* Percentual: 0.0
* Valor: 0.0
Tipo de Operação: 
', '', '2011-02-03 14:38:58.436', 'Administrador', 'INCLUSÃO', 74);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANOMODALIDADE - PLANOMODALIDADEVEZESSEMANA', 'Plano Modalidade Vezes Semana', '1', '8', '', 'Campo(s) 
Código: 8
* Número de Vezes Por Semana: 6
Forma de Cálculo: 
* Percentual: 0.0
* Valor: 0.0
Tipo de Operação: 
', '', '2011-02-03 14:38:58.436', 'Administrador', 'EXCLUSÃO', 75);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOMODALIDADE', 'Plano - Plano Modalidade', '1', '', '* Modalidade:', '6', '2', '2011-02-03 14:38:58.436', 'Administrador', 'ALTERAÇÃO', 76);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANOMODALIDADE - PLANOMODALIDADEVEZESSEMANA', 'Plano Modalidade Vezes Semana', '1', '11', '', 'Campo(s) 
Código: 11
* Número de Vezes Por Semana: 6
Forma de Cálculo: 
* Percentual: 0.0
* Valor: 0.0
Tipo de Operação: 
', '', '2011-02-03 14:38:58.436', 'Administrador', 'INCLUSÃO', 77);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANOMODALIDADE - PLANOMODALIDADEVEZESSEMANA', 'Plano Modalidade Vezes Semana', '1', '9', '', 'Campo(s) 
Código: 9
* Número de Vezes Por Semana: 3
Forma de Cálculo: 
* Percentual: 0.0
* Valor: 0.0
Tipo de Operação: 
', '', '2011-02-03 14:38:58.446', 'Administrador', 'EXCLUSÃO', 78);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANOMODALIDADE - PLANOMODALIDADEVEZESSEMANA', 'Plano Modalidade Vezes Semana', '1', '12', '', 'Campo(s) 
Código: 12
* Número de Vezes Por Semana: 6
Forma de Cálculo: 
* Percentual: 0.0
* Valor: 0.0
Tipo de Operação: 
', '', '2011-02-03 14:38:58.446', 'Administrador', 'INCLUSÃO', 79);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANOMODALIDADE - PLANOMODALIDADEVEZESSEMANA', 'Plano Modalidade Vezes Semana', '1', '8', '', 'Campo(s) 
Código: 8
* Número de Vezes Por Semana: 6
Forma de Cálculo: 
* Percentual: 0.0
* Valor: 0.0
Tipo de Operação: 
', '', '2011-02-03 14:38:58.446', 'Administrador', 'EXCLUSÃO', 80);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOMODALIDADE', 'Plano - Plano Modalidade', '1', '', '* Modalidade:', '6', '3', '2011-02-03 14:38:58.446', 'Administrador', 'ALTERAÇÃO', 81);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANOMODALIDADE - PLANOMODALIDADEVEZESSEMANA', 'Plano Modalidade Vezes Semana', '1', '12', '', 'Campo(s) 
Código: 12
* Número de Vezes Por Semana: 6
Forma de Cálculo: 
* Percentual: 0.0
* Valor: 0.0
Tipo de Operação: 
', '', '2011-02-03 14:38:58.446', 'Administrador', 'INCLUSÃO', 82);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANOMODALIDADE - PLANOMODALIDADEVEZESSEMANA', 'Plano Modalidade Vezes Semana', '1', '9', '', 'Campo(s) 
Código: 9
* Número de Vezes Por Semana: 3
Forma de Cálculo: 
* Percentual: 0.0
* Valor: 0.0
Tipo de Operação: 
', '', '2011-02-03 14:38:58.446', 'Administrador', 'EXCLUSÃO', 83);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANOMODALIDADE - PLANOMODALIDADEVEZESSEMANA', 'Plano Modalidade Vezes Semana', '1', '13', '', 'Campo(s) 
Código: 13
* Número de Vezes Por Semana: 3
Forma de Cálculo: 
* Percentual: 0.0
* Valor: 0.0
Tipo de Operação: 
', '', '2011-02-03 14:38:58.446', 'Administrador', 'INCLUSÃO', 84);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANOMODALIDADE - PLANOMODALIDADEVEZESSEMANA', 'Plano Modalidade Vezes Semana', '1', '9', '', 'Campo(s) 
Código: 9
* Número de Vezes Por Semana: 3
Forma de Cálculo: 
* Percentual: 0.0
* Valor: 0.0
Tipo de Operação: 
', '', '2011-02-03 14:38:58.446', 'Administrador', 'EXCLUSÃO', 85);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOMODALIDADE', 'Plano Modalidade', '1', '9', '', 'Campo(s) 
Código: 9
* Modalidade: 5
 [negocio.comuns.plano.PlanoModalidadeVezesSemanaVO@1dc9535, negocio.comuns.plano.PlanoModalidadeVezesSemanaVO@c7d1af, negocio.comuns.plano.PlanoModalidadeVezesSemanaVO@973eb7]
', '', '2011-02-03 14:38:58.446', 'Administrador', 'INCLUSÃO', 86);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO', 'Plano', '1', '', 'Permite Acesso Horário Turma:', 'Não', 'Sim', '2011-02-03 14:39:07.246', 'Administrador', 'ALTERAÇÃO', 87);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Duração do Plano', '1', '3', '', 'Campo(s) 
Código: 3
* Número Máximo de Parcelas da Condição de Pagamento: 1
* Número de Meses: 1
 0
 PD
Tipo da Operação: AC
* Percentual: 0.0
* Valor: 0.0
 [negocio.comuns.plano.PlanoCondicaoPagamentoVO@1374664, negocio.comuns.plano.PlanoCondicaoPagamentoVO@1ee6a87]
', '', '2011-02-03 15:00:49.948', 'Administrador', 'INCLUSÃO', 88);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Duração do Plano', '1', '4', '', 'Campo(s) 
Código: 4
* Número Máximo de Parcelas da Condição de Pagamento: 3
* Número de Meses: 3
 0
 PD
Tipo da Operação: RE
* Percentual: 20.0
* Valor: 0.0
 [negocio.comuns.plano.PlanoCondicaoPagamentoVO@17a8d85, negocio.comuns.plano.PlanoCondicaoPagamentoVO@e143a1, negocio.comuns.plano.PlanoCondicaoPagamentoVO@11f1712, negocio.comuns.plano.PlanoCondicaoPagamentoVO@23c338]
', '', '2011-02-03 15:00:49.948', 'Administrador', 'INCLUSÃO', 89);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Duração do Plano', '1', '5', '', 'Campo(s) 
Código: 5
* Número Máximo de Parcelas da Condição de Pagamento: 6
* Número de Meses: 6
 0
 PD
Tipo da Operação: RE
* Percentual: 25.0
* Valor: 0.0
 [negocio.comuns.plano.PlanoCondicaoPagamentoVO@875021, negocio.comuns.plano.PlanoCondicaoPagamentoVO@19db943, negocio.comuns.plano.PlanoCondicaoPagamentoVO@1edf621, negocio.comuns.plano.PlanoCondicaoPagamentoVO@1b9feda, negocio.comuns.plano.PlanoCondicaoPagamentoVO@aac89, negocio.comuns.plano.PlanoCondicaoPagamentoVO@c55a7e, negocio.comuns.plano.PlanoCondicaoPagamentoVO@b84216]
', '', '2011-02-03 15:00:49.948', 'Administrador', 'INCLUSÃO', 90);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Duração do Plano', '1', '6', '', 'Campo(s) 
Código: 6
* Número Máximo de Parcelas da Condição de Pagamento: 9
* Número de Meses: 9
 0
 PD
Tipo da Operação: RE
* Percentual: 28.0
* Valor: 0.0
 [negocio.comuns.plano.PlanoCondicaoPagamentoVO@fad7a6, negocio.comuns.plano.PlanoCondicaoPagamentoVO@63f083, negocio.comuns.plano.PlanoCondicaoPagamentoVO@14357d8, negocio.comuns.plano.PlanoCondicaoPagamentoVO@3c3904, negocio.comuns.plano.PlanoCondicaoPagamentoVO@1c36b12, negocio.comuns.plano.PlanoCondicaoPagamentoVO@aa9093, negocio.comuns.plano.PlanoCondicaoPagamentoVO@1f9c202, negocio.comuns.plano.PlanoCondicaoPagamentoVO@30438e, negocio.comuns.plano.PlanoCondicaoPagamentoVO@a34ad0, negocio.comuns.plano.PlanoCondicaoPagamentoVO@84b495]
', '', '2011-02-03 15:00:49.948', 'Administrador', 'INCLUSÃO', 91);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Duração do Plano', '1', '7', '', 'Campo(s) 
Código: 7
* Número Máximo de Parcelas da Condição de Pagamento: 12
* Número de Meses: 12
 0
 PD
Tipo da Operação: RE
* Percentual: 30.0
* Valor: 0.0
 [negocio.comuns.plano.PlanoCondicaoPagamentoVO@6bacb9, negocio.comuns.plano.PlanoCondicaoPagamentoVO@118b028, negocio.comuns.plano.PlanoCondicaoPagamentoVO@1d5272c, negocio.comuns.plano.PlanoCondicaoPagamentoVO@fec3e0, negocio.comuns.plano.PlanoCondicaoPagamentoVO@1edaed0, negocio.comuns.plano.PlanoCondicaoPagamentoVO@1701733, negocio.comuns.plano.PlanoCondicaoPagamentoVO@32043, negocio.comuns.plano.PlanoCondicaoPagamentoVO@13b492f, negocio.comuns.plano.PlanoCondicaoPagamentoVO@141c2d5, negocio.comuns.plano.PlanoCondicaoPagamentoVO@5e4f6b, negocio.comuns.plano.PlanoCondicaoPagamentoVO@1b01cde, negocio.comuns.plano.PlanoCondicaoPagamentoVO@13738d0, negocio.comuns.plano.PlanoCondicaoPagamentoVO@730c6b]
', '', '2011-02-03 15:00:49.958', 'Administrador', 'INCLUSÃO', 92);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Duração do Plano', '1', '8', '', 'Campo(s) 
Código: 8
* Número Máximo de Parcelas da Condição de Pagamento: 15
* Número de Meses: 15
 0
 PD
Tipo da Operação: RE
* Percentual: 36.0
* Valor: 0.0
 [negocio.comuns.plano.PlanoCondicaoPagamentoVO@1347b66, negocio.comuns.plano.PlanoCondicaoPagamentoVO@9179a5, negocio.comuns.plano.PlanoCondicaoPagamentoVO@c9743e, negocio.comuns.plano.PlanoCondicaoPagamentoVO@1eebffc, negocio.comuns.plano.PlanoCondicaoPagamentoVO@17e4296, negocio.comuns.plano.PlanoCondicaoPagamentoVO@45afa6, negocio.comuns.plano.PlanoCondicaoPagamentoVO@10ed826, negocio.comuns.plano.PlanoCondicaoPagamentoVO@190dba5, negocio.comuns.plano.PlanoCondicaoPagamentoVO@1446f10, negocio.comuns.plano.PlanoCondicaoPagamentoVO@1d7c4b6, negocio.comuns.plano.PlanoCondicaoPagamentoVO@b3bcf6, negocio.comuns.plano.PlanoCondicaoPagamentoVO@3ad78f, negocio.comuns.plano.PlanoCondicaoPagamentoVO@11dfdd0]
', '', '2011-02-03 15:00:49.958', 'Administrador', 'INCLUSÃO', 93);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Duração do Plano', '1', '1', '', 'Campo(s) 
Código: 1
* Número Máximo de Parcelas da Condição de Pagamento: 1
* Número de Meses: 1
 0
 PD
Tipo da Operação: AC
* Percentual: 0.0
* Valor: 0.0
 [negocio.comuns.plano.PlanoCondicaoPagamentoVO@29fc8]
', '', '2011-02-03 15:00:49.958', 'Administrador', 'EXCLUSÃO', 94);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Duração do Plano', '1', '2', '', 'Campo(s) 
Código: 2
* Número Máximo de Parcelas da Condição de Pagamento: 3
* Número de Meses: 3
 0
 PD
Tipo da Operação: RE
* Percentual: 5.2631578
* Valor: 0.0
 [negocio.comuns.plano.PlanoCondicaoPagamentoVO@482d88, negocio.comuns.plano.PlanoCondicaoPagamentoVO@a55167, negocio.comuns.plano.PlanoCondicaoPagamentoVO@18376dd]
', '', '2011-02-03 15:00:49.958', 'Administrador', 'EXCLUSÃO', 95);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Número Máximo de Parcelas da Condição de Pagamento:', '9', '1', '2011-02-03 15:01:42.13', 'Administrador', 'ALTERAÇÃO', 96);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Número de Meses:', '9', '1', '2011-02-03 15:01:42.13', 'Administrador', 'ALTERAÇÃO', 97);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', 'Tipo da Operação:', 'RE', 'AC', '2011-02-03 15:01:42.13', 'Administrador', 'ALTERAÇÃO', 98);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Percentual:', '28.0', '0.0', '2011-02-03 15:01:42.13', 'Administrador', 'ALTERAÇÃO', 99);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '5', '', 'Campo(s) 
Código: 5
* Percentual: 5.0
* Condição de Pagamento: 
 PD
Tipo Operação: RE
* Valor: 0.0
 1
', '', '2011-02-03 15:01:42.13', 'Administrador', 'INCLUSÃO', 100);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '6', '', 'Campo(s) 
Código: 6
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 1
', '', '2011-02-03 15:01:42.13', 'Administrador', 'INCLUSÃO', 101);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '18', '', 'Campo(s) 
Código: 18
* Percentual: 5.0
* Condição de Pagamento: 
 PD
Tipo Operação: RE
* Valor: 0.0
 1
', '', '2011-02-03 15:01:42.13', 'Administrador', 'EXCLUSÃO', 102);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '19', '', 'Campo(s) 
Código: 19
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 1
', '', '2011-02-03 15:01:42.13', 'Administrador', 'EXCLUSÃO', 103);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '20', '', 'Campo(s) 
Código: 20
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 2
', '', '2011-02-03 15:01:42.13', 'Administrador', 'EXCLUSÃO', 104);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '21', '', 'Campo(s) 
Código: 21
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 3
', '', '2011-02-03 15:01:42.13', 'Administrador', 'EXCLUSÃO', 105);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '22', '', 'Campo(s) 
Código: 22
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 4
', '', '2011-02-03 15:01:42.13', 'Administrador', 'EXCLUSÃO', 106);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '23', '', 'Campo(s) 
Código: 23
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 5
', '', '2011-02-03 15:01:42.13', 'Administrador', 'EXCLUSÃO', 107);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '24', '', 'Campo(s) 
Código: 24
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 6
', '', '2011-02-03 15:01:42.13', 'Administrador', 'EXCLUSÃO', 108);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '25', '', 'Campo(s) 
Código: 25
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 7
', '', '2011-02-03 15:01:42.13', 'Administrador', 'EXCLUSÃO', 109);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '26', '', 'Campo(s) 
Código: 26
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 8
', '', '2011-02-03 15:01:42.13', 'Administrador', 'EXCLUSÃO', 110);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '27', '', 'Campo(s) 
Código: 27
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 9
', '', '2011-02-03 15:01:42.13', 'Administrador', 'EXCLUSÃO', 111);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Número Máximo de Parcelas da Condição de Pagamento:', '9', '1', '2011-02-03 15:02:34.54', 'Administrador', 'ALTERAÇÃO', 112);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Número de Meses:', '9', '1', '2011-02-03 15:02:34.54', 'Administrador', 'ALTERAÇÃO', 113);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', 'Tipo da Operação:', 'RE', 'AC', '2011-02-03 15:02:34.54', 'Administrador', 'ALTERAÇÃO', 114);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Percentual:', '28.0', '0.0', '2011-02-03 15:02:34.54', 'Administrador', 'ALTERAÇÃO', 115);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '5', '', 'Campo(s) 
Código: 5
* Percentual: 5.0
* Condição de Pagamento: 
 PD
Tipo Operação: RE
* Valor: 0.0
 1
', '', '2011-02-03 15:02:34.54', 'Administrador', 'INCLUSÃO', 116);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '6', '', 'Campo(s) 
Código: 6
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 1
', '', '2011-02-03 15:02:34.54', 'Administrador', 'INCLUSÃO', 117);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '18', '', 'Campo(s) 
Código: 18
* Percentual: 5.0
* Condição de Pagamento: 
 PD
Tipo Operação: RE
* Valor: 0.0
 1
', '', '2011-02-03 15:02:34.55', 'Administrador', 'EXCLUSÃO', 118);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '19', '', 'Campo(s) 
Código: 19
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 1
', '', '2011-02-03 15:02:34.55', 'Administrador', 'EXCLUSÃO', 119);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '20', '', 'Campo(s) 
Código: 20
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 2
', '', '2011-02-03 15:02:34.55', 'Administrador', 'EXCLUSÃO', 120);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '21', '', 'Campo(s) 
Código: 21
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 3
', '', '2011-02-03 15:02:34.55', 'Administrador', 'EXCLUSÃO', 121);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '22', '', 'Campo(s) 
Código: 22
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 4
', '', '2011-02-03 15:02:34.55', 'Administrador', 'EXCLUSÃO', 122);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '23', '', 'Campo(s) 
Código: 23
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 5
', '', '2011-02-03 15:02:34.55', 'Administrador', 'EXCLUSÃO', 123);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '24', '', 'Campo(s) 
Código: 24
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 6
', '', '2011-02-03 15:02:34.55', 'Administrador', 'EXCLUSÃO', 124);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '25', '', 'Campo(s) 
Código: 25
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 7
', '', '2011-02-03 15:02:34.55', 'Administrador', 'EXCLUSÃO', 125);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '26', '', 'Campo(s) 
Código: 26
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 8
', '', '2011-02-03 15:02:34.55', 'Administrador', 'EXCLUSÃO', 126);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '27', '', 'Campo(s) 
Código: 27
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 9
', '', '2011-02-03 15:02:34.55', 'Administrador', 'EXCLUSÃO', 127);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOHORARIO', 'Horários do Plano', '1', '2', '', 'Campo(s) 
Código: 2
* Horário: 2
 PD
Tipo Operação: RE
* Percentual: 20.0
* Valor: 0.0
 false
', '', '2011-02-03 15:02:34.58', 'Administrador', 'INCLUSÃO', 128);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOHORARIO', 'Horários do Plano', '1', '3', '', 'Campo(s) 
Código: 3
* Horário: 1
 
Tipo Operação: 
* Percentual: 0.0
* Valor: 0.0
 false
', '', '2011-02-03 15:02:34.58', 'Administrador', 'INCLUSÃO', 129);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOHORARIO', 'Horários do Plano', '1', '1', '', 'Campo(s) 
Código: 1
* Horário: 2
 
Tipo Operação: 
* Percentual: 0.0
* Valor: 0.0
 false
', '', '2011-02-03 15:02:34.58', 'Administrador', 'EXCLUSÃO', 130);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Número Máximo de Parcelas da Condição de Pagamento:', '9', '1', '2011-02-03 15:05:20.071', 'Administrador', 'ALTERAÇÃO', 131);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Número de Meses:', '9', '1', '2011-02-03 15:05:20.071', 'Administrador', 'ALTERAÇÃO', 132);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', 'Tipo da Operação:', 'RE', 'AC', '2011-02-03 15:05:20.071', 'Administrador', 'ALTERAÇÃO', 133);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Percentual:', '28.0', '0.0', '2011-02-03 15:05:20.071', 'Administrador', 'ALTERAÇÃO', 134);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '5', '', 'Campo(s) 
Código: 5
* Percentual: 5.0
* Condição de Pagamento: 
 PD
Tipo Operação: RE
* Valor: 0.0
 1
', '', '2011-02-03 15:05:20.071', 'Administrador', 'INCLUSÃO', 135);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '6', '', 'Campo(s) 
Código: 6
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 1
', '', '2011-02-03 15:05:20.071', 'Administrador', 'INCLUSÃO', 136);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '18', '', 'Campo(s) 
Código: 18
* Percentual: 5.0
* Condição de Pagamento: 
 PD
Tipo Operação: RE
* Valor: 0.0
 1
', '', '2011-02-03 15:05:20.071', 'Administrador', 'EXCLUSÃO', 137);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '19', '', 'Campo(s) 
Código: 19
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 1
', '', '2011-02-03 15:05:20.071', 'Administrador', 'EXCLUSÃO', 138);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '20', '', 'Campo(s) 
Código: 20
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 2
', '', '2011-02-03 15:05:20.071', 'Administrador', 'EXCLUSÃO', 139);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '21', '', 'Campo(s) 
Código: 21
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 3
', '', '2011-02-03 15:05:20.071', 'Administrador', 'EXCLUSÃO', 140);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '22', '', 'Campo(s) 
Código: 22
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 4
', '', '2011-02-03 15:05:20.071', 'Administrador', 'EXCLUSÃO', 141);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '23', '', 'Campo(s) 
Código: 23
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 5
', '', '2011-02-03 15:05:20.071', 'Administrador', 'EXCLUSÃO', 142);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '24', '', 'Campo(s) 
Código: 24
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 6
', '', '2011-02-03 15:05:20.071', 'Administrador', 'EXCLUSÃO', 143);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '25', '', 'Campo(s) 
Código: 25
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 7
', '', '2011-02-03 15:05:20.071', 'Administrador', 'EXCLUSÃO', 144);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '26', '', 'Campo(s) 
Código: 26
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 8
', '', '2011-02-03 15:05:20.071', 'Administrador', 'EXCLUSÃO', 145);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '27', '', 'Campo(s) 
Código: 27
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 9
', '', '2011-02-03 15:05:20.071', 'Administrador', 'EXCLUSÃO', 146);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Produtos Sugeridos', '1', '5', '', 'Campo(s) 
Código: 5
Obrigatório: true
 false
Produto: 6
Valor do Produto para esse Plano: 0.0
', '', '2011-02-03 15:05:20.111', 'Administrador', 'INCLUSÃO', 147);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Produtos Sugeridos', '1', '6', '', 'Campo(s) 
Código: 6
Obrigatório: false
 false
Produto: 8
Valor do Produto para esse Plano: 0.0
', '', '2011-02-03 15:05:20.121', 'Administrador', 'INCLUSÃO', 148);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Produtos Sugeridos', '1', '7', '', 'Campo(s) 
Código: 7
Obrigatório: false
 false
Produto: 7
Valor do Produto para esse Plano: 0.0
', '', '2011-02-03 15:05:20.121', 'Administrador', 'INCLUSÃO', 149);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Produtos Sugeridos', '1', '1', '', 'Campo(s) 
Código: 1
Obrigatório: false
 false
Produto: 7
Valor do Produto para esse Plano: 30.0
', '', '2011-02-03 15:05:20.121', 'Administrador', 'EXCLUSÃO', 150);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Produtos Sugeridos', '1', '2', '', 'Campo(s) 
Código: 2
Obrigatório: false
 false
Produto: 8
Valor do Produto para esse Plano: 20.0
', '', '2011-02-03 15:05:20.121', 'Administrador', 'EXCLUSÃO', 151);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Produtos Sugeridos', '1', '4', '', 'Campo(s) 
Código: 4
Obrigatório: false
 false
Produto: 6
Valor do Produto para esse Plano: 30.0
', '', '2011-02-03 15:05:20.121', 'Administrador', 'EXCLUSÃO', 152);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Número Máximo de Parcelas da Condição de Pagamento:', '9', '1', '2011-02-03 15:06:29.24', 'Administrador', 'ALTERAÇÃO', 153);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Número de Meses:', '9', '1', '2011-02-03 15:06:29.24', 'Administrador', 'ALTERAÇÃO', 154);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', 'Tipo da Operação:', 'RE', 'AC', '2011-02-03 15:06:29.24', 'Administrador', 'ALTERAÇÃO', 155);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Percentual:', '28.0', '0.0', '2011-02-03 15:06:29.24', 'Administrador', 'ALTERAÇÃO', 156);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '5', '', 'Campo(s) 
Código: 5
* Percentual: 5.0
* Condição de Pagamento: 
 PD
Tipo Operação: RE
* Valor: 0.0
 1
', '', '2011-02-03 15:06:29.24', 'Administrador', 'INCLUSÃO', 157);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '6', '', 'Campo(s) 
Código: 6
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 1
', '', '2011-02-03 15:06:29.24', 'Administrador', 'INCLUSÃO', 158);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '18', '', 'Campo(s) 
Código: 18
* Percentual: 5.0
* Condição de Pagamento: 
 PD
Tipo Operação: RE
* Valor: 0.0
 1
', '', '2011-02-03 15:06:29.24', 'Administrador', 'EXCLUSÃO', 159);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '19', '', 'Campo(s) 
Código: 19
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 1
', '', '2011-02-03 15:06:29.24', 'Administrador', 'EXCLUSÃO', 160);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '20', '', 'Campo(s) 
Código: 20
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 2
', '', '2011-02-03 15:06:29.24', 'Administrador', 'EXCLUSÃO', 161);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '21', '', 'Campo(s) 
Código: 21
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 3
', '', '2011-02-03 15:06:29.24', 'Administrador', 'EXCLUSÃO', 162);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '22', '', 'Campo(s) 
Código: 22
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 4
', '', '2011-02-03 15:06:29.24', 'Administrador', 'EXCLUSÃO', 163);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '23', '', 'Campo(s) 
Código: 23
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 5
', '', '2011-02-03 15:06:29.24', 'Administrador', 'EXCLUSÃO', 164);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '24', '', 'Campo(s) 
Código: 24
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 6
', '', '2011-02-03 15:06:29.24', 'Administrador', 'EXCLUSÃO', 165);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '25', '', 'Campo(s) 
Código: 25
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 7
', '', '2011-02-03 15:06:29.24', 'Administrador', 'EXCLUSÃO', 166);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '26', '', 'Campo(s) 
Código: 26
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 8
', '', '2011-02-03 15:06:29.25', 'Administrador', 'EXCLUSÃO', 167);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '27', '', 'Campo(s) 
Código: 27
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 9
', '', '2011-02-03 15:06:29.25', 'Administrador', 'EXCLUSÃO', 168);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '4', '', '0', '15', '2011-02-03 15:06:29.25', 'Administrador', 'ALTERAÇÃO', 169);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '5', '', '0', '25', '2011-02-03 15:06:29.25', 'Administrador', 'ALTERAÇÃO', 170);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '', '0', '35', '2011-02-03 15:06:29.25', 'Administrador', 'ALTERAÇÃO', 171);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '7', '', '0', '45', '2011-02-03 15:06:30.18', 'Administrador', 'ALTERAÇÃO', 172);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '8', '', '0', '60', '2011-02-03 15:06:30.19', 'Administrador', 'ALTERAÇÃO', 173);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Plano - Produtos Sugeridos', '1', '6', 'Obrigatório:', 'Não', 'Sim', '2011-02-03 15:06:30.21', 'Administrador', 'ALTERAÇÃO', 174);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Plano - Produtos Sugeridos', '1', '', 'Produto:', '8', '6', '2011-02-03 15:06:30.21', 'Administrador', 'ALTERAÇÃO', 175);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Número Máximo de Parcelas da Condição de Pagamento:', '9', '1', '2011-02-03 15:09:23.364', 'Administrador', 'ALTERAÇÃO', 176);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Número de Meses:', '9', '1', '2011-02-03 15:09:23.364', 'Administrador', 'ALTERAÇÃO', 177);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '', '35', '0', '2011-02-03 15:09:23.364', 'Administrador', 'ALTERAÇÃO', 178);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', 'Tipo da Operação:', 'RE', 'AC', '2011-02-03 15:09:23.364', 'Administrador', 'ALTERAÇÃO', 179);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Percentual:', '28.0', '0.0', '2011-02-03 15:09:23.364', 'Administrador', 'ALTERAÇÃO', 180);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '5', '', 'Campo(s) 
Código: 5
* Percentual: 5.0
* Condição de Pagamento: 
 PD
Tipo Operação: RE
* Valor: 0.0
 1
', '', '2011-02-03 15:09:23.364', 'Administrador', 'INCLUSÃO', 181);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '6', '', 'Campo(s) 
Código: 6
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 1
', '', '2011-02-03 15:09:23.364', 'Administrador', 'INCLUSÃO', 182);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '18', '', 'Campo(s) 
Código: 18
* Percentual: 5.0
* Condição de Pagamento: 
 PD
Tipo Operação: RE
* Valor: 0.0
 1
', '', '2011-02-03 15:09:23.364', 'Administrador', 'EXCLUSÃO', 183);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '19', '', 'Campo(s) 
Código: 19
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 1
', '', '2011-02-03 15:09:23.364', 'Administrador', 'EXCLUSÃO', 184);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '20', '', 'Campo(s) 
Código: 20
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 2
', '', '2011-02-03 15:09:23.364', 'Administrador', 'EXCLUSÃO', 185);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '21', '', 'Campo(s) 
Código: 21
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 3
', '', '2011-02-03 15:09:23.364', 'Administrador', 'EXCLUSÃO', 186);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '22', '', 'Campo(s) 
Código: 22
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 4
', '', '2011-02-03 15:09:23.364', 'Administrador', 'EXCLUSÃO', 187);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '23', '', 'Campo(s) 
Código: 23
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 5
', '', '2011-02-03 15:09:23.364', 'Administrador', 'EXCLUSÃO', 188);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '24', '', 'Campo(s) 
Código: 24
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 6
', '', '2011-02-03 15:09:23.374', 'Administrador', 'EXCLUSÃO', 189);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '25', '', 'Campo(s) 
Código: 25
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 7
', '', '2011-02-03 15:09:23.374', 'Administrador', 'EXCLUSÃO', 190);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '26', '', 'Campo(s) 
Código: 26
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 8
', '', '2011-02-03 15:09:23.374', 'Administrador', 'EXCLUSÃO', 191);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '27', '', 'Campo(s) 
Código: 27
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 9
', '', '2011-02-03 15:09:23.374', 'Administrador', 'EXCLUSÃO', 192);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Plano - Produtos Sugeridos', '1', '6', 'Obrigatório:', 'Não', 'Sim', '2011-02-03 15:09:24.334', 'Administrador', 'ALTERAÇÃO', 193);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Plano - Produtos Sugeridos', '1', '', 'Produto:', '8', '6', '2011-02-03 15:09:24.334', 'Administrador', 'ALTERAÇÃO', 194);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Produtos Sugeridos', '1', '8', '', 'Campo(s) 
Código: 8
Obrigatório: false
 false
Produto: 13
Valor do Produto para esse Plano: 50.0
', '', '2011-02-03 15:09:24.334', 'Administrador', 'INCLUSÃO', 195);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO', 'Plano', '1', '', '* Descrição:', 'PLANO', 'METÓDO DE GESTÃO: PACTO 2011', '2011-02-03 15:15:17.168', 'Administrador', 'ALTERAÇÃO', 196);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO', 'Plano', '1', '', '* Percentual da Multa Cancelamento:', '20.0', '10.0', '2011-02-03 15:15:17.168', 'Administrador', 'ALTERAÇÃO', 197);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Número Máximo de Parcelas da Condição de Pagamento:', '9', '1', '2011-02-03 15:15:17.168', 'Administrador', 'ALTERAÇÃO', 198);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Número de Meses:', '9', '1', '2011-02-03 15:15:17.168', 'Administrador', 'ALTERAÇÃO', 199);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '', '35', '0', '2011-02-03 15:15:17.168', 'Administrador', 'ALTERAÇÃO', 200);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', 'Tipo da Operação:', 'RE', 'AC', '2011-02-03 15:15:17.168', 'Administrador', 'ALTERAÇÃO', 201);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Percentual:', '28.0', '0.0', '2011-02-03 15:15:17.168', 'Administrador', 'ALTERAÇÃO', 202);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '5', '', 'Campo(s) 
Código: 5
* Percentual: 5.0
* Condição de Pagamento: 
 PD
Tipo Operação: RE
* Valor: 0.0
 1
', '', '2011-02-03 15:15:17.178', 'Administrador', 'INCLUSÃO', 203);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '6', '', 'Campo(s) 
Código: 6
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 1
', '', '2011-02-03 15:15:17.178', 'Administrador', 'INCLUSÃO', 204);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '18', '', 'Campo(s) 
Código: 18
* Percentual: 5.0
* Condição de Pagamento: 
 PD
Tipo Operação: RE
* Valor: 0.0
 1
', '', '2011-02-03 15:15:17.178', 'Administrador', 'EXCLUSÃO', 205);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '19', '', 'Campo(s) 
Código: 19
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 1
', '', '2011-02-03 15:15:17.178', 'Administrador', 'EXCLUSÃO', 206);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '20', '', 'Campo(s) 
Código: 20
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 2
', '', '2011-02-03 15:15:17.178', 'Administrador', 'EXCLUSÃO', 207);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '21', '', 'Campo(s) 
Código: 21
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 3
', '', '2011-02-03 15:15:17.178', 'Administrador', 'EXCLUSÃO', 208);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '22', '', 'Campo(s) 
Código: 22
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 4
', '', '2011-02-03 15:15:17.178', 'Administrador', 'EXCLUSÃO', 209);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '23', '', 'Campo(s) 
Código: 23
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 5
', '', '2011-02-03 15:15:17.178', 'Administrador', 'EXCLUSÃO', 210);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '24', '', 'Campo(s) 
Código: 24
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 6
', '', '2011-02-03 15:15:17.178', 'Administrador', 'EXCLUSÃO', 211);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '25', '', 'Campo(s) 
Código: 25
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 7
', '', '2011-02-03 15:15:17.178', 'Administrador', 'EXCLUSÃO', 212);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '26', '', 'Campo(s) 
Código: 26
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 8
', '', '2011-02-03 15:15:17.178', 'Administrador', 'EXCLUSÃO', 213);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '27', '', 'Campo(s) 
Código: 27
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 9
', '', '2011-02-03 15:15:17.178', 'Administrador', 'EXCLUSÃO', 214);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Plano - Produtos Sugeridos', '1', '6', 'Obrigatório:', 'Não', 'Sim', '2011-02-03 15:15:18.158', 'Administrador', 'ALTERAÇÃO', 215);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Plano - Produtos Sugeridos', '1', '', 'Produto:', '8', '6', '2011-02-03 15:15:18.158', 'Administrador', 'ALTERAÇÃO', 216);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Plano - Produtos Sugeridos', '1', '', 'Produto:', '13', '8', '2011-02-03 15:15:18.158', 'Administrador', 'ALTERAÇÃO', 217);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Plano - Produtos Sugeridos', '1', '8', 'Valor do Produto para esse Plano:', '50.0', '0.0', '2011-02-03 15:15:18.158', 'Administrador', 'ALTERAÇÃO', 218);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO', 'Plano', '1', '', '* Descrição:', 'METÓDO DE GESTÃO: PACTO 2011', 'PACTO 2011: MÉTODO DE GESTÃO', '2011-02-03 15:15:37.128', 'Administrador', 'ALTERAÇÃO', 219);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Número Máximo de Parcelas da Condição de Pagamento:', '9', '1', '2011-02-03 15:15:37.128', 'Administrador', 'ALTERAÇÃO', 220);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Número de Meses:', '9', '1', '2011-02-03 15:15:37.128', 'Administrador', 'ALTERAÇÃO', 221);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '', '35', '0', '2011-02-03 15:15:37.128', 'Administrador', 'ALTERAÇÃO', 222);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', 'Tipo da Operação:', 'RE', 'AC', '2011-02-03 15:15:37.128', 'Administrador', 'ALTERAÇÃO', 223);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANODURACAO', 'Plano - Duração do Plano', '1', '6', '* Percentual:', '28.0', '0.0', '2011-02-03 15:15:37.128', 'Administrador', 'ALTERAÇÃO', 224);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '5', '', 'Campo(s) 
Código: 5
* Percentual: 5.0
* Condição de Pagamento: 
 PD
Tipo Operação: RE
* Valor: 0.0
 1
', '', '2011-02-03 15:15:37.128', 'Administrador', 'INCLUSÃO', 225);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '6', '', 'Campo(s) 
Código: 6
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 1
', '', '2011-02-03 15:15:37.128', 'Administrador', 'INCLUSÃO', 226);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '18', '', 'Campo(s) 
Código: 18
* Percentual: 5.0
* Condição de Pagamento: 
 PD
Tipo Operação: RE
* Valor: 0.0
 1
', '', '2011-02-03 15:15:37.128', 'Administrador', 'EXCLUSÃO', 227);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '19', '', 'Campo(s) 
Código: 19
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 1
', '', '2011-02-03 15:15:37.128', 'Administrador', 'EXCLUSÃO', 228);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '20', '', 'Campo(s) 
Código: 20
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 2
', '', '2011-02-03 15:15:37.128', 'Administrador', 'EXCLUSÃO', 229);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '21', '', 'Campo(s) 
Código: 21
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 3
', '', '2011-02-03 15:15:37.128', 'Administrador', 'EXCLUSÃO', 230);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '22', '', 'Campo(s) 
Código: 22
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 4
', '', '2011-02-03 15:15:37.128', 'Administrador', 'EXCLUSÃO', 231);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '23', '', 'Campo(s) 
Código: 23
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 5
', '', '2011-02-03 15:15:37.128', 'Administrador', 'EXCLUSÃO', 232);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '24', '', 'Campo(s) 
Código: 24
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 6
', '', '2011-02-03 15:15:37.128', 'Administrador', 'EXCLUSÃO', 233);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '25', '', 'Campo(s) 
Código: 25
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 7
', '', '2011-02-03 15:15:37.128', 'Administrador', 'EXCLUSÃO', 234);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '26', '', 'Campo(s) 
Código: 26
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 8
', '', '2011-02-03 15:15:37.138', 'Administrador', 'EXCLUSÃO', 235);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANODURACAO - PLANOCONDICAOPAGAMENTO', 'Plano Condição Pagamento', '1', '27', '', 'Campo(s) 
Código: 27
* Percentual: 0.0
* Condição de Pagamento: 
 
Tipo Operação: 
* Valor: 0.0
 9
', '', '2011-02-03 15:15:37.138', 'Administrador', 'EXCLUSÃO', 236);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Plano - Produtos Sugeridos', '1', '6', 'Obrigatório:', 'Não', 'Sim', '2011-02-03 15:15:38.108', 'Administrador', 'ALTERAÇÃO', 237);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Plano - Produtos Sugeridos', '1', '', 'Produto:', '8', '6', '2011-02-03 15:15:38.108', 'Administrador', 'ALTERAÇÃO', 238);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Plano - Produtos Sugeridos', '1', '', 'Produto:', '13', '8', '2011-02-03 15:15:38.108', 'Administrador', 'ALTERAÇÃO', 239);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('PLANO - PLANOPRODUTOSUGERIDO', 'Plano - Produtos Sugeridos', '1', '8', 'Valor do Produto para esse Plano:', '50.0', '0.0', '2011-02-03 15:15:38.108', 'Administrador', 'ALTERAÇÃO', 240);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('CONTRATO', 'Contrato', '1', '', 'TODOS', '', '--------------------------------------

 

Código do Contrato = 1

Valor Contrato = R$114,00

Empresa = ACADEMIA NOME

Plano = PACTO 2011: MÉTODO DE GESTÃO

Responsável pelo Contrato = Administrador

--------------------------------------

Modalidade = MUSCULAÇÃO Nº Vezes Por Semana= 6

Modalidade = GINÁSTICA Nº Vezes Por Semana= 6

Modalidade = PILATES Nº Vezes Por Semana= 6

Modalidade = DANÇA  Nº Vezes Por Semana= 3

Modalidade = PISCINA Nº Vezes Por Semana= 0

--------------------------------------

Turma = NINF

', '2011-03-10 18:05:15.88', 'Administrador', 'INCLUSÃO DE CONTRATO', 241);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('CLIENTE', 'Cliente', '2', '', 'Situação:', '', 'VI', '2011-03-11 08:15:22.554', 'Administrador', 'INCLUSÃO', 242);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('CLIENTE', 'Cliente', '2', '', 'Matrícula:', '', '000006', '2011-03-11 08:15:22.554', 'Administrador', 'INCLUSÃO', 243);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('CLIENTE', 'Cliente', '2', '', 'codigoMatricula', '', '6', '2011-03-11 08:15:22.554', 'Administrador', 'INCLUSÃO', 244);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('CLIENTE', 'Cliente - Vinculo/Vínculo', '2', '3', 'Campo(s)', '', '* Tipo de Vínculo: CO', '2011-03-11 08:15:22.57', 'Administrador', 'INCLUSÃO', 245);
INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, codigo) VALUES ('CONTRATO', 'Contrato', '2', '', 'TODOS', '', '--------------------------------------

 

Código do Contrato = 2

Valor Contrato = R$114,00

Empresa = ACADEMIA NOME

Plano = PACTO 2011: MÉTODO DE GESTÃO

Responsável pelo Contrato = Administrador

--------------------------------------

Modalidade = MUSCULAÇÃO Nº Vezes Por Semana= 6

Modalidade = GINÁSTICA Nº Vezes Por Semana= 6

Modalidade = PILATES Nº Vezes Por Semana= 6

Modalidade = DANÇA  Nº Vezes Por Semana= 3

Modalidade = PISCINA Nº Vezes Por Semana= 0

--------------------------------------

Turma = NINF

', '2011-03-11 08:19:24.807', 'Administrador', 'INCLUSÃO DE CONTRATO', 246);

INSERT INTO perguntacliente (multipla, simples, textual, tipopergunta, descricao, codigo) VALUES (true, false, false, 'ME', 'ATIVIDADES DE INTERESSE: ', 1);
INSERT INTO perguntacliente (multipla, simples, textual, tipopergunta, descricao, codigo) VALUES (true, false, false, 'ME', 'QUAL SEU OBJETIVO?', 2);
INSERT INTO perguntacliente (multipla, simples, textual, tipopergunta, descricao, codigo) VALUES (false, true, false, 'SE', 'EM QUANTO TEMPO?', 3);
INSERT INTO perguntacliente (multipla, simples, textual, tipopergunta, descricao, codigo) VALUES (false, true, false, 'SE', 'HÁ QUANTO TEMPO ESTÁ PENSANDO EM COMEÇAR ATIVIDADE FÍSICA?', 4);
INSERT INTO perguntacliente (multipla, simples, textual, tipopergunta, descricao, codigo) VALUES (true, false, false, 'ME', 'O QUE TE IMPEDIU DE COMEÇAR ANTES?', 5);
INSERT INTO perguntacliente (multipla, simples, textual, tipopergunta, descricao, codigo) VALUES (true, false, false, 'ME', 'COMO CONHECEU A ACADEMIA?', 6);
INSERT INTO perguntacliente (multipla, simples, textual, tipopergunta, descricao, codigo) VALUES (true, false, false, 'ME', 'QUAL SEU OBJETIVO?', 7);
INSERT INTO perguntacliente (multipla, simples, textual, tipopergunta, descricao, codigo) VALUES (false, true, false, 'SE', 'HÁ QUANTO TEMPO ESTÁ PENSANDO EM COMEÇAR ATIVIDADE FÍSICA?', 8);
INSERT INTO perguntacliente (multipla, simples, textual, tipopergunta, descricao, codigo) VALUES (false, false, true, 'TE', 'O QUE TE FEZ PARAR DA ÚLTIMA VEZ?', 9);
INSERT INTO perguntacliente (multipla, simples, textual, tipopergunta, descricao, codigo) VALUES (true, false, false, 'ME', 'ATIVIDADES DE INTERESSE: ', 10);
INSERT INTO perguntacliente (multipla, simples, textual, tipopergunta, descricao, codigo) VALUES (true, false, false, 'ME', 'QUAL SEU OBJETIVO?', 11);
INSERT INTO perguntacliente (multipla, simples, textual, tipopergunta, descricao, codigo) VALUES (false, true, false, 'SE', 'EM QUANTO TEMPO?', 12);
INSERT INTO perguntacliente (multipla, simples, textual, tipopergunta, descricao, codigo) VALUES (false, true, false, 'SE', 'HÁ QUANTO TEMPO ESTÁ PENSANDO EM COMEÇAR ATIVIDADE FÍSICA?', 13);
INSERT INTO perguntacliente (multipla, simples, textual, tipopergunta, descricao, codigo) VALUES (true, false, false, 'ME', 'O QUE TE IMPEDIU DE COMEÇAR ANTES?', 14);
INSERT INTO perguntacliente (multipla, simples, textual, tipopergunta, descricao, codigo) VALUES (true, false, false, 'ME', 'COMO CONHECEU A ACADEMIA?', 15);

INSERT INTO periodoacessocliente (tipoacesso, datafinalacesso, datainicioacesso, aulaavulsadiaria, contrato, contratobaseadorenovacao, pessoa, codigo) VALUES ('CA', '2012-03-24 00:00:00', '2011-03-25 00:00:00', NULL, 1, NULL, 2, 1);