
# Waller Maciel: esse arquivo de propriedades deve armazenar informa\u00e7\u00f5es onde, anteriormente,
# estava sendo definidos em Classes (.class), obrigando uma compila\u00e7\u00e3o nova para isso.
# Vide constantes em PropsService.java:
# - Cada propriedade aqui tem que possuir uma constante com o seu nome!
# As keywords com @ (arroba) n\u00e3o sobrescritas pelo Script de deployment da aplica\u00e7\u00e3o,
# portanto, muita aten\u00e7\u00e3o ao alter\u00e1-las!
instanciasNotificar = *********:28080,*********:28081,*********:28082,*********:28083,*********:28084,*********:28080,*********:28081,*********:28082,*********:28083,*********:28084,*********:28080,*********:28081,*********:28082,*********:28083,*********:28084,*********:28080,*********:28081,*********:28082,*********:28083,*********:28084,*********:28080,*********:28081,*********:28082,*********:28083,*********:28084