# To change this template, choose Too<PERSON> | Templates
# and open the template in the editor.
principal.lingua=Language
principal.cadastros = Signups
principal.gestao = Management
principal.alunos = Students
principal.agenda = Calendar
principal.notificacoes = Notifications Center
principal.pesquisa = Search

cadastros.musculos = Muscles
cadastros.gruposmusculares = Muscles Groups
cadastros.aparelhos = Devices
cadastros.atividades = Activities
cadastros.programas = Training Program
cadastros.todosalunos = View All
cadastros.agendamentos = Schedules
cadastros.compromissos = Commitment
cadastros.objetivos = Predefined goals
cadastros.fichas = Sheets

cadastros.salvar=Save
cadastros.addNovaAtividade=Add new activitie
cadastros.addNovoGrupo=Add new group
cadastros.musculosCadastrados=Registered muscles
cadastros.adicionarMusculo=Add muscle
cadastros.filtrar=Filter
cadastros.relacoes=relations
cadastros.musculo.nome=Muscle name
cadastros.excluir=Delete
cadastros.gruposmuscularescadastrados=Registered Muscles Groups
cadastros.addNovoMusculo=Add new muscle
cadastros.grupoMuscular.nome=Group name
cadastros.adicionarGrupoMuscular=Add Muscle Group
cadastros.aparelho.nome=Device name
cadastros.adicionarAparelho=Add Device
cadastros.aparelhoscadastrados=Registered Devices
cadastros.aparelho.tipo=Type
cadastros.atividade.nome=Activitie name
cadastros.atividade.descricao=Description
cadastros.atividade.ativo=Active
cadastros.atividade.tipo=Type
cadastros.atividade.copyDescToSerie=Copy desc. to serie
cadastros.adicionarAtividade=Add activitie
cadastros.addNovoAparelho=Add new device
cadastros.addCategoria=Add new category
cadastros.categoriaAtividades=Activity's Cat.
cadastros.atividadescadastradas=Registered activities
cadastros.nome=Name
cadastros.descricao=Description
cadastros.sim=Yes
cadastros.nao=No
cadastros.categoriaatividadeadicionar=Add category
cadastros.categoriaatividade.nome=Category name
cadastros.categoriaatividade=Categories of activities
cadastros.categoriaatividadecadastradas=Registered categories of activities
cadastros.categoriaficha=Categories of sheets
cadastros.categoriafichacadastradas=Registered categories of sheets
cadastros.addFichas=Add new sheet
cadastros.nivel=Levels
cadastros.adicionarNivel=Add level
cadastros.nivel.nome=Level name
cadastros.nivel.ordem=Order
cadastros.adicionarobjetivos=Add goal
cadastros.objetivos.nome=Goal name
cadastros.objetivoscadastrados=Registered predefined goals
cadastros.niveiscadastrados=Registered levels
cadastros.imagens=Images
cadastros.imagensadicionar=Add image
cadastros.imagem.nome=Image name
cadastros.imagem.endereco=Image path
cadastros.imagemcadastradas=Registered images
cadastros.addImagem=Add new image
cadastros.todos=All
domingo=Sunday
segunda=Monday
terca=Tuesday
quarta=Wednesday
quinta=Thursday
sexta=Friday
sabado=Saturday

DM=S
SG=M
TR=T
QA=W
QI=T
SX=F
SB=S

cadastros.ficha=Sheet
cadastros.programa.nome=Training program name
cadastros.detalhes=Details
cadastros.datainicio=Start date
cadastros.datafim=End date
cadastros.diassemana=Days per week
cadastros.programa.aulasprevistas=Classes planned
cadastros.programa.proximarevisao=Next review
cadastros.programa.termino=Finish
cadastros.ficha.nova=New sheet
cadastros.fichas.predefinidas=Predefined sheet
cadastros.objetivos.predefinidos=Predefined objectives
cadastros.atividades.selecionadas=Sheet activities
cadastros.fichas.adicionadas=Selected sheets
cadastros.ficha.editar=Edit sheet
cadastros.ficha.tornarpre=Making predefined
cadastros.fichas.nome=New sheet name
cadastros.ficha.tipoexecucao=Execution type
cadastros.mensagem=Message
cadastros.ajuste=Adjustment
cadastros.valor=Value
cadastros.adicionarAjuste=Add adjustment
cadastros.serie=Series
cadastros.quantidade=Sets
cadastros.carga=Weight
cadastros.repeticoes=Repetitions
cadastros.duracao=Duration
cadastros.distancia=Distance
cadastros.velocidade=Speed
cadastros.descanso=Rest
cadastros.complemento=Complement
cadastros.remover=Remove
cadastros.serie.adicionar=Add series
cadastros.ficha.concluir=Save sheet
cadastros.programa.novo=New program
cadastros.musculo=Muscle
cadastros.grupomuscular=Muscular group
cadastros.aparelho=Device
cadastros.atividade=Activity
cadastros.categoriaatividadesing=Activity category
cadastros.categoriafichasing=Sheet category
cadastros.nivelsing=Level
cadastros.objetivo=Goal
cadastros.imagem=Image
cadastros.programa=Training program
cadastros.serie.adicionada=Series added successfully!
cancelar=Cancel
cadastros.atividade.remover=Remove activity
cadastros.atividade.salvar=Save activity
disponibilidade=Availability
contatoInterpessoal=Interpersonal contact
prescricaoTreino=Prescription Training
revisaoTreino=Review of Training
renovarTreino=Renew Training
agenda.tipo=Type
agenda.inicio=Start
agenda.fim=End
agenda.diaTodo=All day
agenda.novo=New schedule
agenda.aluno=Student
professor=Teacher
editarDisponibilidade=Edit availability
start=Start
finishTreino=Finish training
finishSerie=Finish series
cadastros.ajustes=Adjustments
realizada=Held
alterarSerie=Change series
status=Status
historicoExecucoes=History plays
series=Series
quilos=Kg
metros=m
minutos=min
semNotificacao=Without notice
diminuiuCarga=Decreased load
aumentouCarga=Increased load
buscar=Search
concluirCadastro=Complete registration
selecioneProfessor=Select a Teacher
naotemprofessor=Without a teacher
voltar=Back
emaillogincontato=Email to login and contact
senhapodedeixar=Password (Can leave blank)
nomecompletoparte=Name (Full or Part)
CPF=CPF
matriculaaluno=Student enrollment
badge=Badge
badges=Badges
valorBadge=Badge value
ativo=Active
horarioComercial=Business hours
repetir=Repeat
diaSemana=Days of the week
repetirAte=Repeat until
remover=Remove
disponibilidades=Availabilities
voltarAgenda=Back
dia=Day
veragenda=See schedule
acompanhar=Follow
agendar=Schedule
configuracoesGlobais=Global settings
configuracoesUsuario=User settings
configuracoesPerfis=Profile settings
novoperfil=New profile
cadastros.tipoEvento=Event types
cadastros.metodo=Training method
cadastros.adicionarTipoEvento=Add type
cadastros.tiposcadastrados=Registered event types
cadastros.tiposEvento=Event types
cadastros.tiponome=Type name
cor=Color
comportamento=Behavior
cortipoevento=Event type color
disponibilidadepara=Availability for
todosTipos=All types
agruparPor=Group by
editar=Edit
fechar=Close
aplicarNSU=Confirm changes
simsomentemesmotipo=As the same type
NENHUM=Don't group
PROFESSOR=Teacher
TIPOEVENTO=Tipo evento
CADASTROS_AUXILIARES=Auxiliary registers
AGENDA=Agenda
ALUNO=Student
GERAL=General
entidade=Entities
funcionalidade=Features
permitir=Allow
Janeiro=January
Fevereiro=February
Marco=March
Abril=April
Maio=May
Junho=June
Julho=July
Agosto=August
Setembro=September
Outubro=October
Novembro=November
Dezembro=December
nrAgendamentos=Number of schedules
cada=each 
dias=days
agrupar=Group
apenasAlunosCarteira=Only students in the teacher's wallet
configuracoesUsuarios=Users
usuario=User
senha=New password
perfil=Profile
editarUsuario=Edit user
editarPerfil=Edit profile
concluirEdicao=End editing
somenteEste=Only this event
todosEventosRepetidos=All repeating events
acompanhadoPor=Accompanied by
vencimentoContrato=Expiration
nascimento=Birthday
nomeperfil=Profile title
PORC_ACIMA_NOTIFICAR=Percentage above the load to notify the teacher
PORC_ABAIXO_NOTIFICAR=Percentage under load to notify the teacher
gestao.inidicadores=Indicators
gestao.agendados=Scheduled
gestao.executados=Executed
itens=items
total=Amount
professores=Teachers
categorias=Categories
calendario=Calendar
tiposEvento=Events
SUGERIR_TIPO_ALTERNADO=Suggest alternate form of execution on record
qrCodePlayStore=Get application on Play Store
qrCodeAppStore=Get application on App Store
qrCodeChaveEmpresa=Capture QrCode Key from Customers
verAgendados=View Scheduling
porcExecucao=Implementation of the Training Program in relation to combined
enfileirarImpressao=Send to Printer
gestao.indicadores=Indicators
gestao.cancelados=Canceled
gestao.faltas=Absence
gestao.indicadoresAgenda=Scheduling Indicators
DURACAO_LIVRE=Free
DURACAO_PREDEFINIDA=Pre-defined
INTERVALO_DE_TEMPO=Interval
duracao_min=Min.
duracao_max=Max.
duracao_evento=Duration
pesquisaGeralAluno=Search Student (Ctrl+Shift+L)
fichaDeHoje=Today's record
previstoRealizado=Intended / Held
digiteAlgoAguarde=Type something and waint
SUGERIR_DESCANSO=Suggest decanso standard in series
intervalo_minimo=Minimum interval in case of missing
legenda=Legend
carregando=Loading
leve=Light
grave=Severe
media=Normal
criadoPor=Created by
AGENDA_NOVA_ABA=Open Schedule always in a new tab
irParaAluno=Go To Student
atividadesNaFicha=Activities on sheet
dadosFicha=Data of record
adicionandoAtividadesFicha=Add activities on record
verdadosficha=Edit data of record
cadastros.serie.remover=Series
cadastros.repeticoes.abrev=Rep
cadastros.duracao.abrev=Dur
cadastros.distancia.abrev=Dist
cadastros.velocidade.abrev=Vel
cadastros.descanso.abrev=Desc
cadastros.carga.abrev=Carga
cadastros.serie.gerar=Gen Series
cadastros.serie.gerar.hint=Generate series in all activities
gestao.carteira=Carteira
gestaoAgenda=Management: Agenda
gestaoProfessores=Management: Teachers
indicadoresAgenda=Indicators of Agenda
indicadoresProfessores=Indicators of Teachers
intervaloPesquisa=Range of research
filtros=Filters
data=Date
gestao.semCarteira=Without program
carteiras=Portfolios of teachers
atividadesProfessores=Activities of teachers
indicadoresAtividadesProfessores=Indicators of the activities of teachers
gestaoAtividades=Management: Activities
gestao.treinoNovo=New training
renovar=Renew
gestao.treinoRenovado=Renew training
gestao.treinoRevisado=Revised training
gestao.treinoAcompanhados=Accompanied trainings
cadastros.atividade.seriesApenasDuracao=Series just with Duration

DIAS_ANTES_VENCIMENTO=Days before expiration
DIAS_DEPOIS_VENCIMENTO=Days after expiration
gestao.treinoProxVencimento=Prox. expiration
gestao.vencidos=Expired
gestao.avaliacao=Rating
gestao.duasEstrelas=2 stars
GRAVE=Severe
LEVE=Light
MEDIA=Normal
horaInicio=Start hour
horaFim=End hour
gestao.indicadoresTreinos=Indicators of trainings
ATIVO=Active
VENCIDO=Expired
TRANCADO=Locked
DESISTENTE=Quitter
CANCELADO=Canceled
MATRICULA=Matriculation
REMATRICULA=Matriculation
RENOVACAO=Renovation
VISITANTE=Visitor
cliente.enviarEmailAtivacao=Send/Resend activation for email
notificacao.push=Sent PUSH
notificacao.sms=Sent SMS
addAluno.usarAplicativo=Do you going use app?
revisar=Review
OUTROS=Others
descricaoSituacao=Current contract situation as of today's student. Regardless of the base date of the query, this is the current situation.
situacoesContrato=Contract situation
situacoesPrograma=Program situation
gravidade=Gravity
gestaoNotificacoes=Management notifications
notificacoes=notifications
dataReferenciaCarteira=Date reference portfolio
cadastros.programa.comoAlterarProfCarteira=To change the teacher's portfolio must run "Add Student to Treino" again.
tipSemTreino=Number of students who are in training but never had an associated training program.
tipTotalAlunos=This indicator takes into account the number of students with the training program based on the date of consultation, involving the teacher portfolio. Students are considered assets and accrued programs.
tipTreinoNovo=Number of new training, ie the first workout after the student enrolls, the teacher identified this as a new training workout. The teacher chose the "New Training" button
tipProgramasVencidos=Number of students who have training program that has expired and has no assets in the base date.
tipProximoVencidos=You can configure the number of days before and after the expiration of a training program for it to be considered in this indicator. That is, if the settings indicate that five days before and 1 day after the period is to be taken into consideration, and the basis of the query date is 02/06, all programs that expire between 28/05, 03/06 will be presented as a result.
tipIndiceAvaliacao=Average grade of the training received by teachers for two months (month of the base date and the previous). The value of the footer is the sum of all the mean, divided by the number of teachers who received some note, and not by the total teachers.
tipDuasEstrelas=Number less than or equal to 2 notes received by teachers for two months (month of the base date and the previous).
tipTreinosRenovados=From a training program teacher can press the "Reload" button, the system creates a new workout from the previous workout and puts the previous workout as renewed and this as a renewal.
tipTreinosRevisados=When the teacher enters the training program and review click the button, the system records a revision.
tipTreinosAcompanhados=When the teacher chooses to accompany the student join the system records the date and time. The action is that the teacher is with the students to perform all activities of daily training of the student.
cadastros.atividade.abrirGaleriaImagens=Open Image Gallery
cadastros.atividade.selecaoImagens=Select
cadastros.programa.datarevisao=Revision date
cadastros.programa.justificativaRevisao=History
cadastros.programa.historicoRevisoes=Revision History
cadastros.confirmar=Confirm
RETENCAO=Retention
VENDAS=Sales
ICV=ICV
CRM_VENDAS=CRM Sales
PREVISAO_RENOVACAO=Previs\u00e3o renova\u00e7\u00e3o
MAIOR=Maior que
MENOR=Menor que
IGUAL=Igual a
GESTOR=
TODOS=
CONSULTOR=Consultor
PLANOS_POR_DURACAO=Plano por dura\u00e7\u00e3o
cadastros.nova=Nova
cadastros.updateServices=Update Services
cadastros.updateVerificador=Update Checker 
cadastros.gerarBackups=Generate Backups
cadastros.editarCrontab=Editar Crontab
cadastros.gravarCrontab=Gravar Crontab
cadastros.nuvens=Servidores
cadastros.lerVersaoPgsql=Obter Vers\u00e3o PG
cadastros.gravarVersaoPgsql=Gravar Vers\u00e3o PG
FINANCEIRO=Financeiro
TREINO=Treino
CRM=CRM
ADMINISTRADOR=Administrador
GERENTE=Gerente
