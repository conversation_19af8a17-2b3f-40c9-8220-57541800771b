# Armazenar configuracoes Globais para o perfeito funcionamento do Sistema
# Ao mudar essas configuracoes, para que entrem em vigor, 
# deve-se invocar o metodo br.com.pacto.objeto.Aplicacao.init()
version=@VERSION@
urlZillyonWeb=<into OAMD>
urlAdmApp=@URL_ADM_APP@
#urlAdmApp = http://localhost:8084/AdmAPP
keyUnitTests=@KEY_TESTS@
#keyUnitTests = in-memory
urlFeedPacto=@URL_FEED@
urlObterBanners=@URL_BANNERS@
instanciasPropagar=@INSTANCIAS_NOTIFICAR@
myUpUrlBase=@MY_URL_UP_BASE@
telaApoio=@TELA_APOIO@
urlGame=@URL_GAME@
oamdApenasParaAcesso=@APENAS_ACESSO_MULTI_EMPRESAS@
imgLogin=@imgLogin@
userHolding=HOLDING
senhaHolding=S3LF1TH0L
urlJenkinsMonitor=@urlJenkinsMonitor@
urlPush=@URL_PUSH_WS@
urlOAMD3=**************************************
urlLoginFacilite=http://app.pactosolucoes.com.br/login/facilite
urlAPIFacebook=https://graph.facebook.com/v2.11/
URL_API_ZW=@URL_API_ZW@
discoveryUrls=@DISCOVERY_URL@
diretorioFotos=/opt/zw-photos/
diretorioArquivos=@DIRETORIO_ARQUIVOS@
typeMidiasService=@TIPO_MIDIA@
urlFotosNuvem=@URL_FOTOS_NUVEM@
infraHomologacaoSite=@INFRA_HOMOLOGACAO_SITE@
AUTH_SECRET_PATH=@AUTH_SECRET_PATH@
URL_WSFINANCEIRO=@URL_WSFINANCEIRO@
URL_IFINAN=@URL_IFINAN@
EMAIL_MOVIDESK=<EMAIL>
EMAIL_MOVIDESK_RESPONDER=<EMAIL>
REDIRECIONAR_CONSULTORES=@REDIRECIONAR_CONSULTORES@
enableCaptcha=@ENABLE_CAPTCHA@
ipBlockIgnoreCaptcha=@IP_BLOCK_IGNORE@
OAMD_SECRET=@OAMD_SECRET@
OAMD_MIGRATION_SECRET=@OAMD_MIGRATION_SECRET@
URL_GLAPI=@URL_GLAPI@
AES_SECRET_KEY=@AES_SECRET_KEY@
GITLAB_DEVELOP_TOKEN=@GITLAB_DEVELOP_TOKEN@
AWS_REGION=@AWS_REGION@

