CREATE VIEW z_oamd_empresaformapagamento_view2 AS
  SELECT
    z_oamd_empresaformapagamento.codigo,
    z_oamd_empresaformapagamento.empresazw,
    z_oamd_empresaformapagamento.anoexpiracao,
    z_oamd_empresaformapagamento.ativa,
    z_oamd_empresaformapagamento.chave,
    z_oamd_empresaformapagamento.nomebanco,
    z_oamd_empresaformapagamento.cidade,
    z_oamd_empresaformapagamento.clientesativos,
    z_oamd_empresaformapagamento.cnpj,
    z_oamd_empresaformapagamento.codigoempresa,
    z_oamd_empresaformapagamento.creditodcc,
    z_oamd_empresaformapagamento.dataconsulta,
    z_oamd_empresaformapagamento.dataexpiracao,
    z_oamd_empresaformapagamento.dataexpiracaocreditodcc,
    z_oamd_empresaformapagamento.diaexpiracao,
    z_oamd_empresaformapagamento.estado,
    z_oamd_empresaformapagamento.faturadoavista,
    z_oamd_empresaformapagamento.faturadoboleto,
    z_oamd_empresaformapagamento.faturadocartocredito,
    z_oamd_empresaformapagamento.faturadocartaocreditodcc,
    z_oamd_empresaformapagamento.faturadocartaocreditonormal,
    z_oamd_empresaformapagamento.faturadocheque,
    z_oamd_empresaformapagamento.faturadocontacorrente,
    z_oamd_empresaformapagamento.faturadopagamentodigital,
    z_oamd_empresaformapagamento.mesexpiracao,
    z_oamd_empresaformapagamento.nome,
    z_oamd_empresaformapagamento.periodofim,
    z_oamd_empresaformapagamento.periodoinicio,
    z_oamd_empresaformapagamento.qtdrecibos,
    z_oamd_empresaformapagamento.razaosocial,
    z_oamd_empresaformapagamento.sequencia,
    z_oamd_empresaformapagamento.situacao,
    z_oamd_empresaformapagamento.tokensms,
    z_oamd_empresaformapagamento.uf,
    z_oamd_empresaformapagamento.valorfaturado,
    z_oamd_empresaformapagamento.qtdAcessoMes,
    CASE WHEN (((date_part('epoch' :: TEXT, (z_oamd_empresaformapagamento.dataultimoacesso -
                                             (z_oamd_empresaformapagamento.dataconsulta) :: TIMESTAMP WITHOUT TIME ZONE))
                 / (60) :: DOUBLE PRECISION) / (60) :: DOUBLE PRECISION) > (0) :: DOUBLE PRECISION)
      THEN '00- Acessos Futuros' :: TEXT
    WHEN ((((date_part('epoch' :: TEXT, ((z_oamd_empresaformapagamento.dataconsulta) :: TIMESTAMP WITHOUT TIME ZONE -
                                         z_oamd_empresaformapagamento.dataultimoacesso)) / (60) :: DOUBLE PRECISION) /
            (60) :: DOUBLE PRECISION) >= (0) :: DOUBLE PRECISION) AND (((date_part('epoch' :: TEXT, (
      (z_oamd_empresaformapagamento.dataconsulta) :: TIMESTAMP WITHOUT TIME ZONE -
      z_oamd_empresaformapagamento.dataultimoacesso)) / (60) :: DOUBLE PRECISION) / (60) :: DOUBLE PRECISION) <=
                                                                       (6) :: DOUBLE PRECISION))
      THEN '01 - 0-6 horas sem acesso ' :: TEXT
    WHEN ((((date_part('epoch' :: TEXT, ((z_oamd_empresaformapagamento.dataconsulta) :: TIMESTAMP WITHOUT TIME ZONE -
                                         z_oamd_empresaformapagamento.dataultimoacesso)) / (60) :: DOUBLE PRECISION) /
            (60) :: DOUBLE PRECISION) >= (7) :: DOUBLE PRECISION) AND (((date_part('epoch' :: TEXT, (
      (z_oamd_empresaformapagamento.dataconsulta) :: TIMESTAMP WITHOUT TIME ZONE -
      z_oamd_empresaformapagamento.dataultimoacesso)) / (60) :: DOUBLE PRECISION) / (60) :: DOUBLE PRECISION) <=
                                                                       (12) :: DOUBLE PRECISION))
      THEN '02 - 7-12 horas sem acesso ' :: TEXT
    WHEN ((((date_part('epoch' :: TEXT, ((z_oamd_empresaformapagamento.dataconsulta) :: TIMESTAMP WITHOUT TIME ZONE -
                                         z_oamd_empresaformapagamento.dataultimoacesso)) / (60) :: DOUBLE PRECISION) /
            (60) :: DOUBLE PRECISION) >= (13) :: DOUBLE PRECISION) AND (((date_part('epoch' :: TEXT, (
      (z_oamd_empresaformapagamento.dataconsulta) :: TIMESTAMP WITHOUT TIME ZONE -
      z_oamd_empresaformapagamento.dataultimoacesso)) / (60) :: DOUBLE PRECISION) / (60) :: DOUBLE PRECISION) <=
                                                                        (24) :: DOUBLE PRECISION))
      THEN '03 - 13-24 horas sem acesso ' :: TEXT
    WHEN ((((date_part('epoch' :: TEXT, ((z_oamd_empresaformapagamento.dataconsulta) :: TIMESTAMP WITHOUT TIME ZONE -
                                         z_oamd_empresaformapagamento.dataultimoacesso)) / (60) :: DOUBLE PRECISION) /
            (60) :: DOUBLE PRECISION) >= (25) :: DOUBLE PRECISION) AND (((date_part('epoch' :: TEXT, (
      (z_oamd_empresaformapagamento.dataconsulta) :: TIMESTAMP WITHOUT TIME ZONE -
      z_oamd_empresaformapagamento.dataultimoacesso)) / (60) :: DOUBLE PRECISION) / (60) :: DOUBLE PRECISION) <=
                                                                        (48) :: DOUBLE PRECISION))
      THEN '04 - 25-48 horas sem acesso ' :: TEXT
    ELSE '05- 49 horas acima' :: TEXT END                                            AS agrup_ua_faixa_acesso_horas,
    CASE WHEN (z_oamd_empresaformapagamento.clientesativos < 201)
      THEN '01 - FAIXA1_350' :: TEXT
    WHEN ((200 < z_oamd_empresaformapagamento.clientesativos) AND (z_oamd_empresaformapagamento.clientesativos < 501))
      THEN '02 - FAIXA2_500' :: TEXT
    WHEN ((500 < z_oamd_empresaformapagamento.clientesativos) AND (z_oamd_empresaformapagamento.clientesativos < 801))
      THEN '03 - FAIXA3_750' :: TEXT
    WHEN (z_oamd_empresaformapagamento.clientesativos > 800)
      THEN '04 - FAIXA4_1100' :: TEXT
    ELSE NULL :: TEXT END                                                            AS agrup_faixa_clientes_ativos,
    CASE WHEN (z_oamd_empresaformapagamento.dataexpiracao <= now())
      THEN 'BLOQUEADO' :: TEXT
    WHEN (z_oamd_empresaformapagamento.dataexpiracao > now())
      THEN 'VAI BLOQUEAR' :: TEXT
    ELSE '_OK' :: TEXT END                                                           AS expirado,
    date_part('month' :: TEXT, z_oamd_empresaformapagamento.dataexpiracaocreditodcc) AS dcc_dt_exp_mes,
    date_part('year' :: TEXT, z_oamd_empresaformapagamento.dataexpiracaocreditodcc)  AS dcc_dt_exp_ano,
    ((date_part('epoch' :: TEXT,
                age(now(), (z_oamd_empresaformapagamento.dataultimoacesso) :: TIMESTAMP WITH TIME ZONE)) /
      (86400) :: DOUBLE PRECISION)) :: INTEGER                                       AS ultimoacessosistema_dias,
    z_oamd_empresaformapagamento.ultimolancamento                                    AS ultimolancamento_data,
    ((date_part('epoch' :: TEXT,
                age(now(), (z_oamd_empresaformapagamento.ultimolancamento) :: TIMESTAMP WITH TIME ZONE)) /
      (86400) :: DOUBLE PRECISION)) :: INTEGER                                       AS ultimolancamento_dias,
    CASE WHEN (((date_part('epoch' :: TEXT,
                           age(now(), (z_oamd_empresaformapagamento.ultimolancamento) :: TIMESTAMP WITH TIME ZONE)) /
                 (86400) :: DOUBLE PRECISION)) :: INTEGER < 0)
      THEN 'Lançamento Futuro' :: TEXT
    WHEN ((0 <= ((date_part('epoch' :: TEXT,
                            age(now(), (z_oamd_empresaformapagamento.ultimolancamento) :: TIMESTAMP WITH TIME ZONE)) /
                  (86400) :: DOUBLE PRECISION)) :: INTEGER) AND (((date_part('epoch' :: TEXT, age(now(),
                                                                                                  (z_oamd_empresaformapagamento.ultimolancamento) :: TIMESTAMP WITH TIME ZONE))
                                                                   / (86400) :: DOUBLE PRECISION)) :: INTEGER <= 3))
      THEN '01- 0-3 dias' :: TEXT
    WHEN ((4 <= ((date_part('epoch' :: TEXT,
                            age(now(), (z_oamd_empresaformapagamento.ultimolancamento) :: TIMESTAMP WITH TIME ZONE)) /
                  (86400) :: DOUBLE PRECISION)) :: INTEGER) AND (((date_part('epoch' :: TEXT, age(now(),
                                                                                                  (z_oamd_empresaformapagamento.ultimolancamento) :: TIMESTAMP WITH TIME ZONE))
                                                                   / (86400) :: DOUBLE PRECISION)) :: INTEGER <= 10))
      THEN '02- 4-10 dias' :: TEXT
    WHEN ((11 <= ((date_part('epoch' :: TEXT,
                             age(now(), (z_oamd_empresaformapagamento.ultimolancamento) :: TIMESTAMP WITH TIME ZONE)) /
                   (86400) :: DOUBLE PRECISION)) :: INTEGER) AND (((date_part('epoch' :: TEXT, age(now(),
                                                                                                   (z_oamd_empresaformapagamento.ultimolancamento) :: TIMESTAMP WITH TIME ZONE))
                                                                    / (86400) :: DOUBLE PRECISION)) :: INTEGER <= 20))
      THEN '03- 11-20 dias' :: TEXT
    WHEN (20 <= ((date_part('epoch' :: TEXT,
                            age(now(), (z_oamd_empresaformapagamento.ultimolancamento) :: TIMESTAMP WITH TIME ZONE)) /
                  (86400) :: DOUBLE PRECISION)) :: INTEGER)
      THEN '04- 21 a mais' :: TEXT
    ELSE NULL :: TEXT END                                                            AS agrup_ultimolancamento_status,
    z_oamd_empresaformapagamento.dataultimoacesso,
    date_part('year' :: TEXT, z_oamd_empresaformapagamento.dataultimoacesso)         AS ua_ano,
    date_part('month' :: TEXT, z_oamd_empresaformapagamento.dataultimoacesso)        AS ua_mes,
    CASE WHEN (0 > ((date_part('epoch' :: TEXT,
                               age(now(), (z_oamd_empresaformapagamento.dataultimoacesso) :: TIMESTAMP WITH TIME ZONE))
                     / (86400) :: DOUBLE PRECISION)) :: INTEGER)
      THEN '01- 0-3 dias' :: TEXT
    WHEN ((0 <= ((date_part('epoch' :: TEXT,
                            age(now(), (z_oamd_empresaformapagamento.dataultimoacesso) :: TIMESTAMP WITH TIME ZONE)) /
                  (86400) :: DOUBLE PRECISION)) :: INTEGER) AND (((date_part('epoch' :: TEXT, age(now(),
                                                                                                  (z_oamd_empresaformapagamento.dataultimoacesso) :: TIMESTAMP WITH TIME ZONE))
                                                                   / (86400) :: DOUBLE PRECISION)) :: INTEGER <= 3))
      THEN '01- 0-3 dias' :: TEXT
    WHEN ((4 <= ((date_part('epoch' :: TEXT,
                            age(now(), (z_oamd_empresaformapagamento.dataultimoacesso) :: TIMESTAMP WITH TIME ZONE)) /
                  (86400) :: DOUBLE PRECISION)) :: INTEGER) AND (((date_part('epoch' :: TEXT, age(now(),
                                                                                                  (z_oamd_empresaformapagamento.dataultimoacesso) :: TIMESTAMP WITH TIME ZONE))
                                                                   / (86400) :: DOUBLE PRECISION)) :: INTEGER <= 10))
      THEN '02- 4-10 dias' :: TEXT
    WHEN ((11 <= ((date_part('epoch' :: TEXT,
                             age(now(), (z_oamd_empresaformapagamento.dataultimoacesso) :: TIMESTAMP WITH TIME ZONE)) /
                   (86400) :: DOUBLE PRECISION)) :: INTEGER) AND (((date_part('epoch' :: TEXT, age(now(),
                                                                                                   (z_oamd_empresaformapagamento.dataultimoacesso) :: TIMESTAMP WITH TIME ZONE))
                                                                    / (86400) :: DOUBLE PRECISION)) :: INTEGER <= 20))
      THEN '03- 11-20 dias' :: TEXT
    WHEN (20 <= ((date_part('epoch' :: TEXT,
                            age(now(), (z_oamd_empresaformapagamento.dataultimoacesso) :: TIMESTAMP WITH TIME ZONE)) /
                  (86400) :: DOUBLE PRECISION)) :: INTEGER)
      THEN '04- 21 acima' :: TEXT
    ELSE NULL :: TEXT END                                                            AS agrup_ultimoacessosistema_status,
    CASE WHEN (((0) :: NUMERIC < z_oamd_empresaformapagamento.valorfaturado) AND
               (z_oamd_empresaformapagamento.valorfaturado < (51000) :: NUMERIC))
      THEN '01- 0 a 50' :: TEXT
    WHEN (((51000) :: NUMERIC <= z_oamd_empresaformapagamento.valorfaturado) AND
          (z_oamd_empresaformapagamento.valorfaturado < (81000) :: NUMERIC))
      THEN '02- 51 a 80' :: TEXT
    WHEN (((81000) :: NUMERIC <= z_oamd_empresaformapagamento.valorfaturado) AND
          (z_oamd_empresaformapagamento.valorfaturado < (101000) :: NUMERIC))
      THEN '03- 81 a 100' :: TEXT
    WHEN (((101000) :: NUMERIC <= z_oamd_empresaformapagamento.valorfaturado) AND
          (z_oamd_empresaformapagamento.valorfaturado < (151000) :: NUMERIC))
      THEN '04- 101 a 150' :: TEXT
    WHEN ((151000) :: NUMERIC <= z_oamd_empresaformapagamento.valorfaturado)
      THEN '05- 151 acima' :: TEXT
    ELSE '06- SEM FAIXA' :: TEXT END                                                 AS agrup_fatur_faixa,
    date_part('year' :: TEXT, z_oamd_empresaformapagamento.dataconsulta)             AS consulta_ano,
    date_part('month' :: TEXT, z_oamd_empresaformapagamento.dataconsulta)            AS consulta_mes
  FROM z_oamd_empresaformapagamento z_oamd_empresaformapagamento;

