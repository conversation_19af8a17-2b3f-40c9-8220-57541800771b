FROM registry.gitlab.com/plataformazw/docker-pacto/tomcat:8

ENV OAMD_URL=${OAMD_URL:-"************************************"}
ENV DISCOVERY_URL=${DISCOVERY_URL:-"http://10.10.1.7:8080"}
ENV AUTH_SECRET_PATH=${AUTH_SECRET_PATH:-"/keys/auth-secret"}
ENV AUTH_SECRET_PERSONA_PATH=${AUTH_SECRET_PERSONA_PATH:-"/keys/auth-secret-persona"}
ENV MY_URL_UP_BASE=${MY_URL_UP_BASE:-"https://app.pactosolucoes.com.br/ucp"}
ENV TELA_APOIO=${TELA_APOIO:-"https://app.pactosolucoes.com.br/telaapoio"}

ENV HEALTHCHECK_KEY=${HEALTHCHECK_KEY:-"teste"}
ENV TZ=America/Sao_Paulo

RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
COPY ./target/NewOAMD /usr/local/tomcat/webapps/NewOAMD
COPY src/main/resources/br/com/pacto/util/resources/OpcoesGlobais.properties /app/OpcoesGlobais.properties
COPY target/NewOAMD/WEB-INF/classes/br/com/pacto/util/resources/OpcoesGlobais.properties /app/OpcoesGlobais.build.properties

COPY docker/bin/*.sh /bin/
COPY docker/keys/* /keys/
RUN chmod +x /bin/*.sh

HEALTHCHECK --interval=1m --timeout=1m --retries=3 --start-period=5m \
  CMD curl -f http://localhost:8080/NewOAMD/prest/health/$HEALTHCHECK_KEY
ENTRYPOINT ["bash", "/bin/entrypoint.sh"]
