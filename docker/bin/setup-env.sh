#!/usr/bin/env bash

set -e

file_properties=/usr/local/tomcat/webapps/NewOAMD/WEB-INF/classes/br/com/pacto/util/resources/OpcoesGlobais.properties
original_file_properties=/app/OpcoesGlobais.properties
build_file_properties=/app/OpcoesGlobais.build.properties
file_db=/usr/local/tomcat/webapps/NewOAMD/WEB-INF/classes/br/com/pacto/base/oamd/cfgBD.xml
cp $build_file_properties $file_properties

if [ -f "$file_properties" ]; then
    sed -i "s~discoveryUrls.*=.*~discoveryUrls=$DISCOVERY_URL~g" $file_properties
    sed -i "s~myUpUrlBase.*=.*~myUpUrlBase=$MY_URL_UP_BASE~g" $file_properties
    sed -i "s~AUTH_SECRET_PATH.*=.*~AUTH_SECRET_PATH=$AUTH_SECRET_PATH~g" $file_properties
    sed -i "s~AUTH_SECRET_PERSONA_PATH.*=.*~AUTH_SECRET_PERSONA_PATH=$AUTH_SECRET_PERSONA_PATH~g" $file_properties
    sed -i "s~telaApoio.*=.*~telaApoio=$TELA_APOIO~g" $file_properties
    sed -i "s~<url-oamd>.*</url-oamd>~<url-oamd>$OAMD_URL</url-oamd>~g" $file_db

    echo "Application properties:"
    cat $file_properties
    exit 0
else
    echo "Setup environment failed"
    echo "File $file_properties not found"
    exit 0
fi
