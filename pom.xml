<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>br.com.pacto.treino</groupId>
    <artifactId>NewOAMD</artifactId>
    <packaging>war</packaging>
    <version>1.0.673</version><!--USED-BY-BUMPVERION-->
    <name>NewOAMD</name>
    <url>http://www.pactosolucoes.com.br</url>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.1.0.RELEASE</version>
        <relativePath/>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <contexto>oamd</contexto>
        <outputDirectory>D:/suporte/3Versoes/ZillyonWeb/deploy/war/RC</outputDirectory>
        <hostDeploy>localhost</hostDeploy>
        <portDeploy>8085</portDeploy>
        <deployUser>admin</deployUser>
        <deployPwd></deployPwd>
        <sshHost>********</sshHost>
        <sshPort>22</sshPort>
        <sshUser>root</sshUser>
        <sshPwd></sshPwd>
        <keyfile>/root/.ssh/id_rsa</keyfile>
        <hostBD>********</hostBD>
        <nomeBD>OAMD</nomeBD>
        <portBD>5432</portBD>
        <userBD>postgres</userBD>
        <pwdBD>pactodb</pwdBD>
        <urlFeedPacto>http://www.pacto.vc/feed/</urlFeedPacto>
        <urlAdmApp>https://app.pactosolucoes.com.br/AdmAPP</urlAdmApp>
        <urlImportAdmWS>http://app.pactosolucoes.com.br/app/AdmWS?wsdl</urlImportAdmWS>
        <urlImportZW>http://app.pactosolucoes.com.br/app/IntegracaoCadastrosWS?wsdl</urlImportZW>
        <keyUnitTests>in-memory</keyUnitTests>
        <instanciasPropagar></instanciasPropagar>
        <urlObterBanners>http://app.pactosolucoes.com.br/app/UpdateServlet</urlObterBanners>
        <urlPushWeb></urlPushWeb>
        <MY_URL_UP_BASE>https://app.pactosolucoes.com.br/ucp</MY_URL_UP_BASE>
        <TELA_APOIO>https://app.pactosolucoes.com.br/apoio</TELA_APOIO>
        <URL_GAME>http://homologacao.pactosolucoes.com.br:9982/GameofResults</URL_GAME>
        <APENAS_ACESSO_MULTI_EMPRESAS>false</APENAS_ACESSO_MULTI_EMPRESAS>
        <imgLogin></imgLogin>
        <SERVIDOR_MEMCACHED>DISABLED</SERVIDOR_MEMCACHED>
        <urlJenkinsMonitor>http://*********:10091</urlJenkinsMonitor>
        <urlPush>http://push.pactosolucoes.com.br:8085/oamd-ws</urlPush>
		<TIPO_MIDIA>AWS_S3</TIPO_MIDIA>
        <URL_FOTOS_NUVEM>https://s3-sa-east-1.amazonaws.com/prod-zwphotos</URL_FOTOS_NUVEM>
        <URL_ARQUIVOS_NUVEM>https://s3-sa-east-1.amazonaws.com/prod-zwphotos</URL_ARQUIVOS_NUVEM>
        <URL_API_ZW>http://app.pactosolucoes.com.br/api/prest</URL_API_ZW>
        <DIRETORIO_ARQUIVOS>/opt/ZW_ARQ/</DIRETORIO_ARQUIVOS>
        <INFRA_HOMOLOGACAO_SITE></INFRA_HOMOLOGACAO_SITE>
        <DISCOVERY_URL>https://discovery.ms.pactosolucoes.com.br</DISCOVERY_URL>
        <spring.version>4.0.3.RELEASE</spring.version>
        <jsch.version>0.1.55</jsch.version>
        <url.wsfinanceiro>http://suporte.pactosolucoes.com.br:8083/wsfinanceiro/</url.wsfinanceiro>
        <url.integrador.financeiro>https://fin.pactosolucoes.com.br/ifinan</url.integrador.financeiro>
        <REDIRECIONAR_CONSULTORES>true</REDIRECIONAR_CONSULTORES>
        <enableCaptcha>false</enableCaptcha>
        <ipBlockIgnoreCaptcha></ipBlockIgnoreCaptcha>
        <URL_GLAPI></URL_GLAPI>
        <AES_SECRET_KEY>icbvhmZoTFDP3TmhP3jKtain97ED21kWBwa7X0NxP7c=</AES_SECRET_KEY>
        <AWS_REGION>us-east-1</AWS_REGION>
    </properties>

    <repositories>
        <repository>
            <id>prime-repo</id>
            <name>PrimeFaces Maven Repository</name>
            <url>https://repository.primefaces.org</url>
            <layout>default</layout>
        </repository>
        <repository>
            <id>nexusLocal</id>
            <name>Pacto Maven Repository</name>
            <url>https://pow.webhop.net/nexus/content/groups/public</url>
            <layout>default</layout>
        </repository>
        <repository>
            <id>nexusSolutioIn</id>
            <name>Solutio-in Maven Repository</name>
            <url>https://pow.webhop.net/nexus/content/repositories/solutio</url>
            <layout>default</layout>
        </repository>
    </repositories>

    <distributionManagement>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Internal Snapshots</name>
            <url>http://localhost/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
        <repository>
            <id>releases</id>
            <name>Internal Releases</name>
            <url>http://localhost/nexus/content/repositories/releases</url>
        </repository>
    </distributionManagement>

    <profiles>
        <profile>
            <id>tomcat7</id>
            <properties>
                <profile>tomcat7</profile>
                <instanciasPropagar>localhost:8084</instanciasPropagar>
                <hostBD>localhost</hostBD>
                <portBD>5432</portBD>
                <urlPush>http://localhost:8087/oamd-ws</urlPush>
                <INFRA_HOMOLOGACAO_SITE>HOMOLOGACAO</INFRA_HOMOLOGACAO_SITE>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                    <version>1.2.17</version>
                </dependency>
                <dependency>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                    <version>1.7.5</version>
                    <scope>test</scope>
                </dependency>
                <dependency>
                    <groupId>com.sun.xml.ws</groupId>
                    <artifactId>jaxws-rt</artifactId>
                    <version>2.2.8</version>
                </dependency>
                <dependency>
                    <groupId>javax.transaction</groupId>
                    <artifactId>jta</artifactId>
                    <version>1.1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>transactions</artifactId>
                    <version>3.9.1</version>
                </dependency>
                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>transactions-api</artifactId>
                    <version>3.9.1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>transactions-hibernate3</artifactId>
                    <version>3.9.1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>transactions-jdbc</artifactId>
                    <version>3.9.1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>transactions-jms</artifactId>
                    <version>3.9.1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>transactions-jta</artifactId>
                    <version>3.9.1</version>
                </dependency>

                <dependency>
                    <groupId>org.atmosphere</groupId>
                    <artifactId>atmosphere-compat-jbossweb</artifactId>
                    <version>1.1.0.RC4</version>
                </dependency>


                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>atmosphere-compat-tomcat</artifactId>
                    <version>1.0.0.RC1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>atmosphere-compat-tomcat7</artifactId>
                    <version>1.0.0.RC1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>atmosphere-runtime</artifactId>
                    <version>1.0.0.RC1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>atomikos-util</artifactId>
                    <version>3.9.1</version>
                </dependency>
                <dependency>
                    <groupId>org.json</groupId>
                    <artifactId>json</artifactId>
                    <version>20140107</version>
                </dependency>
                <!--AMAZON AWS SDK END -->



                <!-- Swagger  -->
                <dependency>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger2</artifactId>
                    <version>2.6.1</version>
                </dependency>
                <dependency>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger-ui</artifactId>
                    <version>2.6.1</version>
                </dependency>
                <dependency>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-annotations</artifactId>
                    <version>1.5.20</version>
                </dependency>
                <!-- Swagger end -->
            </dependencies>
            <build>
                <plugins>

                </plugins>
            </build>
        </profile>

        <profile>
            <id>glassfish3</id>
            <properties>
                <profile>glassfish3</profile>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>atmosphere-compat-jbossweb</artifactId>
                    <version>1.1.0.RC4</version>
                </dependency>
                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>atmosphere-compat-tomcat</artifactId>
                    <version>1.1.0.RC4</version>
                </dependency>
                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>atmosphere-compat-tomcat7</artifactId>
                    <version>1.1.0.RC4</version>
                </dependency>
                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>atmosphere-runtime</artifactId>
                    <version>1.1.0.RC4</version>
                </dependency>
            </dependencies>
        </profile>

        <profile>
            <id>deployment</id>
            <properties>
                <URL_GLAPI>https://glapi.pactosolucoes.com.br/api</URL_GLAPI>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <inherited>true</inherited>
                        <dependencies>
                            <dependency>
                                <groupId>com.oopsconsultancy</groupId>
                                <artifactId>xmltask</artifactId>
                                <version>1.16</version>
                            </dependency>
                            <dependency>
                                <groupId>ant</groupId>
                                <artifactId>ant-jsch</artifactId>
                                <version>1.6.5</version>
                            </dependency>
                            <dependency>
                                <groupId>com.jcraft</groupId>
                                <artifactId>jsch</artifactId>
                                <version>${jsch.version}</version>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>executeShellRemote</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target>
                                        <scp trust="true" file="${basedir}/target/${project.name}.war"
                                             port="${sshPort}"
                                             passphrase="${sshPwd}"
                                             keyfile="${keyfile}"
                                             verbose="true"
                                             todir="${sshUser}@${sshHost}:/opt/${contexto}.war"/>

                                        <sshexec port="${sshPort}" verbose="false"
                                                 trust="true" host="${sshHost}"
                                                 username="${sshUser}"
                                                 passphrase="${sshPwd}"
                                                 keyfile="${keyfile}"
                                                 command="sh /opt/redeploy.sh ${contexto}"/>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>desenv</id>
            <properties>
                <nomeBD>OAMD</nomeBD>
                <hostBD>localhost</hostBD>
                <portBD>5432</portBD>
                <userBD>postgres</userBD>
                <pwdBD>pactodb</pwdBD>
                <AUTH_SECRET_PATH>/opt/council/elrond.txt</AUTH_SECRET_PATH>
                <DISCOVERY_URL>http://localhost:8087</DISCOVERY_URL>
                <urlImportAdmWS>http://localhost:8200/ZillyonWeb/AdmWS?wsdl</urlImportAdmWS>
                <urlImportZW>http://localhost:8200/ZillyonWeb/IntegracaoCadastrosWS?wsdl</urlImportZW>
                <url.wsfinanceiro>http://localhost:8787/wsfinanceiro/</url.wsfinanceiro>
                <url.integrador.financeiro>http://localhost:8300/ifinan</url.integrador.financeiro>
                <TELA_APOIO>http://localhost:8086/tela-apoio</TELA_APOIO>
                <REDIRECIONAR_CONSULTORES>true</REDIRECIONAR_CONSULTORES>
                <GITLAB_DEVELOP_TOKEN></GITLAB_DEVELOP_TOKEN>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <inherited>true</inherited>
                        <dependencies>
                            <dependency>
                                <groupId>com.oopsconsultancy</groupId>
                                <artifactId>xmltask</artifactId>
                                <version>1.16</version>
                            </dependency>
                            <dependency>
                                <groupId>ant</groupId>
                                <artifactId>ant-jsch</artifactId>
                                <version>1.6.5</version>
                            </dependency>
                            <dependency>
                                <groupId>com.jcraft</groupId>
                                <artifactId>jsch</artifactId>
                                <version>${jsch.version}</version>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>updatePages</id>
                                <phase>validate</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target>
                                        <echo message="Atualizando XHTML, CSS e JS..."/>
                                        <copydir src="${basedir}/src/main/webapp" dest="${basedir}/target/${project.name}"
                                                 includes="**/*.xhtml,**/*.css,**/*.js"/>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>docker</id>
            <properties>
                <contexto>NewOAMD</contexto>
                <hostBD>postgres</hostBD>
                <portBD>5432</portBD>
                <userBD>postgres</userBD>
                <pwdBD>pactodb</pwdBD>
                <AUTH_SECRET_PATH>/keys/auth-secret</AUTH_SECRET_PATH>
                <AUTH_SECRET_PERSONA_PATH>/keys/auth-secret-persona</AUTH_SECRET_PERSONA_PATH>
                <DISCOVERY_URL>https://discovery.ms.pactosolucoes.com.br</DISCOVERY_URL>
                <urlImportAdmWS>http://app.pactosolucoes.com.br/app/AdmWS?wsdl</urlImportAdmWS>
                <urlImportZW>http://app.pactosolucoes.com.br/app/IntegracaoCadastrosWS?wsdl</urlImportZW>
            </properties>
        </profile>

        <profile>
            <id>local</id>
            <properties>
                <AUTH_SECRET_PATH>${project.basedir}/docker/keys/auth-secret</AUTH_SECRET_PATH>
                <AUTH_SECRET_PERSONA_PATH>${project.basedir}/docker/keys/auth-secret-persona</AUTH_SECRET_PERSONA_PATH>
                <DISCOVERY_URL>http://localhost:8101</DISCOVERY_URL>
                <urlImportAdmWS>http://localhost:8200/ZillyonWeb/AdmWS?wsdl</urlImportAdmWS>
                <urlImportZW>http://localhost:8200/ZillyonWeb/IntegracaoCadastrosWS?wsdl</urlImportZW>
            </properties>
        </profile>
    </profiles>


    <dependencies>

        <!-- https://mvnrepository.com/artifact/com.auth0/java-jwt -->
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>3.8.3</version>
        </dependency>

        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
            <version>1.9.13</version>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20140107</version>
        </dependency>

        <dependency>
            <groupId>net.spy</groupId>
            <artifactId>spymemcached</artifactId>
            <version>2.12.1</version>
        </dependency>

        <!-- AMAZON AWS SDK -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-secretsmanager</artifactId>
            <version>1.11.728</version>
        </dependency>

        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.11.591</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-ec2</artifactId>
            <version>1.11.591</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>antlr</artifactId>
            <version>2.7.6</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>aopalliance</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>asm</artifactId>
            <version>1.5.3</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>asm-attrs</artifactId>
            <version>1.5.3</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>bootstrap</artifactId>
            <version>1.0.10</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>cglib</artifactId>
            <version>2.1.3</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.1</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.4</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.6</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>cron4j</artifactId>
            <version>2.2.5</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>ehcache</artifactId>
            <version>1.2.3</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>guava</artifactId>
            <version>16.0.1</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>hibernate-entitymanager</artifactId>
            <version>3.3.2.GA</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>hibernate-jpa-2.0-api</artifactId>
            <version>1.0.1.Final</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>hibernate3</artifactId>
            <version>3.6.10.Final</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>hsqldb</artifactId>
            <version>1.8.0.10</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>iText</artifactId>
            <version>2.1.7</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.9.7</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.9.7</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.9.7</version>
        </dependency>

        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>3.18.2-GA</version>
        </dependency>
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>${jsch.version}</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
            <version>1.7.5</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>jdom</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.sun.faces</groupId>
            <artifactId>jsf-api</artifactId>
            <version>2.2.5</version>
        </dependency>

        <dependency>
            <groupId>com.sun.faces</groupId>
            <artifactId>jsf-impl</artifactId>
            <version>2.2.5</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>jstl</artifactId>
            <version>1.2</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>junit</artifactId>
            <version>4.10</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.17</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>primefaces</artifactId>
            <version>4.0</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>qrgen</artifactId>
            <version>1.3</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>rome</artifactId>
            <version>0.9</version>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.12</version>
        </dependency>


        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>zxing-core</artifactId>
            <version>1.7</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>zxing-j2se</artifactId>
            <version>1.7</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>prettyfaces-jsf2</artifactId>
            <version>3.3.3</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-orm</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.3</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>poi</artifactId>
            <version>3.8-20120326</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>hibernate-tools</artifactId>
            <version>3.2.0</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>jdbc2_0-stdext</artifactId>
            <version>1.2.1</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>lambdaj-2.4-with-dependencies</artifactId>
            <version>2.4.0</version>
            <classifier>dependencies</classifier>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>postgresql-8.4-701.jdbc3</artifactId>
            <version>8.4.701</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>reflections-0.9.9-RC1-uberjar</artifactId>
            <version>0.9.9</version>
            <classifier>uberjar</classifier>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>standard</artifactId>
            <version>1.1.2</version>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
            <scope>provided</scope>
            <version>2.5</version>
        </dependency>

        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.9.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.12</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.3</version>
        </dependency>
        <dependency>
            <artifactId>joda-time</artifactId>
            <groupId>joda-time</groupId>
            <type>jar</type>
            <version>2.9.2</version>
        </dependency>

        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
            <version>1.4</version>
        </dependency>

        <!-- CAPTURA FOTOS -->
        <dependency>
            <groupId>backport-util-concurrent</groupId>
            <artifactId>backport-util-concurrent</artifactId>
            <version>3.1</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.flex</groupId>
            <artifactId>flex-bootstrap</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.flex</groupId>
            <artifactId>flex-messaging-common</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.flex</groupId>
            <artifactId>flex-messaging-core</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.flex</groupId>
            <artifactId>flex-messaging-opt</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.flex</groupId>
            <artifactId>flex-messaging-proxy</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.flex</groupId>
            <artifactId>flex-messaging-remoting</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>org.simpleframework</groupId>
            <artifactId>simple-xml</artifactId>
            <version>2.7.1</version>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>fluent-hc</artifactId>
            <version>4.3.4</version>
        </dependency>

        <dependency>
            <groupId>com.googlecode.jcsv</groupId>
            <artifactId>jcsv</artifactId>
            <version>1.4.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot</artifactId>
            <version>2.1.4.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.github.seancfoley</groupId>
            <artifactId>ipaddress</artifactId>
            <version>5.3.3</version>
        </dependency>

        <!-- Biblioteca para OAuth 2.0 -->
        <dependency>
            <groupId>com.github.scribejava</groupId>
            <artifactId>scribejava-apis</artifactId>
            <version>8.3.3</version>
        </dependency>

        <!-- Biblioteca JSON para processamento da resposta do Google -->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.10.1</version>
        </dependency>

        <!-- Swagger Dependencies -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.6.1</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.6.1</version>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.5.20</version>
        </dependency>
        <!-- Swagger end -->

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.0.2</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.2</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.8</version>
                <inherited>true</inherited>
                <dependencies>
                    <dependency>
                        <groupId>com.oopsconsultancy</groupId>
                        <artifactId>xmltask</artifactId>
                        <version>1.16</version>
                    </dependency>
                    <dependency>
                        <groupId>ant</groupId>
                        <artifactId>ant-jsch</artifactId>
                        <version>1.6.5</version>
                    </dependency>
                    <dependency>
                        <groupId>com.jcraft</groupId>
                        <artifactId>jsch</artifactId>
                        <version>${jsch.version}</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>setProprerties</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <echo message="Setar propriedades da Aplicacao ${basedir}" />

                                <taskdef name="xmltask" classname="com.oopsconsultancy.xmltask.ant.XmlTask"/>

                                <copy failonerror="false" verbose="false" file="${basedir}/src/main/resources/META-INF/persistence-${profile}.xml"
                                      tofile="${basedir}/target/classes/META-INF/persistence.xml">
                                    <filterchain>
                                        <replacetokens>
                                            <token key="HOST_BD" value="${hostBD}"/>
                                            <token key="NOME_BD" value="${nomeBD}"/>
                                            <token key="PORT_BD" value="${portBD}"/>
                                            <token key="USER_BD" value="${userBD}"/>
                                            <token key="PWD_BD" value="${pwdBD}"/>
                                        </replacetokens>
                                    </filterchain>
                                </copy>

                                <copy failonerror="false" verbose="false" file="${basedir}/src/main/webapp/WEB-INF/applicationContext-${profile}.xml"
                                      tofile="${basedir}/target/${project.name}/WEB-INF/applicationContext.xml"/>

                                <copy verbose="false" file="${basedir}/src/main/resources/br/com/pacto/util/resources/OpcoesGlobais.properties"
                                      tofile="${basedir}/target/classes/br/com/pacto/util/resources/OpcoesGlobais-temp.properties">
                                    <filterchain>
                                        <replacetokens>
                                            <token key="URL_FEED" value="${urlFeedPacto}"/>
                                            <token key="URL_ADM_APP" value="${urlAdmApp}"/>
                                            <token key="KEY_TESTS" value="${keyUnitTests}"/>
                                            <token key="URL_BANNERS" value="${urlObterBanners}"/>
                                            <token key="INSTANCIAS_NOTIFICAR" value="${instanciasPropagar}"/>
                                            <token key="MY_URL_UP_BASE" value="${MY_URL_UP_BASE}"/>
                                            <token key="URL_GAME" value="${URL_GAME}"/>
                                            <token key="APENAS_ACESSO_MULTI_EMPRESAS" value="${APENAS_ACESSO_MULTI_EMPRESAS}"/>
                                            <token key="imgLogin" value="${imgLogin}"/>
                                            <token key="urlJenkinsMonitor" value="${urlJenkinsMonitor}"/>
                                            <token key="URL_PUSH_WS" value="${urlPush}"/>
                                            <token key="URL_API_ZW" value="${URL_API_ZW}"/>
                                            <token key="URL_FOTOS_NUVEM" value="${URL_FOTOS_NUVEM}"/>
                                            <token key="URL_ARQUIVOS_NUVEM" value="${URL_ARQUIVOS_NUVEM}"/>
                                            <token key="TIPO_MIDIA" value="${TIPO_MIDIA}"/>
                                            <token key="INFRA_HOMOLOGACAO_SITE" value="${INFRA_HOMOLOGACAO_SITE}"/>
                                            <token key="DIRETORIO_ARQUIVOS" value="${DIRETORIO_ARQUIVOS}"/>
                                            <token key="AUTH_SECRET_PATH" value="${AUTH_SECRET_PATH}"/>
                                            <token key="DISCOVERY_URL" value="${DISCOVERY_URL}"/>
                                            <token key="URL_WSFINANCEIRO" value="${url.wsfinanceiro}"/>
                                            <token key="URL_IFINAN" value="${url.integrador.financeiro}"/>
                                            <token key="TELA_APOIO" value="${TELA_APOIO}"/>
                                            <token key="REDIRECIONAR_CONSULTORES" value="${REDIRECIONAR_CONSULTORES}"/>
                                            <token key="ENABLE_CAPTCHA" value="${enableCaptcha}"/>
                                            <token key="IP_BLOCK_IGNORE" value="${ipBlockIgnoreCaptcha}"/>
                                            <token key="VERSION" value="${project.version}"/>
                                            <token key="OAMD_SECRET" value="${oamd.secret}"/>
                                            <token key="OAMD_MIGRATION_SECRET" value="${oamd.migration.secret}"/>
                                            <token key="URL_GLAPI" value="${URL_GLAPI}"/>
                                            <token key="AES_SECRET_KEY" value="${AES_SECRET_KEY}"/>
                                            <token key="GITLAB_DEVELOP_TOKEN" value="${GITLAB_DEVELOP_TOKEN}"/>
                                            <token key="AWS_REGION" value="${AWS_REGION}"/>
                                        </replacetokens>
                                    </filterchain>
                                </copy>
                                <move verbose="false" file="${basedir}/target/classes/br/com/pacto/util/resources/OpcoesGlobais-temp.properties"
                                      tofile="${basedir}/target/classes/br/com/pacto/util/resources/OpcoesGlobais.properties"/>

                                <xmltask outputter="simple"
                                         source="${basedir}/target/classes/br/com/pacto/base/oamd/cfgBD.xml"
                                         dest="${basedir}/target/classes/br/com/pacto/base/oamd/cfgBD.xml">

                                    <replace path="/aplicacao/bd/nomeBD/text()"
                                             withText="${nomeBD}"/>

                                    <replace path="/aplicacao/bd/servidor/text()"
                                             withText="${hostBD}"/>

                                    <replace path="/aplicacao/bd/url-oamd/text()"
                                             withText="jdbc:postgresql://${hostBD}:${portBD}/${nomeBD}"/>

                                    <replace path="/aplicacao/bd/username/text()"
                                             withText="${userBD}"/>

                                    <replace path="/aplicacao/bd/senha/text()"
                                             withText="${pwdBD}"/>

                                    <replace path="/aplicacao/bd/porta/text()"
                                             withText="${portBD}"/>

                                    <replace path="/aplicacao/bd/ipServidoresMemCached/text()"
                                             withText="${SERVIDOR_MEMCACHED}"/>

                                </xmltask>

                                <taskdef name="xmltask" classname="com.oopsconsultancy.xmltask.ant.XmlTask"/>
                                <xmltask outputter="simple" source="${basedir}/src/main/webapp/WEB-INF/web.xml" dest="${basedir}/target/${project.name}/WEB-INF/web.xml">
                                    <insert path="/web-app/servlet-mapping[last()]" position="after">
                                        <![CDATA[
                                            <context-param>
                                                <param-name>primefaces.PUSH_SERVER_URL</param-name>
                                                <param-value>${urlPushWeb}</param-value>
                                            </context-param>
                                        ]]>
                                    </insert>
                                </xmltask>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>1.0</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <packagingExcludes>
                        WEB-INF/lib/hsqldb*.jar,
                        WEB-INF/lib/junit*.jar,
                        WEB-INF/lib/xalan-2.6.0.jar,
                        WEB-INF/lib/xercesImpl-2.6.2.jar,
                        WEB-INF/lib/xml-apis-1.0.b2.jar,
                        WEB-INF/lib/xmlParserAPIs-2.6.2.jar
                    </packagingExcludes>
                    <webXml>src/main/webapp/WEB-INF/web.xml</webXml>
                    <warSourceExcludes>**/.svn/**</warSourceExcludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.tomcat.maven</groupId>
                <artifactId>tomcat7-maven-plugin</artifactId>
                <version>2.1</version>
                <configuration>
                    <url>http://${hostDeploy}:${portDeploy}/manager/text</url>
                    <username>${deployUser}</username>
                    <password>${deployPwd}</password>
                    <path>/${contexto}</path>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.jvnet.jax-ws-commons</groupId>
                <artifactId>jaxws-maven-plugin</artifactId>
                <version>2.2</version>

                <executions>
                    <execution>
                        <id>generate-ws-oamd</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>wsgen</goal>
                        </goals>
                        <configuration>
                            <sei>br.com.pacto.ws.OamdWS</sei>
                            <genWsdl>true</genWsdl>
                            <keep>false</keep>
                            <source>1.6</source>
                            <target>1.6</target>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-client-AdmWS</id>
                        <goals>
                            <goal>wsimport</goal>
                        </goals>
                        <configuration>
                            <wsdlUrls>
                                <wsdlUrl>${urlImportAdmWS}</wsdlUrl>
                            </wsdlUrls>
                            <packageName>servicos.integracao.adm.client</packageName>
                            <target>2.1</target>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-client-AdmAppWS</id>
                        <goals>
                            <goal>wsimport</goal>
                        </goals>
                        <configuration>
                            <wsdlUrls>
                                <wsdlUrl>${urlAdmApp}/AdmAppWS?wsdl</wsdlUrl>
                            </wsdlUrls>
                            <packageName>servicos.integracao.admapp.client</packageName>
                            <target>2.1</target>
                        </configuration>
                    </execution>

                    <execution>
                        <id>generate-client-IntegracaoCadastrosWS</id>
                        <goals>
                            <goal>wsimport</goal>
                        </goals>
                        <configuration>
                            <wsdlUrls>
                                <wsdlUrl>${urlImportZW}</wsdlUrl>
                            </wsdlUrls>
                            <packageName>servicos.integracao.zw.client</packageName>
                            <target>2.1</target>
                        </configuration>
                    </execution>
                </executions>

                <dependencies>
                    <dependency>
                        <groupId>com.sun.xml.ws</groupId>
                        <artifactId>jaxws-ri</artifactId>
                        <version>2.2.6</version>
                        <type>pom</type>
                    </dependency>

                    <dependency>
                        <groupId>com.sun.xml.ws</groupId>
                        <artifactId>jaxws-tools</artifactId>
                        <version>2.2.10</version>
                    </dependency>

                    <dependency>
                        <groupId>javax.activation</groupId>
                        <artifactId>activation</artifactId>
                        <version>1.1</version>
                    </dependency>
                    <dependency>
                        <groupId>commons-beanutils</groupId>
                        <artifactId>commons-beanutils</artifactId>
                        <version>1.7.0</version>
                    </dependency>
                    <dependency>
                        <groupId>backport-util-concurrent</groupId>
                        <artifactId>backport-util-concurrent</artifactId>
                        <version>3.1</version>
                    </dependency>
                    <dependency>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpmime</artifactId>
                        <version>4.3.1</version>
                    </dependency>

                    <dependency>
                        <groupId>br.com.pacto.lib.sms</groupId>
                        <artifactId>sms</artifactId>
                        <version>1.0</version>
                    </dependency>

                </dependencies>

            </plugin>

        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>
</project>
