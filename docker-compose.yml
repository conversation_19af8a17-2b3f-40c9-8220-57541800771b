version: '3.7'
services:
  zw:
    image: registry.gitlab.com/plataformazw/zw/tomcat:master
    ports:
      - 8081:8080
      - 9001:9000
    environment:
      DISCOVERY_URL: http://*********:8080
    depends_on:
      - postgres
  oamd:
    build: .
    image: registry.gitlab.com/plataformazw/oamd/tomcat:master
    ports:
      - 8083:8080
      - 9003:9000
    depends_on:
      - postgres
      - zw
  autenticacao:
    image: registry.gitlab.com/plataformazw/autenticacao:master
    ports:
      - 8086:8080
      - 9006:9000
    environment:
      DISCOVERY_URL: http://*********:8080
  discovery:
    image: registry.gitlab.com/plataformazw/discovery-urls:master
    ports:
      - 8087:8080
      - 9007:9000
    depends_on:
      - postgres
  postgres:
    image: registry.gitlab.com/plataformazw/docker-pacto/postgres:9.4
    ports:
      - "5432:5432"
    environment:
      INIT_DB: teste
      RESTORE_DB: "false"
      IP_HOST: postgres
      URL_ZW: http://zw:8080/ZillyonWeb
      URL_TREINO: http://treino:8080/TreinoWeb
      URL_OAMD: http://oamd:8080/NewOAMD
      URL_ZW_API: http://api:8080/api
      URL_LOGIN: http://login:8080/LoginApp
      URL_AUTENTICACAO: http://autenticacao:8080
      URL_ARAGORN: http://aragorn:8080
      MY_URL_UP_BASE: http://ucp:8080
