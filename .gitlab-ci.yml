stages:
  - build  
  - finish

docker-tomcat-build:
  stage: build
  tags:
    - shell
  variables:
    TAG: registry.gitlab.com/plataformazw/oamd/tomcat:$CI_COMMIT_REF_SLUG
  only:
    - master
    - merge_requests
    - tags
  except:
    changes:
      - .bumpversion.cfg
  script:
    - docker login -u $CI_USER -p $CI_USER_PASS $CI_REGISTRY
    - mvn -T 1C clean package -P tomcat7,docker
    - echo $TAG
    - docker build --cache-from $TAG -t $TAG .
    - docker push $TAG

tag-version:
  image: registry.gitlab.com/plataformazw/tag-versions:master
  tags:
    - docker
  stage: finish
  when: on_success
  only:
    - master
  except:
    changes:
      - .bumpversion.cfg
  script:
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - git config --global user.name "Pipeline"
    - git config --global user.email "<EMAIL>"
    - git tag -l | xargs git tag -d
    - bumpversion  patch
    - git remote show origin
    - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
    - git remote show origin
    - git tag -f latest -m "Deploy tag"
    - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git push origin HEAD:$CI_COMMIT_REF_NAME
    - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git push -f --tags
